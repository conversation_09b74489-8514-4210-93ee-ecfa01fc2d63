# 客资反馈功能实现说明

## 功能概述
在门店工作台-预约到店页面的客户预约信息块左下角"..."隐藏下拉菜单中新增"客资反馈"菜单项，点击后弹出二维码供用户扫描填写反馈。

## 显示条件
客资反馈菜单仅在以下订单状态时显示：
- 已到店 (order_status = 3)
- 待结算 (order_status = 4)  
- 已完成 (order_status = 5)
- 第三方结算 (order_status = 6)
- 售后服务 (order_status = 8)

## 实现方案

### 后端修改
- 文件：`common/services/order/OrderHeaderService.php`
- 方法：`storeSearch()`
- 变更：在 `setExtendAttrs` 中添加了 `'customer.age_bracket' => 'cus_age_bracket'` 字段映射

### 前端修改
- 文件：`manageSystem/src/views/storeWorkbench/makeAppointment/components/more.vue`
  - 新增"客资反馈"菜单项
  - 添加状态判断逻辑
  
- 文件：`manageSystem/src/views/storeWorkbench/makeAppointment/memberCard.vue`
  - 传递 `showFeedback` 事件

- 文件：`manageSystem/src/views/storeWorkbench/makeAppointment/makeAppointment.vue`
  - 添加客资反馈弹窗
  - 实现URL生成逻辑
  - 添加年龄段映射函数

## URL生成逻辑
基础URL：`https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnjdvWVhs5rbVugyoTNporwc`

参数：
- `prefill_客户姓名`: 预填充客户姓名
- `prefill_订单ID`: 预填充订单ID
- `hide_订单ID=1`: 隐藏订单ID字段
- `prefill_年龄段`: 预填充年龄段（如果存在且不是"其他"）

## 年龄段映射
```
1: '18岁以下'
2: '18-19岁'
3: '20-23岁'
4: '24-30岁'
5: '31-35岁'
6: '36-40岁'
7: '41-45岁'
8: '46-50岁'
9: '51-55岁'
10: '56-59岁'
11: '60岁以上'
```

## 测试示例
- 基础URL（无年龄段）：客户"毛琴"，订单ID 94495
- 完整URL（有年龄段）：客户"杨美"，订单ID 71747，年龄段"24-30岁"

## 注意事项
1. 中文字符会自动进行URL编码
2. 年龄段为0（其他）时不会添加年龄段参数
3. 年龄段信息可能为空，此时只包含基础信息
4. 弹窗样式与现有的"扫码调店"功能保持一致