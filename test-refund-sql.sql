-- 测试GROUP_CONCAT在二次分组时的行为

-- 创建测试数据表
DROP TABLE IF EXISTS test_refund_groupby;
CREATE TEMPORARY TABLE test_refund_groupby (
    teacher_id INT,
    teacher_name VARCHAR(50),
    refund_data TEXT
);

-- 插入测试数据（模拟dataQuery的分组结果）
INSERT INTO test_refund_groupby VALUES 
(1, '张老师', '101:1000,102:500'),  -- 张老师有两笔退款
(2, '李老师', '103:2000'),          -- 李老师有一笔退款
(3, '王老师', NULL);                -- 王老师没有退款

-- 测试1：查看原始数据
SELECT '=== 原始数据（按teacher_id分组后的结果） ===';
SELECT teacher_id, teacher_name, refund_data FROM test_refund_groupby;

-- 测试2：执行groupBy('')合计（模拟问题代码）
SELECT '=== 执行GROUP BY \'\' 合计 ===';
SELECT 
    '' as teacher_id,
    '合计' as teacher_name,
    GROUP_CONCAT(CASE WHEN refund_data IS NOT NULL THEN refund_data ELSE NULL END) as refund_data
FROM test_refund_groupby;

-- 测试3：正确的合计方法（重新聚合原始数据）
SELECT '=== 正确的合计方法示例 ===';
-- 这里应该从原始订单数据重新聚合，而不是对已经分组的结果再次分组

-- 测试4：检验GROUP_CONCAT的分隔符问题
SELECT '=== 测试GROUP_CONCAT分隔符 ===';
SELECT 
    GROUP_CONCAT(refund_data) as direct_concat,
    GROUP_CONCAT(refund_data SEPARATOR ',') as comma_concat,
    GROUP_CONCAT(DISTINCT refund_data) as distinct_concat
FROM test_refund_groupby 
WHERE refund_data IS NOT NULL;