{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(php -l:*)", "Bash(rg:*)", "Bash(npm run lint:*)", "Bash(node:*)", "<PERSON><PERSON>(mysql:*)", "Bash(php yii migrate/create:*)", "Bash(php yii migrate:*)", "<PERSON><PERSON>(curl:*)", "Bash(php:*)", "Bash(rm:*)", "mcp__mysql__query_data", "mcp__mysql__describe_table", "<PERSON><PERSON>(env)", "Bash(npx eslint:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__Playwright__browser_navigate", "mcp__Playwright__browser_click", "mcp__Playwright__browser_wait_for", "mcp__Playwright__browser_tab_select", "mcp__Playwright__browser_snapshot", "WebFetch(domain:docs.anthropic.com)", "Bash(claude --version)", "<PERSON><PERSON>(claude config list)", "Bash(claude config set installation-type npm-global)", "Bash(claude config reset)", "<PERSON><PERSON>(claude /doctor)", "mcp__mysql__create_table", "WebFetch(domain:local.erp-saas.com)", "mcp__Playwright__browser_take_screenshot", "<PERSON><PERSON>(cat:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(chmod:*)"], "deny": []}, "enableAllProjectMcpServers": false}