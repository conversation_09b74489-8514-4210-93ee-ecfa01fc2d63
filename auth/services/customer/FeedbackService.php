<?php

namespace auth\services\customer;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\customer\Feedback;
use common\services\customer\FeedbackService as CommonFeedbackService;
use common\models\backend\order\OrderHeader;
use common\models\Customer;
use common\models\backend\Store;
use common\models\wxcom\CusCustomerUser;
use common\models\backend\Member;
use common\models\common\AdsMaterial;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\CustomerChurnRemarkReachStatusEnum;
use common\models\order\CustomerChurnRemark;
use services\UserService;
use yii\db\ActiveQuery;

class FeedbackService extends CommonFeedbackService
{
    /**
     * @var Feedback
     */
    public static $modelClass = Feedback::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = new ActiveQuery(static::$modelClass);
        $query->from(['cf' => static::$modelClass::tableName()]);
        
        $query->leftJoin(['oh' => OrderHeader::tableName()], 'oh.id = cf.order_id');
        $query->leftJoin(['c' => Customer::tableName()], 'c.id = cf.cus_id');
        $query->leftJoin(['s' => Store::tableName()], 's.id = cf.store_id');
        $query->leftJoin(['ccu' => CusCustomerUser::tableName()], 'ccu.id = oh.customer_user_id');
        $query->leftJoin(['bm' => Member::tableName()], 'bm.id = oh.promoter_user_id');
        $query->leftJoin(['am' => AdsMaterial::tableName()], 'am.material_id = ccu.mid3');
        
        $query->andWhere(['cf.entity_id' => UserService::getInst()->current_entity_id]);
        $query->andFilterWhere(['between', 'cf.created_at', $params['start_time'], $params['end_time']]);
        $query->andFilterWhere([
            'or',
            ['like', 'c.name', $params['cus_name']],
            ['like', 'c.nick_name', $params['cus_name']],
            ['like', 'c.remark', $params['cus_name']],
        ]);
        $query->andFilterWhere(['s.dept_id' => $params['store_id']]);
        $query->andFilterWhere(['=', 'oh.order_no', $params['order_no']]);
        
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @param bool $isExport 是否导出模式
     * @return array
     * @throws \Exception
     */
    public static function search($params = [], $isExport = false)
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'customer_info',
            'store_name',
            'plan_time_text',
            'status_text',
            'age_bracket_text',
            'feedback_short',
            'images_parsed',
            'promoter_name',
            'material_info',
            'account_info',
            'add_time_text'
        ]);

        $query = static::getQuery($params);
        
        $query->select([
            'cf.*',
            'oh.order_no',
            'oh.order_status',
            'oh.plan_time as order_plan_time',
            'oh.promoter_user_id',
            'c.name as customer_name',
            'c.nick_name',
            'c.avatar',
            'c.mobile',
            'c.material_id',
            's.store_name',
            'ccu.mid3',
            'ccu.add_time',
            'ccu.csite',
            'ccu.sub_advertiser_name',
            'ccu.sub_advertiser_id',
            'ccu.adid',
            'bm.username as promoter_username',
            'am.video_img',
        ]);
        
        // 分页处理
        $totalCount = $query->count();
        $query->orderBy('cf.created_at DESC');
        
        if (!$isExport) {
            $page = ArrayHelper::getValue($params, 'page', 1);
            $limit = ArrayHelper::getValue($params, 'limit', 20);
            $offset = ($page - 1) * $limit;
            $query->offset($offset)->limit($limit);
        }
        
        $list = $query->all();
        
        if (!empty($list)) {
            static::preloadCustomerStatus($list);
        }
        
        return [$list, $totalCount];
    }

    /**
     * 预加载客户状态，避免 N+1 查询问题
     * @param array $feedbackList
     */
    private static function preloadCustomerStatus($feedbackList)
    {
        $cusIds = array_unique(ArrayHelper::getColumn($feedbackList, 'cus_id'));
        if (empty($cusIds)) {
            return;
        }

        $statusMap = static::getCustomerStatusMap($cusIds);

        foreach ($feedbackList as $feedback) {
            $feedback->customer_status_text = $statusMap[$feedback->cus_id] ?? '其他';
        }
    }

    /**
     * 获取客户状态映射
     * @param array $cusIds
     * @return array
     */
    private static function getCustomerStatusMap($cusIds)
    {
        $sql = "
            SELECT 
                oh.cus_id,
                CASE
                    WHEN MAX(CASE WHEN oh.order_status = :completed THEN 1 ELSE 0 END) = 1 THEN '已完成'
                    WHEN MAX(CASE WHEN oh.order_status = :after_sale THEN 1 ELSE 0 END) = 1 THEN '售后服务'
                    WHEN MAX(CASE WHEN ccr.id IS NOT NULL THEN 1 ELSE 0 END) = 1 THEN '到店流失'
                    ELSE '其他'
                END AS customer_status
            FROM " . OrderHeader::tableName() . " oh
            LEFT JOIN " . CustomerChurnRemark::tableName() . " ccr 
                ON ccr.order_id = oh.id AND ccr.reach_status = :churn_status
            WHERE oh.cus_id IN (" . implode(',', array_map('intval', $cusIds)) . ")
            GROUP BY oh.cus_id
        ";

        $statusMap = [];
        $rows = \Yii::$app->db->createCommand($sql, [
            ':completed' => OrderHeaderStatusEnum::STATUS_COMPLETED,
            ':after_sale' => OrderHeaderStatusEnum::STATUS_AFTER_SALE,
            ':churn_status' => CustomerChurnRemarkReachStatusEnum::REACH_STORE_NOT_DONE,
        ])->queryAll();

        foreach ($rows as $row) {
            $statusMap[$row['cus_id']] = $row['customer_status'];
        }

        // 为没有订单的客户设置默认状态
        foreach ($cusIds as $cusId) {
            if (!isset($statusMap[$cusId])) {
                $statusMap[$cusId] = '其他';
            }
        }

        return $statusMap;
    }

    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }

        // 为单条记录也预加载状态
        static::preloadCustomerStatus([$info]);

        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }
}
