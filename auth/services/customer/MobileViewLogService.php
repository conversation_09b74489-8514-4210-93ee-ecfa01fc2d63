<?php

namespace auth\services\customer;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\customer\MobileViewLog;
use common\helpers\DateHelper;
use common\models\backend\Member;
use common\models\backend\order\OrderHeader;
use common\models\backend\Store;
use common\services\customer\MobileViewLogService as CommonMobileViewLogService;
use services\common\FeishuExamineService;
use services\UserService;
use Yii;

class MobileViewLogService extends CommonMobileViewLogService
{
    /**
     * @var MobileViewLog
     */
    public static $modelClass = MobileViewLog::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = static::$modelClass::find()
            ->alias('mv')
            ->select(['mv.mobile', 'mv.created_at', 'mv.created_by', 'oh.order_no', 's.store_name', 'm.username', 'm2.username AS created_by_username', 'oh.created_at AS order_created_at'])
            ->leftJoin(['oh' => OrderHeader::tableName()], 'oh.id = mv.order_id')
            ->leftJoin(['m' => Member::tableName()], 'm.id = mv.created_by')
            ->leftJoin(['m2' => Member::tableName()], 'm2.id = oh.created_by')
            ->leftJoin(['s' => Store::tableName()], 's.id = mv.store_id');
       
        // 条件过滤
        $query->andFilterWhere(['mv.mobile' => $params['mobile']])
            ->andFilterWhere(['oh.order_no' => $params['order_no']])
            ->andFilterWhere(['between', 'mv.created_at', $params['start_time'], $params['end_time']])
            ->andFilterWhere(['like', 'm.username', $params['username']]);

        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page') ?: 1;   //页码
        $limit = ArrayHelper::getValue($params, 'limit') ?: 10;   //条数
        $offset = ($page - 1) * $limit;

        $query = static::getQuery($params);
        $totalCount = $query->count();

        $query->offset($offset)->limit($limit)->orderBy('mv.id DESC');
        $list = $query->asArray()->all();

        foreach ($list as &$item) {
            $item['created_at_text'] = DateHelper::toDate($item['created_at'], 'Y-m-d H:i:s');
            $item['order_created_at_text'] = DateHelper::toDate($item['order_created_at'], 'Y-m-d H:i:s');
        }
        
        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    public static function create($params)
    {
        $orderId = ArrayHelper::getValue($params, 'order_id');
        $customerId = ArrayHelper::getValue($params, 'cus_id');
        $storeId = OrderHeader::find() ->select(['store_id']) ->where(['id' => $orderId]) ->scalar();

        $model = new static::$modelClass();
        $model->setAttributes([
            'mobile' => $params['mobile'] ?? '',
            'order_id' => $orderId,
            'customer_id' => $customerId,
            'store_id' => $storeId,
        ]);
        if (!$model->save()) {
            throw new Exception('保存失败: ' . $model->getFirstErrMsg());
        }

        return $model;
    }

    public static function viewWarning($orderId)
    {
        if (Yii::$app->services->auth->isSuperAdmin() || empty($orderId)) {
            return;
        }

        $today = DateHelper::today();
        $count = static::$modelClass::find()
            ->alias('mv')
            ->select(['COUNT(DISTINCT mv.customer_id) AS count'])
            ->leftJoin(['oh' => OrderHeader::tableName()], 'mv.order_id = oh.id')
            ->where(['mv.created_by' => UserService::getInst()->id])
            ->andWhere(['between', 'mv.created_at', $today['start'], $today['end']])
            ->andWhere(['!=', 'oh.created_by', UserService::getInst()->id])
            ->scalar();
        if ($count <= 20) {
            return;
        }

        $isMyOrder = OrderHeader::find()
            ->select(['id'])
            ->where(['id' => $orderId])
            ->andWhere(['created_by' => UserService::getInst()->id])
            ->scalar();
        if ($isMyOrder) {
            return;
        }

        $group = FeishuExamineService::arrGroup('XXXLYJQ');
        $content = '<at user_id="all">所有人</at>【' . Yii::$app->user->identity->username . '】今日查看非本人客户号码超过20次，请及时关注。';
        Yii::$app->feishuNotice->text($content, $group['chat_id']);
    }
}
