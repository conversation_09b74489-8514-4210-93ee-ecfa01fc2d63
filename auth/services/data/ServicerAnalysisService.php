<?php

namespace auth\services\data;

use auth\models\data\ServicerAnalysis;
use backendapi\services\promote\PromoteProjectService;
use common\helpers\ArrayHelper;
use common\enums\order\OrderHeaderStatusEnum;
use common\models\common\Department;
use common\models\common\DepartmentAssignment;
use common\models\wxcom\CusCustomerUser;
use common\models\backend\order\OrderPlanDetail;
use common\services\data\ServicerAnalysisService as CommonServicerAnalysisService;
use Exception;
use services\UserService;
use backendapi\models\order\OrderHeader;
use common\models\Customer;
use common\models\wechat\FansRecord;
use backendapi\services\promote\AnalysisService;
use common\helpers\BcHelper;
use yii\db\Expression;
use Yii;

class ServicerAnalysisService extends CommonServicerAnalysisService
{
    /**
     * @var ServicerAnalysis
     */
    public static $modelClass = ServicerAnalysis::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = static::$modelClass::find();
        $query->alias('analysis');
        $query->joinWith(['deptAssignment deptAssignment' => function ($query) {
            $query->with(['dept']);
        }]);
        // 时间过滤
        $searchStartTime = $params['search_start_time'] ?: strtotime(date('Y-m-1'));
        $searchEndTime = $params['search_end_time'] ?: time();
        $query->andFilterWhere(['between', 'date_time', $searchStartTime, $searchEndTime]);
        // 客服搜索
        $userKeyword = $params['user_keyword'];
        if ($userKeyword) {
            $query->joinWith(['user'])->andFilterWhere(['like', 'realname', $userKeyword]);
        }
        // 部门过滤
        $deptId = $params['dept_id'];
        if ($deptId) {
            $deptIds = Department::getManageDeptIdsByIds($deptId);
            $query->andFilterWhere(['deptAssignment.dept_id' => $deptIds]);
        }
        // 管理范围
        $scope = Yii::$app->services->scopeDataService->getScope();
        if (!empty($scope)) {
            $query->andWhere([
                'or',
                ['analysis.user_id' => UserService::getInst()->id],
                ['in', 'deptAssignment.dept_id', $scope]
            ]);
        }

        $query->andFilterWhere(['analysis.project_id' => AnalysisService::getProjectId($params['project_type'], $params['project_id'])]);

        return $query;
    }

    /**
     * 获取新客当月到店人数统计
     * @param array $params
     * @param bool $isGroup
     * @return array
     */
    public static function getNewCustomerStoreCount($params, $isGroup = false)
    {
        try {
            //完成状态
            $completedStatus = OrderHeader::orderCompletedStatusList();
            $orderQuery = OrderHeader::find()
                ->alias('h')
                ->leftJoin(['c' => Customer::tableName()], 'c.id = h.cus_id')
                ->leftJoin(['cu' => CusCustomerUser::tableName()], 'cu.id = h.customer_user_id')
                ->leftJoin(['f' => FansRecord::tableName()], 'f.id = c.add_fans_id')
                ->where(['between', 'h.plan_time', $params['start_time'], $params['end_time']])
                ->andWhere(['in', 'h.order_status', $completedStatus])
                ->andWhere(new Expression('FROM_UNIXTIME(h.plan_time, \'%Y%m%d\') = FROM_UNIXTIME(c.first_store_time, \'%Y%m%d\')'))
                ->andWhere(new Expression("COALESCE(cu.add_time, f.add_time) BETWEEN {$params['start_time']} AND {$params['end_time']}"))
                ->andFilterWhere([
                    'c.project_id' => AnalysisService::getProjectId($params['project_type'], $params['project_id'])
                ])
                ->asArray();

            if ($isGroup) {
                // 按部门分组
                $orderQuery
                    ->leftJoin(['da' => DepartmentAssignment::tableName()], 'da.user_id = h.plan_by')
                    ->select('da.dept_id as dept_id, count(DISTINCT c.id) as cus_count')
                    ->groupBy('da.dept_id')
                    ->indexBy('dept_id');
            } else {
                // 按用户分组
                $orderQuery
                    ->select('h.plan_by as user_id, count(DISTINCT c.id) as cus_count')
                    ->groupBy('h.plan_by')
                    ->indexBy('user_id');
            }

            // 执行查询并返回结果
            $list = $orderQuery->all();
            return $list;
        } catch (Exception $e) {
            error_log($e->getMessage());
            return [];
        }
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws Exception
     */
    public static function search($params = [])
    {
        $searchStartTime = $params['search_start_time'] ?: strtotime(date('Y-m-1'));
        $searchEndTime = $params['search_end_time'] ?: time();
        // 新客当月到店人数
        $newCusCountList = static::getNewCustomerStoreCount([
            'start_time' => $searchStartTime,
            'end_time' => $searchEndTime,
            'project_type' => $params['project_type'],
            'project_id' => $params['project_id'],
        ]);

        $performanceData = static::performanceData($params);
        $performanceDataList = ArrayHelper::index($performanceData, 'id');

        // 当月订金人数
        $monthDepositCusCountList = static::getMonthDepositCusCount([
            'start_time' => $searchStartTime,
            'end_time' => $searchEndTime,
            'project_type' => $params['project_type'],
            'project_id' => $params['project_id'],
        ]);

        $query = static::getQuery($params);

        ServicerAnalysis::setExtendAttrs([
            'deptAssignment.dept.name' => 'dept_name',
            'user.realname' => 'user_name',
            'user.servicerRoleNames' => 'role_names',
            'user.status' => 'status',
            'deposit_count_rate',
            'deposit_cus_count_rate',
            'deposit_store_rate',
            'month_deposit_cus_count_rate',
            'new_store_cus_count_rate',
            'remote_count_rate',
            'attrition_count_rate',
            [
                'field' => 'new_fans_store_cus_count',
                'value' => function ($servicerAnalysis) use ($newCusCountList) {
                    return ArrayHelper::getValue($newCusCountList, "{$servicerAnalysis->user_id}.cus_count", 0);
                }
            ],
            [
                'field' => 'new_fans_store_cus_count_rate',
                'value' => function ($servicerAnalysis) use ($newCusCountList) {
                    return round(ArrayHelper::getValue($newCusCountList, "{$servicerAnalysis->user_id}.cus_count", 0) / ($servicerAnalysis['add_fans_count'] ?: 1) * 100, 2);
                }
            ],
            [
                'field' => 'day_count',
                'value' => function ($item) {
                    return intval($item->day_count);
                }
            ],
            [
                'field' => 'performance',
                'value' => function ($servicerAnalysis) use ($performanceDataList) {
                    return ArrayHelper::getValue($performanceDataList, "{$servicerAnalysis->user_id}.performance", 0);
                }
            ],
            [
                'field' => 'per_customer',
                'value' => function ($servicerAnalysis) use ($performanceDataList) {
                    return round(ArrayHelper::getValue($performanceDataList, "{$servicerAnalysis->user_id}.performance", 0) / ($servicerAnalysis['new_store_cus_count'] ?: 1), 2);
                }
            ],
            [
                'field' => 'month_deposit_cus_count',
                'value' => function ($servicerAnalysis) use ($monthDepositCusCountList) {
                    return ArrayHelper::getValue($monthDepositCusCountList, "{$servicerAnalysis->user_id}.month_deposit_cus_count", 0);
                }
            ],
            [
                'field' => 'month_deposit_cus_count_rate',
                'value' => function ($servicerAnalysis) use ($monthDepositCusCountList) {
                    return round(ArrayHelper::getValue($monthDepositCusCountList, "{$servicerAnalysis->user_id}.month_deposit_cus_count", 0) / ($servicerAnalysis['add_fans_count'] ?: 1) * 100, 2);
                }
            ],
        ]);

        $list = $query
            ->with(['user' => function ($query) {
                $query->with(['authAssignments' => function ($query) {
                    $query->with(['role']);
                }]);
            }])
            ->select([
                'deptAssignment.dept_id',
                'analysis.user_id',
                'sum(add_fans_count) as add_fans_count',
                'sum(plan_cus_count) as plan_cus_count',
                'sum(new_plan_cus_count) as new_plan_cus_count',
                'sum(tomorrow_plan_cus_count) as tomorrow_plan_cus_count',
                'sum(tomorrow_new_plan_cus_count) as tomorrow_new_plan_cus_count',
                'sum(deposit_count) as deposit_count',
                'round(sum(analysis.deposit_sum),2) AS deposit_sum',
                'sum(deposit_cus_count) as deposit_cus_count',
                'sum(new_store_cus_count) as new_store_cus_count',
                'sum(new_store_cus_count_before) as new_store_cus_count_before',
                'count(DISTINCT(case when deposit_count>0 then CONCAT(analysis.user_id,analysis.date_time) end)) AS day_count',
                'sum(remote_count) as remote_count',
                'sum(attrition_count) as attrition_count',
            ])
            ->groupBy('deptAssignment.dept_id,analysis.user_id')
            ->orderBy('deptAssignment.dept_id,analysis.user_id')
            ->all();
        $newList = ArrayHelper::toArray($list);
        $totalCount = count($newList);

        $totalRecord = [
            'index' => '-',
            'dept_id' => 0,
            'dept_name' => '-',
            'user_id' => 0,
            'user_name' => '-',
            'role_names' => [],
            'status' => 1,
            'add_fans_count' => 0,
            'plan_cus_count' => 0,
            'new_plan_cus_count' => 0,
            'tomorrow_plan_cus_count' => 0,
            'tomorrow_new_plan_cus_count' => 0,
            'deposit_cus_count' => 0,
            'deposit_count' => 0,
            'month_deposit_cus_count' => 0,
            'deposit_sum' => 0,
            'day_count' => 0,
            'new_store_cus_count' => 0,
            'new_store_cus_count_before' => 0,
            'new_fans_store_cus_count' => 0,
            'performance' => 0,
            'remote_count' => 0,
            'remote_count_rate' => 0,
            'attrition_count' => 0,
            'attrition_count_rate' => 0,
        ];

        foreach ($newList as $item) {
            $totalRecord['add_fans_count'] += $item['add_fans_count'];
            $totalRecord['plan_cus_count'] += $item['plan_cus_count'];
            $totalRecord['new_plan_cus_count'] += $item['new_plan_cus_count'];
            $totalRecord['tomorrow_plan_cus_count'] += $item['tomorrow_plan_cus_count'];
            $totalRecord['tomorrow_new_plan_cus_count'] += $item['tomorrow_new_plan_cus_count'];
            $totalRecord['deposit_cus_count'] += $item['deposit_cus_count'];
            $totalRecord['deposit_count'] += $item['deposit_count'];
            $totalRecord['month_deposit_cus_count'] += $item['month_deposit_cus_count'];
            $totalRecord['deposit_sum'] += $item['deposit_sum'];
            $totalRecord['day_count'] += $item['day_count'];
            $totalRecord['new_store_cus_count'] += $item['new_store_cus_count'];
            $totalRecord['new_store_cus_count_before'] += $item['new_store_cus_count_before'];
            $totalRecord['new_fans_store_cus_count'] += $item['new_fans_store_cus_count'];
            $totalRecord['performance'] = BcHelper::add($totalRecord['performance'], $item['performance']);
            $totalRecord['remote_count'] += $item['remote_count'];
            $totalRecord['attrition_count'] += $item['attrition_count'];
        }

        // 排序
        $orderField = $params['order_field'] ?: 'new_store_cus_count';
        $orderType = $params['order_type'] ?: 'desc';
        $newList = ArrayHelper::arraySort($newList, $orderField, $orderType);
        $index = 0;
        foreach ($newList as &$item) {
            $item['index'] = ++$index;
        }

        // 分页
        $page = (isset($params['page']) && !empty($params['page'])) ? $params['page'] : 1;
        $limit = (isset($params['limit']) && !empty($params['limit'])) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;

        $newList = array_splice($newList, $offset, $limit);

        $totalRecord['deposit_sum'] = round($totalRecord['deposit_sum'], 2);
        $totalRecord['deposit_count_rate'] = round($totalRecord['deposit_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['month_deposit_cus_count_rate'] = round($totalRecord['month_deposit_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['deposit_cus_count_rate'] = round($totalRecord['deposit_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['deposit_store_rate'] = round($totalRecord['new_store_cus_count'] / ($totalRecord['deposit_cus_count'] ?: 1) * 100, 2);
        $totalRecord['new_fans_store_cus_count_rate'] = round($totalRecord['new_fans_store_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['new_store_cus_count_rate'] = round($totalRecord['new_store_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['remote_count_rate'] = round($totalRecord['remote_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['attrition_count_rate'] = round($totalRecord['attrition_count'] / (($totalRecord['attrition_count'] + $totalRecord['new_store_cus_count']) ?: 1) * 100, 2);
        $totalRecord['per_customer'] = round($totalRecord['performance'] / ($totalRecord['new_store_cus_count'] ?: 1), 2);
        if (!isset($params['is_child']) || !$params['is_child']) {
            array_unshift($newList, $totalRecord);
        }
        return [$newList, $totalCount];
    }

    /**
     * 获取当月订金人数
     * @param array $params
     * @param bool $isGroup
     * @return array
     */
    public static function getMonthDepositCusCount($params, $isGroup = false)
    {
        try {
            $depositOrderStatusList = OrderHeader::depositOrderStatusList();
            $computeAddWay = CusCustomerUser::getComputeAddWay();

            $orderQuery = OrderPlanDetail::find()
                ->alias('planDetail')
                ->leftJoin('erp_order_header orderHeader', 'orderHeader.id = planDetail.order_id')
                ->leftJoin('erp_customer cus', 'cus.id = orderHeader.cus_id')
                ->leftJoin('erp_wxcom_cus_customer_user ccu', 'ccu.id = orderHeader.customer_user_id')
                ->where(['planDetail.entity_id' => UserService::getInst()->current_entity_id])
                ->andWhere(['>', 'ccu.channel_id', 0])
                ->andWhere(['ccu.add_way' => $computeAddWay])
                ->andWhere(['between', 'orderHeader.pre_pay_time', $params['start_time'], $params['end_time']])
                ->andWhere(['between', 'ccu.add_time', $params['start_time'], $params['end_time']])
                ->andFilterWhere([
                    'cus.project_id' => AnalysisService::getProjectId($params['project_type'], $params['project_id'])
                ])
                ->asArray();

            if ($isGroup) {
                $orderQuery
                    ->leftJoin(['da' => DepartmentAssignment::tableName()], 'da.user_id = planDetail.created_by')
                    ->select('da.dept_id as dept_id,count(distinct orderHeader.cus_id) as month_deposit_cus_count')
                    ->groupBy('da.dept_id')
                    ->indexBy('dept_id');
            } else {
                $orderQuery
                    ->select('planDetail.created_by as user_id,count(distinct orderHeader.cus_id) as month_deposit_cus_count')
                    ->groupBy('planDetail.created_by')
                    ->indexBy('user_id');
            }

            // 当月订金人数
            $list = $orderQuery
                ->andWhere(['orderHeader.order_status' => $depositOrderStatusList])
                ->all();

            return $list;
        } catch (Exception $e) {
            error_log($e->getMessage());
            return [];
        }
    }

    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andFilterWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function searchForGroup($params = [])
    {
        $searchStartTime = $params['search_start_time'] ?: strtotime(date('Y-m-1'));
        $searchEndTime = $params['search_end_time'] ?: time();
        // 新客当月到店人数
        $newCusCountList = static::getNewCustomerStoreCount([
            'start_time' => $searchStartTime,
            'end_time' => $searchEndTime,
            'project_type' => $params['project_type'],
            'project_id' => $params['project_id'],
        ], true);

        $performanceData = static::performanceDataGroup($params);
        $performanceDataList = ArrayHelper::index($performanceData, 'dept_id');

        $monthDepositCusCountList = static::getMonthDepositCusCount([
            'start_time' => $searchStartTime,
            'end_time' => $searchEndTime,
            'project_type' => $params['project_type'],
            'project_id' => $params['project_id'],
        ], true);

        $query = static::getQuery($params);

        ServicerAnalysis::setExtendAttrs([
            'dept.name' => 'dept_name',
            'deposit_count_rate',
            'month_deposit_cus_count_rate',
            'deposit_cus_count_rate',
            'deposit_store_rate',
            'new_store_cus_count_rate',
            'remote_count_rate',
            'attrition_count_rate',
            [
                'field' => 'new_fans_store_cus_count',
                'value' => function ($servicerAnalysis) use ($newCusCountList) {
                    return ArrayHelper::getValue($newCusCountList, "{$servicerAnalysis->dept_id}.cus_count", 0);
                }
            ],
            [
                'field' => 'new_fans_store_cus_count_rate',
                'value' => function ($servicerAnalysis) use ($newCusCountList) {
                    return round(ArrayHelper::getValue($newCusCountList, "{$servicerAnalysis->dept_id}.cus_count", 0) / ($servicerAnalysis['add_fans_count'] ?: 1) * 100, 2);
                }
            ],
            [
                'field' => 'day_count',
                'value' => function ($item) {
                    return intval($item->day_count);
                }
            ],
            [
                'field' => 'performance',
                'value' => function ($servicerAnalysis) use ($performanceDataList) {
                    return ArrayHelper::getValue($performanceDataList, "{$servicerAnalysis->dept_id}.performance", 0);
                }
            ],
            [
                'field' => 'per_customer',
                'value' => function ($servicerAnalysis) use ($performanceDataList) {
                    return round(ArrayHelper::getValue($performanceDataList, "{$servicerAnalysis->dept_id}.performance", 0) / ($servicerAnalysis['new_store_cus_count'] ?: 1), 2);
                }
            ],
            [
                'field' => 'month_deposit_cus_count',
                'value' => function ($servicerAnalysis) use ($monthDepositCusCountList) {
                    return ArrayHelper::getValue($monthDepositCusCountList, "{$servicerAnalysis->dept_id}.month_deposit_cus_count", 0);
                }
            ],
            [
                'field' => 'month_deposit_cus_count_rate',
                'value' => function ($servicerAnalysis) use ($monthDepositCusCountList) {
                    return round(ArrayHelper::getValue($monthDepositCusCountList, "{$servicerAnalysis->dept_id}.month_deposit_cus_count", 0) / ($servicerAnalysis['add_fans_count'] ?: 1) * 100, 2);
                }
            ],
        ]);

        $list = $query
            ->with('dept')
            ->select([
                'deptAssignment.dept_id',
                'sum(add_fans_count) as add_fans_count',
                'sum(plan_cus_count) as plan_cus_count',
                'sum(new_plan_cus_count) as new_plan_cus_count',
                'sum(tomorrow_plan_cus_count) as tomorrow_plan_cus_count',
                'sum(tomorrow_new_plan_cus_count) as tomorrow_new_plan_cus_count',
                'sum(deposit_count) as deposit_count',
                'round(sum(analysis.deposit_sum),2) AS deposit_sum',
                'sum(deposit_cus_count) as deposit_cus_count',
                'sum(new_store_cus_count) as new_store_cus_count',
                'sum(new_store_cus_count_before) as new_store_cus_count_before',
                'count(DISTINCT(case when deposit_count>0 then CONCAT(analysis.user_id,analysis.date_time) end)) AS day_count',
                'sum(remote_count) as remote_count',
                'sum(attrition_count) as attrition_count'
            ])
            ->groupBy('deptAssignment.dept_id')
            ->orderBy('deptAssignment.dept_id')
            ->all();
        $newList = ArrayHelper::toArray($list);
        $totalCount = count($newList);

        $totalRecord = [
            'index' => '-',
            'dept_id' => 0,
            'dept_name' => '-',
            'user_name' => '-',
            'add_fans_count' => 0,
            'plan_cus_count' => 0,
            'new_plan_cus_count' => 0,
            'tomorrow_plan_cus_count' => 0,
            'tomorrow_new_plan_cus_count' => 0,
            'deposit_cus_count' => 0,
            'deposit_count' => 0,
            'month_deposit_cus_count' => 0,
            'deposit_sum' => 0,
            'day_count' => 0,
            'new_store_cus_count' => 0,
            'new_store_cus_count_before' => 0,
            'new_fans_store_cus_count' => 0,
            'performance' => 0,
            'remote_count' => 0,
            'attrition_count' => 0
        ];

        foreach ($newList as $item) {
            $totalRecord['add_fans_count'] += $item['add_fans_count'];
            $totalRecord['plan_cus_count'] += $item['plan_cus_count'];
            $totalRecord['new_plan_cus_count'] += $item['new_plan_cus_count'];
            $totalRecord['tomorrow_plan_cus_count'] += $item['tomorrow_plan_cus_count'];
            $totalRecord['tomorrow_new_plan_cus_count'] += $item['tomorrow_new_plan_cus_count'];
            $totalRecord['deposit_cus_count'] += $item['deposit_cus_count'];
            $totalRecord['deposit_count'] += $item['deposit_count'];
            $totalRecord['month_deposit_cus_count'] += $item['month_deposit_cus_count'];
            $totalRecord['deposit_sum'] += $item['deposit_sum'];
            $totalRecord['day_count'] += $item['day_count'];
            $totalRecord['new_store_cus_count'] += $item['new_store_cus_count'];
            $totalRecord['new_store_cus_count_before'] += $item['new_store_cus_count_before'];
            $totalRecord['new_fans_store_cus_count'] += $item['new_fans_store_cus_count'];
            $totalRecord['performance'] = BcHelper::add($totalRecord['performance'], $item['performance']);
            $totalRecord['remote_count'] += $item['remote_count'];
            $totalRecord['attrition_count'] += $item['attrition_count'];
        }

        // 排序
        $orderField = $params['order_field'] ?: 'new_store_cus_count';
        $orderType = $params['order_type'] ?: 'desc';
        $newList = ArrayHelper::arraySort($newList, $orderField, $orderType);
        $index = 0;
        foreach ($newList as &$item) {
            $item['index'] = ++$index;
        }

        // 分页
        $page = $params['page'] - 0 ?: 1;
        $limit = $params['limit'] - 0 ?: 10;
        $offset = ($page - 1) * $limit;
        $newList = array_splice($newList, $offset, $limit);

        $totalRecord['deposit_sum'] = round($totalRecord['deposit_sum'], 2);
        $totalRecord['deposit_count_rate'] = round($totalRecord['deposit_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['month_deposit_cus_count_rate'] = round($totalRecord['month_deposit_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['deposit_cus_count_rate'] = round($totalRecord['deposit_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['deposit_store_rate'] = round($totalRecord['new_store_cus_count'] / ($totalRecord['deposit_cus_count'] ?: 1) * 100, 2);
        $totalRecord['new_fans_store_cus_count_rate'] = round($totalRecord['new_fans_store_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['new_store_cus_count_rate'] = round($totalRecord['new_store_cus_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['remote_count_rate'] = round($totalRecord['remote_count'] / ($totalRecord['add_fans_count'] ?: 1) * 100, 2);
        $totalRecord['attrition_count_rate'] = round($totalRecord['attrition_count'] / (($totalRecord['attrition_count'] + $totalRecord['new_store_cus_count']) ?: 1) * 100, 2);
        $totalRecord['per_customer'] = round($totalRecord['performance'] / ($totalRecord['new_store_cus_count'] ?: 1), 2);
        if (!isset($params['is_child']) || !$params['is_child']) {
            array_unshift($newList, $totalRecord);
        }
        return [$newList, $totalCount];
    }

    /**
     * 导出列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function exportForGroup($params = [])
    {
        return static::searchForGroup($params);
    }

    /**
     * 获取总行数
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getTotal($params = [])
    {
        $query = static::getQuery($params);

        ServicerAnalysis::setExtendAttrs([
            'deptAssignment.dept.name' => 'dept_name',
            'user.realname' => 'user_name',
            'user.servicerRoleNames' => 'role_names',
            'user.status' => 'status',
            'deposit_cus_count_rate',
            'deposit_store_rate',
            'new_fans_store_cus_count_rate',
            'new_store_cus_count_rate',
        ]);

        $list = $query
            ->with(['user' => function ($query) {
                $query->with(['authAssignments' => function ($query) {
                    $query->with(['role']);
                }]);
            }])
            ->select('deptAssignment.dept_id,analysis.user_id,sum(add_fans_count) as add_fans_count,sum(plan_cus_count) as plan_cus_count,sum(deposit_cus_count) as deposit_cus_count,sum(new_store_cus_count) as new_store_cus_count,sum(new_store_cus_count_before) as new_store_cus_count_before')
            ->groupBy('deptAssignment.dept_id,analysis.user_id')
            ->orderBy('deptAssignment.dept_id,analysis.user_id')
            ->all();
        $newList = ArrayHelper::toArray($list);
        $totalCount = count($newList);

        return [[], $totalCount];
    }

    /**
     * 获取总行数
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getTotalForGroup($params = [])
    {
        $query = static::getQuery($params);

        ServicerAnalysis::setExtendAttrs([
            'deptAssignment.dept.name' => 'dept_name',
            'deposit_cus_count_rate',
            'deposit_store_rate',
            'new_fans_store_cus_count_rate',
            'new_store_cus_count_rate',
        ]);

        $list = $query
            ->select('deptAssignment.dept_id,sum(add_fans_count) as add_fans_count,sum(plan_cus_count) as plan_cus_count,sum(deposit_cus_count) as deposit_cus_count,sum(new_store_cus_count) as new_store_cus_count,sum(new_store_cus_count_before) as new_store_cus_count_before')
            ->groupBy('deptAssignment.dept_id')
            ->orderBy('deptAssignment.dept_id')
            ->all();
        $newList = ArrayHelper::toArray($list);
        $totalCount = count($newList);

        return [[], $totalCount];
    }

    /**
     * 客服业绩
     */
    public static function performanceData($params)
    {
        $params['end_time_2'] = $params['search_end_time'] + 2 * 86400;

        $query = OrderHeader::find()->alias('eoh')
            ->select(['new_data.plan_by as id,SUM(IFNULL(eoh.received_amount, 0) + IFNULL(eoh.card_real_amount, 0) + IFNULL(eoh.group_amount, 0)) AS performance'])
            ->leftJoin('{{%customer}} ec', 'eoh.cus_id = ec.id')
            ->innerJoin("
                (SELECT 
                    h.plan_by, h.cus_id 
                FROM 
                    erp_order_header h 
                LEFT JOIN erp_customer ec_sub ON h.cus_id = ec_sub.id
                WHERE 
                    h.order_status = 5 
                    AND h.source_type = 1 
                    AND FROM_UNIXTIME(h.plan_time,'%Y-%m-%d') =  FROM_UNIXTIME(ec_sub.first_store_time,'%Y-%m-%d')
                    AND h.plan_time BETWEEN {$params['search_start_time']} AND {$params['search_end_time']}
                    GROUP BY h.cus_id ) AS new_data
            ", 'new_data.cus_id = eoh.cus_id')
            ->leftJoin('{{%backend_member}} ebm', 'ebm.id = new_data.plan_by')
            ->where(['eoh.order_status' => 5])
            ->andFilterWhere(['between', 'eoh.plan_time', $params['search_start_time'], $params['end_time_2']])
            ->andWhere(new Expression("eoh.plan_time BETWEEN 
                UNIX_TIMESTAMP(DATE(FROM_UNIXTIME(ec.first_store_time))) AND 
                (UNIX_TIMESTAMP(DATE_ADD(DATE(FROM_UNIXTIME(ec.first_store_time)), INTERVAL 2 DAY)) + 86399)"))
            ->andFilterWhere(['like', 'ebm.username', trim($params['user_keyword'])])
            ->andFilterWhere(['ec.project_id' => AnalysisService::getProjectId($params['project_type'], $params['project_id'])])
            ->groupBy('new_data.plan_by');

        return $query->asArray()->all();
    }

    public static function performanceDataGroup($params)
    {
        $params['end_time_2'] = $params['search_end_time'] + 2 * 86400;

        $query = OrderHeader::find()->alias('eoh')
            ->select(['da.dept_id,SUM(IFNULL(eoh.received_amount, 0) + IFNULL(eoh.card_real_amount, 0) + IFNULL(eoh.group_amount, 0)) AS performance'])
            ->leftJoin('{{%customer}} ec', 'eoh.cus_id = ec.id')
            ->innerJoin("
                (SELECT 
                    h.plan_by, h.cus_id 
                FROM 
                    erp_order_header h 
                LEFT JOIN erp_customer ec_sub ON h.cus_id = ec_sub.id
                WHERE 
                    h.order_status = 5 
                    AND h.source_type = 1 
                    AND FROM_UNIXTIME(h.plan_time,'%Y-%m-%d') =  FROM_UNIXTIME(ec_sub.first_store_time,'%Y-%m-%d')
                    AND h.plan_time BETWEEN {$params['search_start_time']} AND {$params['search_end_time']} 
                    GROUP BY h.cus_id ) AS new_data
            ", 'new_data.cus_id = eoh.cus_id')
            ->leftJoin('{{%backend_member}} ebm', 'ebm.id = new_data.plan_by')
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = ebm.id')
            ->where(['eoh.order_status' => 5])
            ->andFilterWhere(['between', 'eoh.plan_time', $params['search_start_time'], $params['end_time_2']])
            ->andWhere(new Expression("eoh.plan_time BETWEEN 
                UNIX_TIMESTAMP(DATE(FROM_UNIXTIME(ec.first_store_time))) AND 
                (UNIX_TIMESTAMP(DATE_ADD(DATE(FROM_UNIXTIME(ec.first_store_time)), INTERVAL 2 DAY)) + 86399)"))
            ->andFilterWhere(['like', 'ebm.username', trim($params['user_keyword'])])
            ->andFilterWhere(['ec.project_id' => AnalysisService::getProjectId($params['project_type'], $params['project_id'])])
            ->groupBy('da.dept_id');

        return $query->asArray()->all();
    }
}
