<?php

namespace auth\services\promote;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\promote\AdsAdvertisingBudget;
use common\models\rbac\AuthAssignment;
use common\models\rbac\AuthRole;
use common\models\backend\Member;
use common\enums\StatusEnum;
use common\enums\AppEnum;
use common\services\promote\AdsAdvertisingBudgetService as CommonAdsAdvertisingBudgetService;
use Yii;

class AdsAdvertisingBudgetService extends CommonAdsAdvertisingBudgetService
{
    const PROMOTE_AUTH_ROLE_TITLE = [
        '推广专员',
        '推广经理',
        '推广总监',
        '推广主管'
    ];

    /**
     * @var AdsAdvertisingBudget
     */
    public static $modelClass = AdsAdvertisingBudget::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = Member::find()
            ->alias('m')
            ->select(['m.id as member_id', 'm.username'])
            ->leftJoin(['aa' => AuthAssignment::tableName()], 'm.id = aa.user_id')
            ->leftJoin(['ar' => AuthRole::tableName()], 'ar.id = aa.role_id')
            ->where(['ar.title' => self::PROMOTE_AUTH_ROLE_TITLE])
            ->andWhere(['m.current_entity_id' => Yii::$app->user->identity->current_entity_id])
            ->andWhere(['m.status' => StatusEnum::ENABLED])
            ->andWhere(['aa.app_id' => AppEnum::BACKEND_API])
            ->andFilterWhere(['like', 'm.username', $params['username']]);

        $query->offset($offset)->limit($limit)->groupBy('m.id')->orderBy('m.id ASC');
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
        ]);

        $query = static::getQuery($params);
        $totalCount = $query->count();
        $list = $query->asArray()->all();

        $platform = ArrayHelper::getValue($params, 'platform');
        if (empty($platform)) {
            throw new Exception('请选择平台');
        }   

        $budgets = static::$modelClass::findAll(['platform' => $platform]);
        if (empty($budgets)) {
            return [$list, $totalCount];
        }

        $userIdToBudgetId = [];
        $memberBudgetMapping = [];
        foreach ($budgets as $budget) {
            $userIdToBudgetId[$budget->promote_user_id] = $budget->id;
            ArrayHelper::setValue($memberBudgetMapping, $budget->promote_user_id.'.'.$budget->platform, $budget->budget);
        }
        foreach ($list as $key => $item) {
            $list[$key]['budget'] = ArrayHelper::getValue($memberBudgetMapping, $item['member_id'].'.'.$platform, '');
            $list[$key]['budget_id'] = ArrayHelper::getValue($userIdToBudgetId, $item['member_id'], 0);
            // 今日已充金额字段 - 目前暂时不返回，前端会显示 "-"
            // 后续如果需要实际数据，需要根据业务逻辑从相关表中查询
            $list[$key]['today_transfer_money'] = null;
        }

        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            $info = new static::$modelClass();
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }
}
