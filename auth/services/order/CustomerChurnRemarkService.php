<?php

namespace auth\services\order;

use auth\models\data\StorePerformanceAnalysis;
use Exception;
use common\helpers\ArrayHelper;
use common\models\backend\order\OrderHeader;
use auth\models\order\CustomerChurnRemark;
use common\enums\CustomerChurnRemarkReachStatusEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\helpers\DateHelper;
use common\services\order\CustomerChurnRemarkService as CommonCustomerChurnRemarkService;
use Yii;

class CustomerChurnRemarkService extends CommonCustomerChurnRemarkService
{
    /**
     * @var CustomerChurnRemark
     */
    public static $modelClass = CustomerChurnRemark::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'plan_teacher_name_text',
            'plan_time_text',
            'reach_status_text',
            'reason_status_text',
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
            'orderHeader.order_no',
            'orderHeader.order_status',
            'orderHeader.store_id',
            'orderHeader.cus_id',
            'orderHeader.store.store_name',
            'orderHeader.customer.name' => 'cus_name',
            // 'orderHeader.customer.mobile' => 'cus_mobile',
        ]);

        if ($params['start_time'] && $params['end_time']) {
            $startTime = strtotime(date('Y-m-d', $params['start_time']));
            $endTime = strtotime(date('Y-m-d 23:59:59', $params['end_time']));
        } else {    // 默认只能查看当天的数据
            $startTime = strtotime(date('Y-m-d'));
            $endTime = $startTime + 86400 - 1;
        }

        $query = static::$modelClass::find()
            ->alias('r')
            ->joinWith(['orderHeader o'])
            ->orderBy('r.id DESC');

        $query->where(['r.entity_id' => Yii::$app->user->identity->current_entity_id]);

        $query->andFilterWhere(['r.reason_status' => $params['reason_status']])
            ->andFilterWhere(['o.store_id' => $params['store_id']])
            ->andFilterWhere(['BETWEEN', 'r.plan_time', $startTime, $endTime]);

        // $scope = Yii::$app->services->scopeDataService->getMenuStore() ?: [-1];
        // $query->andFilterWhere(['o.store_id' => $scope]);

        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        $query = static::getQuery($params);
        $list = ArrayHelper::toArray($query->all());

        // foreach ($list as &$value) {
        // $value['cus_mobile'] = ResultHelper::mobileEncryption($value['cus_mobile']);
        // }

        $statusList = [
            OrderHeaderStatusEnum::STATUS_COMPLETED,
            OrderHeaderStatusEnum::STATUS_OTHER_SETTLEMENT,
        ];

        $newList = $storeCustomerList = [];
        foreach ($list as $value) {
            if (empty($newList[$value['store_id']])) {
                $newList[$value['store_id']] = [
                    'store_id' => $value['store_id'],
                    'store_name' => $value['store_name'],
                    'unfinished' => 0,
                ];
            }
            $newList[$value['store_id']]['details'][] = $value;

            //统计未做人数
            if (
                $value['reach_status'] == CustomerChurnRemarkReachStatusEnum::REACH_STORE_NOT_DONE
                && !in_array($value['order_status'], $statusList)
                && !in_array($value['cus_id'], $storeCustomerList[$value['store_id']] ?: [])
            ) {
                $newList[$value['store_id']]['unfinished'] += 1;
                $storeCustomerList[$value['store_id']][] = $value['cus_id'];
            }
        }

        sort($newList);

        return [$newList, 0];
    }

    /**
     * 导出列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function export($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;
        $query = static::getQuery($params);
        $query->offset($offset)->limit($limit);

        $list = ArrayHelper::toArray($query->all());
        return [$list, 0];
    }

    /**
     * 新增
     *
     * @param array $params
     * @return bool
     */
    public static function create($params)
    {
        $order = OrderHeader::find()->select('id,cus_id,plan_time,order_status')->where(['id' => $params['order_id']])->one();
        if (!$order) {
            throw new Exception('订单不存在');
        }
        $plan_time_start = strtotime(DateHelper::toDate($order->plan_time, 'Y-m-d'));
        $plan_time_end = $plan_time_start + 86400 - 1;

        $model = CustomerChurnRemark::find()->where(['order_id' => $params['order_id']])
            ->andWhere(['between', 'plan_time', $plan_time_start, $plan_time_end])
            ->one();

        if (!$model) {
            $model = new static::$modelClass();
        } else {
            if ($model->store_real_remark) {
                throw new Exception('该记录门店已经核实，不允许在操作');
            }
        }

        $model->attributes = $params;
        $model->reach_status = CustomerChurnRemarkReachStatusEnum::REACH_STORE_NOT_DONE;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrMsg());
        }

        //设置客户首次到店时间
        \auth\services\CustomerService::setCustomerFirstVisitTime($order->cus_id);
        return $model;
    }

    /**
     * 取消到店未做
     *
     * @param $id
     * @param array $params
     * @return bool
     * @throws Exception
     */
    public static function cancelReachStoreRemark($id, $params = [])
    {
        $remark = static::$modelClass::find()
            ->andWhere(['id' => $id])
            ->one();

        if (empty($remark)) {
            throw new Exception('查无次备注记录');
        }

        if ($remark->reach_status == CustomerChurnRemarkReachStatusEnum::ALREADY_DONE) {
            throw new Exception('该记录已取消到店未做，无需重复取消');
        }

        if ($remark->store_real_remark) {
            throw new Exception('该记录门店已经核实，不允许在操作');
        }

        $edit_remark = trim($params['content']);
        if (empty($edit_remark)) {
            throw new Exception('取消原因不能为空');
        }
        $remark->scenario = 'cancle';
        $remark->edit_remark = $edit_remark;
        $remark->reach_status = CustomerChurnRemarkReachStatusEnum::ALREADY_DONE;
        if (!$remark->save()) {
            throw new Exception($remark->getFirstErrMsg());
        }

        $cus_id = OrderHeader::find()->select('cus_id')->where(['id' => $remark->order_id])->scalar();
        //设置客户首次到店时间
        \auth\services\CustomerService::setCustomerFirstVisitTime($cus_id);

        return true;
    }

    /**
     * 门店-记录客户到店未做原因
     *
     * @param $id
     * @param array $params
     * @return bool
     * @throws Exception
     */
    public static function storeRealRemark($id, $params = [])
    {
        $remark = static::$modelClass::find()
            ->andWhere(['id' => $id])
            ->one();

        if (empty($remark)) {
            throw new Exception('查无次备注记录');
        }

        if ($remark->reach_status == CustomerChurnRemarkReachStatusEnum::ALREADY_DONE && !$remark->store_real_remark) {
            throw new Exception('该记录已取消，不可备注');
        }

        $content = trim($params['content']);
        if (empty($content)) {
            throw new Exception('到店未做内容不能为空');
        }
        $remark->scenario = 'store';
        $remark->store_real_remark = $content;
        $remark->plan_teacher_id = $params['plan_teacher_id'];
        $remark->reach_status = $params['reach_status'];
        if (!$remark->save()) {
            throw new Exception($remark->getFirstErrMsg());
        }

        if ($remark->reach_status == CustomerChurnRemarkReachStatusEnum::ALREADY_DONE) {
            $cus_id = OrderHeader::find()->select('cus_id')->where(['id' => $remark->order_id])->scalar();
            //设置客户首次到店时间
            \auth\services\CustomerService::setCustomerFirstVisitTime($cus_id);
        }
        return true;
    }

    /**
     * 设置门店业绩分析-流失人数
     */
    public static function setStoreLossNum($startTime, $endTime, int $entityId, $storeId = null)
    {
        $condition = ['date' => $startTime, 'entity_id' => $entityId];
        if ($storeId) {
            $condition['store_id'] = $storeId;
        }

        StorePerformanceAnalysis::updateAll(['loss_num' => 0], $condition);

        $data = static::getStoreLossNum($startTime, $endTime, $entityId, $storeId);
        if (empty($data)) {
            return true;
        }

        foreach ($data as $item) {
            $model = StorePerformanceAnalysis::find()
                ->andWhere([
                    'store_id' => $item['store_id'],
                    'date' => $startTime,
                    'entity_id' => $entityId
                ])
                ->one();

            if (empty($model)) {
                $model = new StorePerformanceAnalysis();
                $model->date = $startTime;
                $model->store_id = $item['store_id'];
                $model->entity_id = $entityId;
            }

            $model->loss_num = $item['loss_cus_num'];
            if (!$model->save()) {
                throw new Exception(current($model->getFirstErrors()));
            }
        }

        return true;
    }

    /**
     * 获取门店流失人数
     */
    public static function getStoreLossNum($startTime, $endTime, int $entityId, $storeId = null)
    {
        $query = CustomerChurnRemark::find()
            ->alias('ccr')
            ->select([
                'oh.store_id',
                'COUNT( DISTINCT oh.cus_id ) AS loss_cus_num'
            ])
            ->leftJoin(['oh' => OrderHeader::tableName()], 'oh.id = ccr.order_id')
            ->where([
                'and',
                ['ccr.reach_status' => 2],
                ['ccr.entity_id' => $entityId],
                ['between', 'ccr.plan_time', $startTime, $endTime]
            ])->andFilterWhere(['oh.store_id' => $storeId]);

        $list = $query->groupBy('oh.store_id')
            ->asArray()
            ->all();

        if (empty($list)) {
            return [];
        }

        return $list;
    }
}
