# Agent Configuration for ERP SaaS Platform

## Quick Commands
```bash
# Backend (PHP/Yii2)
php init                           # Initialize environment (choose 0)
php composer install              # Install dependencies
php yii migrate                    # Run migrations
php yii queue/listen               # Start main queue worker (REQUIRED)
vendor/bin/codecept run            # Run all tests
vendor/bin/codecept run frontend   # Run frontend tests only
vendor/bin/codecept run backend    # Run backend tests only

# Frontend (manageSystem - Vue 2)
npm run mac-serve                  # Development (macOS)
npm run serve                     # Development (Windows)
npm run mac-build                 # Production (macOS)

# Frontend (client - Vue 3)
cd client && npm run serve        # Customer-facing app
```

## Architecture
- **Multi-app structure**: `backendapi/` (API), `manageSystem/` (Vue 2 admin), `client/` (Vue 3 customer)
- **Service layer**: Controllers → Services (`services/Application.php`) → Models (`common/models/`)
- **Database**: MySQL with `erp_` prefix, snake_case, standard fields (`id`, `status`, `created_at`, `updated_at`, `deleted_at`)
- **Queue system**: Redis-based with `queueHigh`/`queue` for async operations (data sync, notifications, reports)

## Code Style
- **API responses**: Use `code` field (not `status`): `['code' => 200, 'message' => 'success', 'data' => $result]`
- **Models**: Extend `BaseModel` with soft delete, Controllers extend `BaseController`
- **Frontend**: Vue 2 build - avoid ES2020+ features (no `?.`, `??`, `||=`)
- **JeecgListMixin**: `queryParam` must be defined in `data()` with string values only
- **Language**: Chinese for comments, commit messages, user communication
- **Migrations**: Always implement both `up()` and `down()` methods
- **Queue jobs**: Essential for system operation - must be running for proper functionality
