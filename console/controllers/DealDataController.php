<?php

/**
 * 数据处理
 */

namespace console\controllers;

use common\cache\DepartmentCache;
use common\components\DingTalk;
use common\components\feishu\process\ProcurementDailyProcess;
use common\components\feishu\process\ProcurementSpecialProcess;
use common\components\report\ReportFactory;
use common\enums\GoodsTypeEnum;
use common\enums\MemberTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\order\RelayAdsFromEnum;
use common\enums\StatusEnum;
use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\helpers\Tool;
use common\models\backend\Member;
use common\models\backend\order\OrderHeader;
use common\models\backendapi\PromoteChannel;
use common\models\common\AdsAccountData;
use common\models\common\AdsAccountDataCity;
use common\models\common\AdsAccountProgram;
use common\models\common\AdsAccountSub;
use common\models\common\AdsMaterial;
use common\models\common\AdsMaterialRelate;
use common\models\common\ApiLog;
use common\models\common\Department;
use common\models\common\DepartmentAssignment;
use common\models\common\DingtalkDept;
use common\models\common\Entity;
use common\models\common\LandPageStatisticsDetails;
use common\models\Customer;
use common\models\goods\OrderRelay;
use common\models\log\WecomCallback;
use common\models\member\DingtalkUser;
use common\models\member\DingtalkUserAbnormalLog;
use common\models\wxcom\CusCustomerUser;
use common\models\wxcom\DynamicMapping;
use common\queues\UpdateCustomerReportInfoJob;
use common\services\member\DingtalkUserService;
use console\models\MaterialInventory;
use console\models\MaterialInventoryRecord;
use console\models\Procurement;
use console\services\DealDataService;
use Exception;
use Yii;
use yii\console\Controller;

class DealDataController extends Controller
{

    /**
     * 删除-库存列表重复问题
     *
     * @throws \yii\base\InvalidConfigException
     */
    public function actionDelList()
    {
        $list = MaterialInventory::find()
            ->select('material_id,bar_code,dept_id,dept_name,COUNT(id) as num')->groupBy('material_id,dept_id')
            ->having('COUNT(id) > 1')->orderBy('dept_id asc')
            ->asArray()
            ->all();

        $del_ids = [];
        foreach ($list as $item) {
            $info = MaterialInventory::find()->select('id,num')->where(['material_id' => $item['material_id'], 'dept_id' => $item['dept_id']])->asArray()->all();
            if (count($info) < 2) continue;
            if ($info[0]['num'] > $info[1]['num']) {
                $del_id = $info[1]['id'];
            } else {
                $del_id = $info[0]['id'];
            }

            $del_ids[] = $del_id;
        }
        if ($del_ids) MaterialInventory::deleteAll(['id' => $del_ids]);
        echo 'success';
    }

    /**
     * 删除-库存明细重复问题
     *
     * @throws \yii\base\InvalidConfigException
     */
    public function actionDelDetail()
    {
        $list = MaterialInventoryRecord::find()
            ->select('material_id,bar_code,in_dept_name,out_dept_name,type,logistics_no,created_by,num,FROM_UNIXTIME(created_at) as created_at')
            ->groupBy('material_id,type,created_at,logistics_no,num,created_by')
            ->having('COUNT(id) > 1')->orderBy('type asc')
            ->asArray()
            ->all();

        $del_ids = [];
        foreach ($list as $item) {
            $where = [];
            if ($item['in_dept_name']) {
                $where['in_dept_name'] = $item['in_dept_name'];
            } else {
                $where['out_dept_name'] = $item['out_dept_name'];
            }

            $info = MaterialInventoryRecord::find()->select('id')
                ->where(['material_id' => $item['material_id'], 'logistics_no' => $item['logistics_no'], 'num' => $item['num'], 'type' => $item['type'], 'created_by' => $item['created_by']])
                ->andWhere($where)
                ->orderBy('id desc')
                ->asArray()->all();

            if (count($info) != 2) continue;

            $del_ids[] = $info[0]['id'];
        }

        if ($del_ids) MaterialInventoryRecord::deleteAll(['id' => $del_ids]);
        echo 'success';
    }

    /**
     * 维护城市消耗数据
     */
    public function actionDealCityData()
    {
        $startTimeK = time();
        //项目、链路、责任人ID、部门ID
        $list = AdsAccountSub::find()->select('id,responsible_id,link_id,project_id,dept_id,rebates')->asArray()->all();
        foreach ($list as $item) {
            AdsAccountDataCity::updateAll(['responsible_id' => $item['responsible_id'], 'link_id' => $item['link_id'], 'project_id' => $item['project_id'], 'dept_id' => $item['dept_id'], 'rebates' => $item['rebates']], ['ads_sub_id' => $item['id']]);
        }

        $endTimeK = time();
        echo '执行成功,耗时 => ' . ($endTimeK - $startTimeK) . '秒    ';
    }

    /**
     * 维护历史callback
     */
    public function actionCusCallback()
    {
        echo '执行开始 => ';
        echo PHP_EOL;

        $list = CusCustomerUser::find()->alias('ccu')
            ->select('ccu.id,ccu.add_time,ccu.channel_id,w.unionid')
            ->leftJoin('{{%wxcom_cus_customer}} w', 'w.id = ccu.cus_id')
            ->where(['ccu.channel_id' => 1])
            ->andWhere(['<', 'ccu.id', 1057902])
            ->andWhere(['ccu.callback' => ''])
            ->orderBy('ccu.id desc')
            ->asArray()
            ->all();
        echo '总条数 => ' . count($list);
        echo PHP_EOL;

        $num = 0;
        foreach ($list as $item) {
            echo '执行到id => ' . $item['id'];
            echo PHP_EOL;

            $startTime = strtotime((string)DateHelper::toDate($item['add_time'], 'Y-m-d H') . ':00:00');
            $endTime = $startTime + 86400;

            $landPage = LandPageStatisticsDetails::find()
                ->select('id,params')
                ->where(['between', 'created_at', $startTime, $endTime])
                ->andWhere(['unionid' => $item['unionid'], 'event' => 'addfans'])
                ->asArray()
                ->one();

            if (empty($landPage)) continue;

            $params = json_decode($landPage['params'], true);

            if ($item['channel_id'] == 1) {
                $callback = $params['clickid'];
            } else {
                $callback = $params['callback'];
            }

            if (empty($callback)) continue;

            CusCustomerUser::updateAll(['callback' => $callback], ['id' => $item['id']]);
            $num++;
            echo '执行到成功 => ' . $num;
            echo PHP_EOL;
        }

        echo '执行完成--------------';
    }

    /**
     * 处理每日消耗 定向数据
     */
    public function actionPromoteData()
    {
        $accountSub = AdsAccountSub::find()->select('id,direction_id')->where(['>', 'direction_id', 0])->all();
        $startTime = '********';
        $endTime = '********';
        foreach ($accountSub as $key => $item) {
            $res = AdsAccountData::updateAll(['direction_id' => $item->direction_id], ['and', ['between', 'date', $startTime, $endTime], ['ads_sub_id' => $item->id]]);
            $key++;
            echo $item->id . ',执行第 => ' . $key . '条' . ',修改 => ' . $res . '条';
            echo PHP_EOL;
        }

        echo '执行到成功';
        exit();
    }

    public function actionGetJobnumber()
    {
        $list = Member::find()->all();
        $dingTalk = new DingTalk();
        $num = 1;
        foreach ($list as $model) {
            $userInfo = $dingTalk->getUserInfoByUserID($model->dingtalk_user_id);
            if (empty($userInfo) || $userInfo['errcode'] != 0) {
                continue;
            }
            $model->jobnumber = $userInfo['jobnumber'];
            $model->save();
            echo '完成第' . $num . '个,ID为 => id:' . $model->id . ',用户 => ' . $model->username . '';
            echo PHP_EOL;
            $num++;
        }
    }

    /**
     * 修改新16下定上报数据
     *
     * @throws \Exception
     */
    public function actionOrderReport()
    {
        $orderList = OrderHeader::find()->select('id,cus_id,order_no,customer_user_id')->where(['channel_id' => 2])
            ->andWhere(['order_no' => 'E2022081009491000271609833'])
            ->andWhere(['>=', 'pre_pay_time', 1660060800])->all();

        $num = 1;
        $success_num = 1;
        foreach ($orderList as $order) {
            echo PHP_EOL;
            echo '执行第 => ' . $num . '条';
            $num++;

            $apiLog = ApiLog::find()->where(['type' => 'orderReport', 'content' => '新16上报'])
                ->andWhere(['>', 'created_at', '1660060800'])
                ->andWhere(['like', 'callback_pack', $order->order_no])->one();

            if ($apiLog) continue;

            //订单核销上报
            /**@var $customer_user_data CusCustomerUser */
            $customer_user_data = CusCustomerUser::find()->alias('ccu')
                ->select('ccu.id,ccu.callback,cc.unionid,wc.developer_id')
                ->leftJoin(['{{%wxcom_cus_customer}} cc'], 'cc.id = ccu.cus_id')
                ->leftJoin(['{{%wxcom_com}} wc'], 'wc.id = cc.com_id')
                ->andWhere(['ccu.id' => $order->customer_user_id])
                ->asArray()
                ->one();

            $reportService = ReportFactory::getReportServiceByChannelId($order->channel_id);
            $reportService->orderCreatedReport($order);
            $success_num++;
        }
        echo PHP_EOL;
        echo '总条数 => ' . count($orderList) . '条';
        echo PHP_EOL;
        echo '成功执行 => ' . $success_num . '条';
        exit();
    }

    /**
     * 钉钉部门拉取 php yii deal-data/dept-pull
     */
    public function actionDeptPull($entityId = 1)
    {
        $entity = Entity::findOne($entityId);
        $dingTalk = new dingTalk($entity->code);
        $data = $dingTalk->getDepartment();
        $errCode = $data['errcode'] ?? 1;
        if ($errCode != 0 || empty($data['department'])) {
            $msg = $data['errmsg'] ?? '拉去失败';
            exit($msg);
        }
        $tran = Yii::$app->db->beginTransaction();
        try {
            foreach ($data['department'] as $v) {
                $deptIid = $v['id'] ?? 0;
                $data = DingtalkDept::find()->andWhere(['=', 'dingtalk_dept_id', $deptIid])->asArray()->one();
                if (!empty($data)) {
                    continue;
                }
                $model = new DingtalkDept();
                $model->dingtalk_dept_id = $deptIid;
                $model->dingtalk_dept_name = $v['name'];
                $model->dingtalk_parent_id = $v['parentid'];
                $model->entity_id = $entity->id;
                if (!$model->save()) {
                    $tran->rollBack();
                    throw new Exception($model->getFirstErrMsg());
                }
            }
            $tran->commit();
        } catch (Exception $e) {
            $tran->rollBack();
            throw new Exception($e->getMessage());
        }
        echo "处理成功";
    }

    /**
     * 钉钉用户id拉取  php yii deal-data/dingtalk-user-id-pull
     */
    public function actionDingtalkUserIdPull($entityId = 1)
    {
        $entity = Entity::findOne($entityId);
        $dingTalk = new dingTalk($entity->code);
        $dept = DingtalkDept::find()->select('dingtalk_dept_id')->asArray()->column();
        $userIds = [];
        foreach ($dept as $v) {
            var_dump('处理中');
            $result = $dingTalk->getDepartmentUserId($v);
            $errCode = $result['errcode'] ?? 1;
            $errMsg = $result['errmsg'] ?? '拉取失败';
            if ($errCode != 0) {
                var_dump($errMsg);
                continue;
            }
            $userIdList = $result['result']['userid_list'] ?? [];
            $userIds = array_merge($userIds, $userIdList);
        }
        // 查询钉钉人员信息表避免重复拉取写入
        $dingtalkUserAll = DingtalkUser::find()->select('dingtalk_user_id')->asArray()->column();
        $userIds = array_diff($userIds, $dingtalkUserAll);
        if (empty($userIds)) {
            exit("处理完成");
        }
        $tran = Yii::$app->db->beginTransaction();
        try {
            foreach ($userIds as $v) {
                if (in_array($v, $dingtalkUserAll)) {
                    continue;
                }
                $dingtalkUserAll[] = $v;
                $model = new DingtalkUser();
                $model->dingtalk_user_id = $v;
                $model->member_id = 0;
                if (!$model->save()) {
                    $tran->rollBack();
                    throw new Exception($model->getFirstErrMsg());
                }
            }
            $tran->commit();
        } catch (Exception $e) {
            $tran->rollBack();
            throw new Exception($e->getMessage());
        }

        DepartmentCache::init(1)->setDeptAllPersonNum(1);
        echo "处理成功";
    }

    /**
     * 花名册拉取 php yii deal-data/roster-pull
     */
    public function actionRosterPull($entityId = 1, $where = [])
    {
        $entity = Entity::findOne($entityId);
        $dingTalk = new dingTalk($entity->code);
        $userIds = DingtalkUser::find()->andFilterWhere($where)->select('dingtalk_user_id')->asArray()->column();
        $userIds = array_chunk($userIds, 100);
        $insertUser = [];
        foreach ($userIds as $v) {
            var_dump("处理中");
            $v = implode(',', $v);
            $result = $dingTalk->getDingtalkUserMsg($v);
            $errCode = $result['errcode'] ?? 1;
            $errMsg = $result['errmsg'] ?? '拉取失败';
            if ($errCode != 0) {
                exit($errMsg);
            }
            $data = $result['result'] ?? [];
            $data = DingtalkUserService::rosterData($data);
            $insertUser = array_merge($insertUser, $data);
        }
        DingtalkUserService::setUserMsg($insertUser);
        echo "处理成功";
    }

    /**
     * 头像和Unionid拉取 php yii deal-data/unionid-and-avatar-pull
     */
    public function actionUnionidAndAvatarPull($entityId = 1)
    {
        $entity = Entity::findOne($entityId);
        $userIds = DingtalkUser::find()->select('id,dingtalk_user_id')->asArray()->all();
        $number = count($userIds);
        foreach ($userIds as $v) {
            var_dump($number--);
            $dingTalk = new dingTalk($entity->code);
            $result = $dingTalk->getUserInfoByUserID($v['dingtalk_user_id']);
            if (!$result) {
                continue;
            }
            $model = DingtalkUser::findOne($v['id']);
            $model->head_portrait = $result['avatar'];
            $model->dingtalk_unionid = $result['unionid'];
            $model->save();

            $memberModel = Member::findOne($model->member_id);
            if ($memberModel) {
                if (!empty($memberModel->dingtalk_unionid_id) && $memberModel->dingtalk_unionid_id != $result['unionid']) {
                    echo 'member_id:' . $model->member_id;
                    continue;
                }
                $memberModel->dingtalk_unionid_id = $result['unionid'];
                $memberModel->realname = $result['name'];
                $memberModel->save();
            } else {
                echo 'member_id不存在,dingtalk_user_id => ' . $v['dingtalk_user_id'];
                continue;
            }
        }
        echo "处理成功";
    }

    /**
     * 离职人员拉取  php yii deal-data/resign-pull
     *
     * @param string $startTime
     * @param string $endTime
     * @param int $entityId
     * @throws Exception
     */
    public function actionResignPull($startTime = '', $endTime = '', $entityId = 1)
    {
        if (empty($startTime) || empty($endTime)) {
            $startTime = date("Y-m-d", strtotime("-6 month")) . 'T00:00:00Z';
            $endTime = date("Y-m-d") . 'T00:00:00Z';
        } else {
            $startTime = "{$startTime}T00:00:00Z";
            $endTime = "{$endTime}T23:59:59Z";
        }
        $insertArr = [];
        $nextToken = 0;
        $entity = Entity::findOne($entityId);
        for ($i = 1000; $i > 0; $i--) {
            var_dump("数据处理中");
            $dingTalk = new dingTalk($entity->code);
            $resignMsg = $dingTalk->getResignMsgByUserIds($startTime, $endTime, $nextToken);
            $records = $resignMsg['records'] ?? [];
            if (empty($records)) {
                break;
            }
            $insertArr = array_merge($insertArr, $records);
            $nextToken = $resignMsg['nextToken'] ?? '';
            if (empty($nextToken)) {
                break;
            }
        }
        if (empty($insertArr)) {
            exit("数据为空");
        }

        $dingtalkUsers = DingtalkUser::find()->select('dingtalk_user_id')->asArray()->column();
        foreach ($insertArr as $v) {
            $dingtalkUserId = $v['userId'] ?? '';
            if (in_array($dingtalkUserId, $dingtalkUsers)) {
                continue;
            }
            $dingtalkUsers[] = $dingtalkUserId;
            $member = Member::find()->where(['=', 'dingtalk_user_id', $dingtalkUserId])->one();
            $dingtalkUser = new DingtalkUser();
            $dingtalkUser->dingtalk_user_id = $dingtalkUserId;
            $dingtalkUser->member_id = $member->id ?? 0;
            $dingtalkUser->name = $v['name'] ?? '';
            $dingtalkUser->mobile = $v['mobile'] ?? '';
            $dingtalkUser->status = 0;
            $dingtalkUser->save();
        }
        echo "处理成功";
    }


    /**
     * php yii deal-data/number-people-statistics
     */
    public function actionNumberPeopleStatistics()
    {
        $member = Member::find()->select('id')->where(['=', 'status', 1])->asArray()->column();
        $all = count($member);
        foreach ($member as $v) {
            var_dump($all--);
            $departmentAssignment = DepartmentAssignment::find()->where(['=', 'user_id', $v])->one();
            if (empty($departmentAssignment)) {
                $model = new DepartmentAssignment();
                $model->dept_id = 1;
                $model->user_id = $v;
                $model->save();
            }
        }
        DepartmentCache::init()->resetDeptPerson();
        echo "处理成功";
    }

    /**
     * 数据矫正 php yii deal-data/data-correction
     */
    public function actionDataCorrection($memberId = 0)
    {
        $member = Member::findOne($memberId);
        if (empty($member)) {
            exit('数据为空');
        }
        $model = new DingtalkUser();
        $model->dingtalk_user_id = $member->dingtalk_user_id;
        $model->member_id = $member->id;
        $model->save();
        $this->actionRosterPull(1, ['in', 'dingtalk_user_id', $member->dingtalk_user_id]);
        echo "处理成功";
    }

    /**
     * 转单信息记录表处理
     *
     * @throws \yii\db\Exception
     */
    public function actionOrderRely()
    {
        $data = [
            ["order_no" => "E2022111510315700317795366", "system" => "lzn", "other_no" => "P20221115103159330"],
            ["order_no" => "E2022111511053500317994950", "system" => "lzn", "other_no" => "P20221115110538330"],
            ["order_no" => "E2022111511060000317182479", "system" => "lzn", "other_no" => "P20221115110604330"],
        ];

        $i = 0;
        foreach ($data as $value) {
            $order = OrderHeader::find()
                ->select('id,order_no,pre_pay_time,created_by')
                ->andWhere(['order_no' => $value['order_no']])
                ->asArray()
                ->one();
            if (empty($order)) {
                continue;
            }
            $order['order_no'] = $value['order_no'];
            $order['other_no'] = $value['other_no'];
            $order['system'] = $value['system'];

            $orderRelay = OrderRelay::find()->where(['order_id' => $order['id']])->one();
            if ($orderRelay) {
                continue;
            }

            $otherOrder = Yii::$app->{$order['system']}->createCommand("SELECT * FROM `qd_order_header` WHERE `order_no`=:order_no")
                ->bindValue(':order_no', $order['other_no'])->queryOne();
            if (empty($otherOrder)) {
                continue;
            }

            if ($otherOrder['mix_id'] > 0) {
                $goodsType = GoodsTypeEnum::PACKAGE;
                $product = Yii::$app->{$order['system']}->createCommand("SELECT * FROM `qd_product_mix` WHERE `id`=:id")
                    ->bindValue(':id', $otherOrder['mix_id'])->queryOne();

                $goodsName = $product['mix_name'];
            } else {
                $goodsType = GoodsTypeEnum::PROJECT;
                $orderDetail = Yii::$app->{$order['system']}->createCommand("SELECT * FROM `qd_order_detail` WHERE `order_id`=:order_id")
                    ->bindValue(':order_id', $otherOrder['id'])->queryOne();

                $product = Yii::$app->{$order['system']}->createCommand("SELECT * FROM `qd_product` WHERE `id`=:id")
                    ->bindValue(':id', $orderDetail['product_id'])->queryOne();

                $goodsName = $product['title'];
            }

            $amount = $product['special_price'] ? $product['special_price'] : $product['normal_price'];
            $relayGoodsList = [
                'id' => $product['id'],
                'name' => $goodsName,
                'price_one' => $amount,
                'goods_type' => $goodsType,
                'deposit' => $otherOrder['account_reduce'],
            ];
            $orderRelay = [
                'order_id' => $order['id'],
                'third_order_no' => $order['other_no'],
                'created_by' => $order['created_by'],
                'created_at' => $order['pre_pay_time'],
                'entity_id' => 1,
                'system_code' => $order['system'],
                'third_order_id' => $otherOrder['id'],
                'hour' => 0,
                'date' => $order['pre_pay_time'],
                'goods_names' => $goodsName,
                'amount' => $amount,
                'relay_goods_list' => json_encode($relayGoodsList)
            ];

            $orderRelayModel = new OrderRelay();
            $orderRelayModel->attributes = $orderRelay;
            $orderRelayModel->save();
            $i++;
            echo "订单号:{$order['order_no']}";
            echo "，已执行第:{$i}条";
            echo "\r\n";
        }
    }

    /**
     * 补充客户首次到店时间
     *
     * @param null $startId 开始ID
     * @param null $endId   结束ID
     * @throws Exception
     */
    public static function actionCorrectionFirstStoreTime($startId = NULL, $endId = NULL)
    {
        $redis = Yii::$app->redis;
        $customerList = Customer::find()
            ->andWhere(['first_store_time' => 0])
            ->andFilterWhere(['BETWEEN', 'id', $startId, $endId])
            ->all();

        $sumNum = count($customerList);
        foreach ($customerList as $customer) {
            echo PHP_EOL;
            print_r("剩余 => {$sumNum}条数据未处理");
            $sumNum--;

            if ($redis->get('CustomerFirstStoreTime:' . $customer->mobile)) {
                continue;
            }

            $result = \auth\services\CustomerService::getBisCustomerFirstStoreTime($customer->mobile);
            if ($result && $result['first_store_time']) {
                $customer->first_store_time = $result['first_store_time'];
                if (!$customer->save()) {
                    echo PHP_EOL;
                    print_r($customer->getFirstErrMsg());
                }
            } else {
                $redis->set('CustomerFirstStoreTime:' . $customer->mobile, $customer->mobile);
                $redis->EXPIRE('CustomerFirstStoreTime:' . $customer->mobile, 3600 * 24);
            }
        }

        echo PHP_EOL;
        print_r("全部执行完毕");
        exit;
    }

    //新16上报数据advertise_id platform补加
    public function actionWecomCallbackA()
    {
        $data = WecomCallback::find()->andWhere(['=', 'log_id', 0])->andWhere(['=', 'platform', ''])->limit(1000)->all();
        $sub = AdsAccountSub::find()->alias('as')
            ->select('as.sub_advertiser_id,a.platform')
            ->leftJoin(['{{%ads_account}} a'], 'a.id = as.td_id')->asArray()->all();
        $sub = ArrayHelper::index($sub, 'sub_advertiser_id');
        foreach ($data as $value) {
            if ($value->advertiser_id) {
                $value->platform = $sub[$value->advertiser_id]['platform'];
            }
            $value->save();
            echo $value->id;
            echo "\r\n";
        }
    }

    //表wecome content字段 json字段转text数据处理
    public function actionWecomeCallbackJson()
    {
        $data = WecomCallback::find()->andWhere(['<', 'id', 28])->all();
        foreach ($data as $value) {
            $value->content = json_decode($value->content, true);
            $value->save();
        }
    }

    /**
     * 指定企业、userID补充在职人员数据
     * php yii deal-data/dingtalk-personage-msg-pull qiandai *****************
     * @param string $entityCode
     * @param $userID
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function actionDingtalkPersonageMsgPull($entityCode = 'qiandai', $userID)
    {
        $entity = Entity::findOne(['dingTalk_key' => 'qiandai']);
        $dingTalk = new DingTalk($entityCode);
        $userInfo = $dingTalk->getUserInfoByUserID($userID);
        if ($userInfo['errcode'] != 0) {
            exit('用户不存在该企业');
        }
        $member = Member::find()->where(['dingtalk_user_id' => $userID, 'dingtalk_unionid_id' => $userInfo['unionid'] ?? ''])->one();
        if ($member) {
            exit('该用户信息以存在');
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $entityId = $entity->id ?? 0;
            $jobNumber = $userInfo['jobnumber'] = DingtalkUserService::getJObNumber($userInfo, 0, $entityCode, $entityId);
            $member = new Member();
            $member->username = $userInfo['name'] ?? '';
            $member->realname = $userInfo['name'] ?? '';
            $member->mobile = $userInfo['mobile'];
            $member->email = $userInfo['email'];
            $member->jobnumber = $jobNumber;
            $member->head_portrait = $userInfo['avatar'] ?? '';
            $member->dingtalk_unionid_id = $userInfo['unionid'] ?? '';
            $member->dingtalk_user_id = $userID;
            $member->status = StatusEnum::ENABLED;
            $member->type = MemberTypeEnum::GENERAL_ADMIN;
            $member->current_entity_id = $entityId;
            if (!$member->save()) throw new Exception('新增用户失败，请联系管理员');
            // 兼容离职人员重新入职
            $isExist = DingtalkUser::findOne(['member_id' => $member->id,  'dingtalk_user_id' => $userID]);
            if (empty($isExist)) {
                $dingtalkUser = new DingtalkUser();
                $dingtalkUser->member_id = $member->id;
                $dingtalkUser->dingtalk_user_id = $userInfo['userid'];
                if (!$dingtalkUser->save()) throw new Exception('新增用户钉钉信息失败，请联系管理员');
            }
            $result = $dingTalk->getDingtalkUserMsg($userInfo['userid']);
            $result = $result['result'] ?? [];
            $data = DingtalkUserService::rosterData($result);
            DingtalkUserService::setUserMsg($data);
            $abnorma = new DingtalkUserAbnormalLog();
            $abnorma->dingtalk_user_id = $userInfo['userid'];
            $abnorma->dept_id = (string)$userInfo['department'][0] ?? 0;
            $abnorma->position = $userInfo['position'] ?? '';
            $abnorma->remark = '新人入职';
            if (!$abnorma->save()) throw new Exception('新增用户异常记录失败，请联系管理员');
            $hiredDate = !empty($userInfo['hiredDate']) ? substr($userInfo['hiredDate'], 0, 10) : '';
            if ($hiredDate) {
                $abnorma->created_at = $hiredDate;
                $abnorma->save();
            }
            //默认部门分配
            $deptIds = Department::find()->select('id')->where(['entity_id' => $entityId, 'pid' => 0, 'deleted_at' => 0])->asArray()->one();
            if (!$deptIds) throw new Exception('分配的部门不存在，请重试');
            $model = new DepartmentAssignment();
            $deptResult = $model->assign($deptIds, $member->id);
            $transaction->commit();
            $model->eventUserAssignDept($deptResult);
            echo "处理完成";
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }


    /**
     * 矫正历史已完成、售后服务的订单项目分摊实收
     * 命令：yii deal-data/order-project
     */
    public function actionOrderProject()
    {
        $orderList = OrderHeader::find()
            ->andWhere([
                'order_status' => [
                    OrderHeaderStatusEnum::STATUS_COMPLETED,
                    OrderHeaderStatusEnum::STATUS_AFTER_SALE,
                ]
            ])
            ->all();

        foreach ($orderList as $order) {
            //重新计算订单项目分摊
            DealDataService::caleOrderProject($order);
        }

        print_r('矫正历史已完成、售后服务的订单项目分摊实收完成');
        exit;
    }

    public function actionCusAdid()
    {
        $list = CusCustomerUser::find()->where(['=', 'adid', ''])
            ->andWhere(['<>', 'callback', ''])
            ->andWhere(['>=', 'id', 6622])
            ->orderBy('id asc')
            ->all();

        if (empty($list)) {
            echo '没有数据可执行';
            exit();
        }

        $count = count($list);
        foreach ($list as &$cus) {
            echo '执行第' . $count . '条' . PHP_EOL;
            $count--;
            $mapping = DynamicMapping::find()->where(['like', 'content', $cus->callback])->asArray()->one();
            if (empty($mapping)) {
                echo 'ID:' . $cus->id . '不存在callback' . PHP_EOL;
                continue;
            }

            $content = json_decode($mapping['content'], true);
            echo 'cus表ID：' . $cus->id . ',mapping表ID：' . $mapping['id'] . PHP_EOL;
            $params = json_decode($content['params'], true);


            $adid = '';
            if (isset($params['adid'])) {
                $adid = $params['adid'] ?: '';
            }

            if (isset($params['promotionid'])) {
                $adid = $params['promotionid'] ?: '';
            }

            if (empty($adid)) {
                echo 'cus表ID：' . $cus->id . ',mapping表ID：' . $mapping['id'] . ',adid不存在' . PHP_EOL;
            }

            $changeData = [
                'adid' => $adid,
                'platform' => 'tiktok',
                'wxcomCusId' => $mapping['external_user_id'],
                'wxcomUserId' => $mapping['wxcom_user_id'],
                'callback' => $cus->callback,
                'cl_code' => $params['cl'],
                'ua' => $params['ua'],
            ];

            UpdateCustomerReportInfoJob::addJob($changeData);
        }
    }

    /**
     * 采购申请单-重新发起
     */
    public function actionFeishuCaigou()
    {
        $id = '5';
        $apiLog = ApiLog::findOne($id);
        $arrData = json_decode($apiLog->content, true);

        $procurement = Procurement::findOne($arrData['procurement_id']);
        if (empty($procurement)) {
            echo '数据不存在';
            exit;
        }

        if ($apiLog->is_used != 0) {
            echo '该数据已被执行过，请勿重复执行';
            exit;
        }

        if ($procurement->type == Procurement::INVENTORY_TAG) {
            $feishu = ProcurementDailyProcess::create($arrData['data'], 1);
        } else {
            $feishu = ProcurementSpecialProcess::create($arrData['data']);
        }

        if ($feishu['code'] == 0) {
            $procurement->process_instance_id = $feishu['data']['instance_code'];
            $procurement->save();

            $apiLog->is_used = 1;
            $apiLog->save();

            echo '重新生产审批成功';
        } else {
            echo '失败：' . $feishu['msg'];
        }
        exit;
    }

    /**
     * 初始化素材数据
     */
    public function actionAdMaterial()
    {
        $list = AdsMaterialRelate::find()->select('material_id,video_img,entity_id,created_at,updated_at')->groupBy('material_id')->asArray()->all();

        $count = count($list);
        echo '总条数：' . $count . PHP_EOL;
        foreach ($list as $item) {
            echo '剩余：' . $count-- . PHP_EOL;
            $material = AdsMaterial::findOne(['material_id' => $item['material_id']]);
            if ($material) {
                continue;
            }

            $material = new AdsMaterial();
            $material->material_id = $item['material_id'];
            $material->video_img = $item['video_img'];
            $material->entity_id = $item['entity_id'];
            $material->created_at = $item['created_at'];
            $material->updated_at = $item['updated_at'];
            $material->save();

            AdsMaterialRelate::updateAll(['material_id' => $material->id], ['material_id' => $item['material_id']]);
        }

        echo '执行完成';
    }

    /**
     * 批量设置客户到店访问时间
     * 
     * php yii deal-data/set-cus-visit-time
     */
    public function actionSetCusVisitTime()
    {
        $sql = "
            SELECT 
                customer_id,
                MIN(first_plan_time) AS first_visit_time
            FROM (
            -- 从订单表中获取已完成订单的预约时间
            SELECT 
                cus_id AS customer_id, 
                plan_time AS first_plan_time
            FROM erp_order_header
            WHERE order_status = 5

            UNION ALL

            -- 从流失表关联订单表获取客户 ID 和预约时间
            SELECT 
                o.cus_id AS customer_id, 
                c.plan_time AS first_plan_time
            FROM erp_customer_churn_remark c
            JOIN erp_order_header o ON c.order_id = o.id
                WHERE c.reach_status = 2
            ) AS combined_data
            GROUP BY customer_id
            ORDER BY customer_id asc;
        ";

        $list = Yii::$app->db->createCommand($sql)->queryAll();
        if (empty($list)) {
            echo "没有数据";
            exit;
        }

        $count = count($list);
        echo "一共有{$count}条数据" . PHP_EOL;
        foreach ($list as $k => $v) {
            echo "剩余：" . $count . '需要处理' . PHP_EOL;
            $count--;
            $customer = Customer::find()
                ->andWhere(['id' => $v['customer_id']])
                ->one();

            if ($customer->first_visit_time == $v['first_visit_time']) {
                continue;
            }

            $customer->first_visit_time = $v['first_visit_time'];
            if (!$customer->save(false)) {
                echo '用户Id:' . $v['customer_id'] . "更新失败";
                exit;
            }
        }

        echo "已全部执行完成";
        exit;
    }
}
