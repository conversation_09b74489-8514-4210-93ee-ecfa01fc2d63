<?php

use common\components\migrate\Migration;

/**
 * Class m250724_131633_add_ads_transfer_money_record
 */
class m250724_131633_add_ads_transfer_money_record extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_ads_transfer_money_record` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `serial_number` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '充值序号' COLLATE 'utf8_general_ci',
                `user_id` INT(11) NOT NULL DEFAULT 0 COMMENT '用户ID，backend_member表ID',
                `user_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '用户姓名' COLLATE 'utf8_general_ci',
                `transfer_advertiser_id` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备用金账户ID，ads_main_body表sub_advertiser_id' COLLATE 'utf8_general_ci',
                `target_advertiser_id` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '广告子账户ID，ads_account_sub表sub_advertiser_id' COLLATE 'utf8_general_ci',
                `platform` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '充值平台' COLLATE 'utf8_general_ci',
                `amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
                `status` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '充值状态',
                `entity_id` INT(11) NOT NULL DEFAULT 0 COMMENT '企业ID',
                `created_at` INT(11) NOT NULL DEFAULT 0 COMMENT '充值时间',
                PRIMARY KEY (`id`),
                INDEX `serial_number` (`serial_number`),
                INDEX `user_id` (`user_id`),
                INDEX `platform` (`platform`),
                INDEX `status` (`status`)
            )
            COMMENT='广告预算充值记录'
            COLLATE='utf8_general_ci'
            ;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250724_131633_add_ads_transfer_money_record cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250724_131633_add_ads_transfer_money_record cannot be reverted.\n";

        return false;
    }
    */
}
