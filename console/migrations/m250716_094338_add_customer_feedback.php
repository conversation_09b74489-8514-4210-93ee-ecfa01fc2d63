<?php

use common\components\migrate\Migration;

/**
 * Class m250716_094338_add_customer_feedback
 */
class m250716_094338_add_customer_feedback extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_customer_feedback` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `order_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '订单ID，order_header表ID',
                `cus_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '客户ID，customer表ID',
                `plan_time` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '到店时间，order_header表plan_time',
                `store_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '门店ID，store表ID',
                `age_bracket` TINYINT(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '年龄段',
                `feedback` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '反馈内容' COLLATE 'utf8_general_ci',
                `images` TEXT NOT NULL COMMENT '图片' COLLATE 'utf8_general_ci',
                `entity_id` INT(11) UNSIGNED NOT NULL DEFAULT '1' COMMENT '企业ID',
                `created_at` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
                `created_by` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
                PRIMARY KEY (`id`),
                INDEX `order_id` (`order_id`),
                INDEX `cus_id` (`cus_id`),
                INDEX `store_id` (`store_id`)
            )
            COMMENT='客资反馈表'
            COLLATE='utf8_general_ci'
            ;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250716_094338_add_customer_feedback cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250716_094338_add_customer_feedback cannot be reverted.\n";

        return false;
    }
    */
}
