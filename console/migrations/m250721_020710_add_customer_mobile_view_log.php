<?php

use common\components\migrate\Migration;

/**
 * Class m250721_020710_add_customer_mobile_view_log
 */
class m250721_020710_add_customer_mobile_view_log extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_customer_mobile_view_log` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `mobile` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '手机号' COLLATE 'utf8_general_ci',
                `order_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '订单ID，order_header表ID',
                `customer_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '客户ID, customer表ID',
                `store_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '门店ID, store表ID',
                `created_by` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '查看人ID，backend_member表ID',
                `created_at` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `customer_id` (`customer_id`) USING BTREE,
                INDEX `order_id` (`order_id`) USING BTREE,
                INDEX `mobile` (`mobile`) USING BTREE
            )
            COMMENT='客户手机号查看日志'
            COLLATE='utf8_general_ci'
            ENGINE=InnoDB
            ;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250721_020710_add_customer_mobile_view_log cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250721_020710_add_customer_mobile_view_log cannot be reverted.\n";

        return false;
    }
    */
}
