<?php

use common\components\migrate\Migration;

/**
 * Class m250724_031726_add_ads_advertising_budget
 */
class m250724_031726_add_ads_advertising_budget extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_ads_advertising_budget` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `promote_user_id` INT(11) UNSIGNED NOT NULL COMMENT '推广人员ID，backend_member表ID',
                `budget` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '每日预算',
                `platform` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '广告平台' COLLATE 'utf8_general_ci',
                `entity_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '企业ID',
                `created_by` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
                `created_at` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
                `updated_by` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
                `updated_at` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
                PRIMARY KEY (`id`)
            )
            COMMENT='广告预算表'
            COLLATE='utf8_general_ci'
            ;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250724_031726_add_ads_advertising_budget cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250724_031726_add_ads_advertising_budget cannot be reverted.\n";

        return false;
    }
    */
}
