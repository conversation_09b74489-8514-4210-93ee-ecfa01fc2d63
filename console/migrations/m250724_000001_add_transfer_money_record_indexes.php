<?php

/**
 * 数据库迁移脚本
 * 为 erp_ads_transfer_money_record 表创建性能优化索引
 */

use yii\db\Migration;

class m250724_000001_add_transfer_money_record_indexes extends Migration
{
    public function up()
    {
        // 目标账户ID + 创建时间索引（用于小时限额检查）
        $this->createIndex(
            'idx_target_advertiser_created',
            '{{%ads_transfer_money_record}}',
            ['target_advertiser_id', 'created_at']
        );

        // 状态 + 创建时间索引（用于成功记录查询）
        $this->createIndex(
            'idx_status_created',
            '{{%ads_transfer_money_record}}',
            ['status', 'created_at']
        );

        // 平台 + 创建时间索引（用于平台统计）
        $this->createIndex(
            'idx_platform_created',
            '{{%ads_transfer_money_record}}',
            ['platform', 'created_at']
        );

        // 用户名索引（用于加粉充值识别）
        $this->createIndex(
            'idx_user_name',
            '{{%ads_transfer_money_record}}',
            ['user_name']
        );

        // 复合索引：目标账户 + 状态 + 创建时间（最优性能）
        $this->createIndex(
            'idx_target_status_created',
            '{{%ads_transfer_money_record}}',
            ['target_advertiser_id', 'status', 'created_at']
        );
    }

    public function down()
    {
        $this->dropIndex('idx_target_advertiser_created', '{{%ads_transfer_money_record}}');
        $this->dropIndex('idx_status_created', '{{%ads_transfer_money_record}}');
        $this->dropIndex('idx_platform_created', '{{%ads_transfer_money_record}}');
        $this->dropIndex('idx_user_name', '{{%ads_transfer_money_record}}');
        $this->dropIndex('idx_target_status_created', '{{%ads_transfer_money_record}}');
    }
}
