-- 测试不同数据分布下的GROUP_CONCAT行为

-- 测试1：模拟只有一个老师有退款的情况
SELECT '=== 测试1：只有一个老师有退款 ===';

DROP TABLE IF EXISTS test_teacher_refund;
CREATE TEMPORARY TABLE test_teacher_refund (
    teacher_id INT,
    teacher_name VARCHAR(50),
    refund_data TEXT
);

INSERT INTO test_teacher_refund VALUES 
(1, '张老师', '101:1000,102:500'),
(2, '李老师', NULL),
(3, '王老师', NULL);

-- 第一次分组结果
SELECT * FROM test_teacher_refund;

-- 第二次分组（问题代码）
SELECT 
    '' as teacher_id,
    '合计' as teacher_name,
    GROUP_CONCAT(refund_data) as refund_data,
    LENGTH(GROUP_CONCAT(refund_data)) as data_length
FROM test_teacher_refund;

-- 测试2：模拟多个老师都有退款的情况
DELETE FROM test_teacher_refund;
INSERT INTO test_teacher_refund VALUES 
(1, '张老师', '101:1000,102:500'),
(2, '李老师', '103:2000'),
(3, '王老师', '104:1500,105:800');

SELECT '=== 测试2：多个老师都有退款 ===';
SELECT * FROM test_teacher_refund;

SELECT 
    '' as teacher_id,
    '合计' as teacher_name,
    GROUP_CONCAT(refund_data) as refund_data,
    LENGTH(GROUP_CONCAT(refund_data)) as data_length
FROM test_teacher_refund;

-- 测试3：检查分隔符处理
SELECT '=== 测试3：分隔符处理 ===';
SELECT 
    GROUP_CONCAT(refund_data) as normal_concat,
    GROUP_CONCAT(refund_data SEPARATOR ',') as comma_separator,
    GROUP_CONCAT(DISTINCT refund_data) as distinct_concat
FROM test_teacher_refund
WHERE refund_data IS NOT NULL;