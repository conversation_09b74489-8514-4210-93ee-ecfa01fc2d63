<?php

/**
 * 预约订单多维表格统一测试工具 - MVP版本
 * 
 * 支持多种模式：
 * - real: 实际调用模式（默认）
 * - test: 单元测试模式  
 * - full: 完整验证模式
 * - cache: 缓存管理模式
 * 
 * 使用示例：
 * php advance-order-test-tool.php --store_id=1 --customer_name=张三
 * php advance-order-test-tool.php --mode=test
 * php advance-order-test-tool.php --mode=full --store_id=1
 * php advance-order-test-tool.php --mode=cache --action=show --store_id=1
 */

// 引入Yii框架
require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';
require __DIR__ . '/common/config/bootstrap.php';
require __DIR__ . '/console/config/bootstrap.php';

$config = yii\helpers\ArrayHelper::merge(
    require __DIR__ . '/common/config/main.php',
    require __DIR__ . '/common/config/main-local.php',
    require __DIR__ . '/console/config/main.php',
    require __DIR__ . '/console/config/main-local.php'
);

$application = new yii\console\Application($config);

// 导入需要的类
use common\components\feishu\multidimensionalTable\AdvanceOrder;
use common\helpers\ArrayHelper;

/**
 * 预约订单测试工具主类
 */
class AdvanceOrderTestTool
{
    private $config = [];
    private $mode = 'real';
    
    public function __construct()
    {
        // 设置控制台输出编码
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            system('chcp 65001');
        }
    }
    
    /**
     * 主入口
     */
    public function run($argv)
    {
        $this->parseArguments($argv);
        $this->showHeader();
        
        try {
            switch ($this->mode) {
                case 'test':
                    return $this->runTestMode();
                case 'full':
                    return $this->runFullMode();
                case 'cache':
                    return $this->runCacheMode();
                case 'real':
                default:
                    return $this->runRealMode();
            }
        } catch (Exception $e) {
            $this->showError("执行失败", $e);
            return 1;
        }
    }
    
    /**
     * 解析命令行参数
     */
    private function parseArguments($argv)
    {
        // 默认配置
        $this->config = [
            'mode' => 'real',
            'store_id' => 1,
            'customer_name' => '测试客户',
            'mobile' => '13800138000',
            'count' => 1,
            'action' => 'show'
        ];
        
        // 解析参数
        for ($i = 1; $i < count($argv); $i++) {
            $arg = $argv[$i];
            if (strpos($arg, '=') !== false) {
                list($key, $value) = explode('=', $arg, 2);
                $key = ltrim($key, '--');
                if (array_key_exists($key, $this->config)) {
                    $this->config[$key] = $value;
                }
            }
        }
        
        $this->mode = $this->config['mode'];
    }
    
    /**
     * 显示工具头部信息
     */
    private function showHeader()
    {
        echo "🚀 预约订单多维表格统一测试工具 - MVP\n";
        echo "=====================================\n";
        echo "当前模式: " . strtoupper($this->mode) . "\n\n";
    }
    
    /**
     * 实际调用模式
     */
    private function runRealMode()
    {
        echo "📋 实际调用模式 - 向多维表格添加真实测试数据\n";
        echo "配置信息:\n";
        foreach (['store_id', 'customer_name', 'mobile', 'count'] as $key) {
            echo "  {$key}: {$this->config[$key]}\n";
        }
        echo "\n";
        
        $handler = new RealCallHandler($this->config);
        return $handler->execute();
    }
    
    /**
     * 测试验证模式
     */
    private function runTestMode()
    {
        echo "🔍 测试验证模式 - 验证方法逻辑正确性\n\n";
        
        $handler = new TestHandler($this->config);
        return $handler->execute();
    }
    
    /**
     * 完整验证模式
     */
    private function runFullMode()
    {
        echo "🎯 完整验证模式 - 先测试逻辑，再实际调用\n\n";
        
        // 先进行测试验证
        echo "阶段1: 逻辑验证\n";
        echo "---------------\n";
        $testHandler = new TestHandler($this->config);
        $testResult = $testHandler->execute();
        
        if ($testResult !== 0) {
            echo "\n❌ 逻辑验证失败，跳过实际调用\n";
            return $testResult;
        }
        
        echo "\n✅ 逻辑验证通过\n\n";
        
        // 再进行实际调用
        echo "阶段2: 实际调用\n";
        echo "---------------\n";
        $realHandler = new RealCallHandler($this->config);
        return $realHandler->execute();
    }
    
    /**
     * 缓存管理模式
     */
    private function runCacheMode()
    {
        echo "🗂️  缓存管理模式\n";
        echo "操作: {$this->config['action']}\n";
        echo "门店ID: {$this->config['store_id']}\n\n";
        
        $handler = new CacheHandler($this->config);
        return $handler->execute();
    }
    
    /**
     * 显示错误信息
     */
    private function showError($message, $exception)
    {
        echo "❌ {$message}:\n";
        echo "   错误: {$exception->getMessage()}\n";
        echo "   位置: {$exception->getFile()}:{$exception->getLine()}\n";
        
        if (defined('YII_DEBUG') && YII_DEBUG) {
            echo "\n调用堆栈:\n";
            echo $exception->getTraceAsString() . "\n";
        }
    }
}

/**
 * 实际调用处理器
 */
class RealCallHandler
{
    private $config;
    
    public function __construct($config)
    {
        $this->config = $config;
    }
    
    public function execute()
    {
        $successCount = 0;
        $failCount = 0;
        
        echo "🔄 开始生成测试数据...\n\n";
        
        for ($i = 1; $i <= $this->config['count']; $i++) {
            try {
                // 生成唯一订单ID
                $orderId = 'TOOL_' . date('YmdHis') . '_' . $i . '_' . mt_rand(1000, 9999);
                
                // 构建测试数据
                $addData = [
                    '订单ID(请勿修改)' => $orderId,
                    '手机尾号' => substr($this->config['mobile'], -4) . sprintf('%02d', $i),
                    '客户姓名' => $this->config['customer_name'] . ($this->config['count'] > 1 ? $i : ''),
                    '年龄段' => '20-23岁',
                    '日期' => time() * 1000,
                    '门店名称' => $this->getStoreName($this->config['store_id']),
                ];

                $obj = new \stdClass();
                $obj->text = '客资反馈';
                $obj->link = 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnpdM7EElNKu1D69I60vXQBf?prefill_年龄段=' . urlencode('20-23岁') . '&prefill_客户姓名=' . urlencode($addData['客户姓名']);
                $addData['客资反馈'] = $obj;
                
                echo "📝 [{$i}/{$this->config['count']}] 创建记录:\n";
                echo "   订单ID: {$orderId}\n";
                echo "   客户: {$addData['客户姓名']}\n";
                echo "   手机尾号: {$addData['手机尾号']}\n";
                
                // 创建AdvanceOrder实例并执行
                $mockMessage = ['ComCode' => 'chz'];
                $advanceOrder = new AdvanceOrder($mockMessage);
                $result = $advanceOrder->createRecords($addData, $this->config['store_id']);
                
                if ($result === true) {
                    echo "   ✅ 成功\n\n";
                    $successCount++;
                } else {
                    echo "   ❌ 失败\n\n";
                    $failCount++;
                }
                
                // 批量时添加延迟
                if ($this->config['count'] > 1 && $i < $this->config['count']) {
                    sleep(1);
                }
                
            } catch (Exception $e) {
                echo "   ❌ 异常: {$e->getMessage()}\n\n";
                $failCount++;
            }
        }
        
        // 显示缓存状态
        $this->showCacheStatus();
        
        // 显示总结
        echo "🎯 执行总结:\n";
        echo "   成功: {$successCount} 条\n";
        echo "   失败: {$failCount} 条\n";
        
        return $failCount > 0 ? 1 : 0;
    }
    
    private function getStoreName($storeId)
    {
        try {
            $storeName = \common\models\backend\Store::find()
                ->select('store_name')
                ->where(['id' => $storeId])
                ->scalar();
            return $storeName ?: "测试门店{$storeId}";
        } catch (Exception $e) {
            return "测试门店{$storeId}";
        }
    }
    
    private function showCacheStatus()
    {
        echo "📊 缓存状态:\n";
        $date = date('Y-m-d');
        $redisKey = 'AdvanceOrder_' . $date . ':' . $this->config['store_id'];
        $redis = \Yii::$app->cache;
        $cacheData = $redis->get($redisKey);
        
        if ($cacheData && is_array($cacheData)) {
            echo "   缓存记录数: " . count($cacheData) . "\n";
        } else {
            echo "   缓存状态: 空\n";
        }
        echo "\n";
    }
}

/**
 * 测试验证处理器
 */
class TestHandler
{
    private $config;
    private $testResults = [];
    
    public function __construct($config)
    {
        $this->config = $config;
    }
    
    public function execute()
    {
        echo "开始执行逻辑验证测试...\n\n";
        
        // 测试1: 实例创建测试
        $this->testInstanceCreation();
        
        // 测试2: 数据格式验证测试
        $this->testDataValidation();
        
        // 测试3: 幂等性测试
        $this->testIdempotency();
        
        // 测试4: 缓存TTL计算测试
        $this->testCacheTTL();
        
        // 显示测试结果
        $this->showTestResults();
        
        $failedTests = array_filter($this->testResults, function($result) {
            return $result['status'] === 'FAIL';
        });
        
        return count($failedTests) > 0 ? 1 : 0;
    }
    
    private function testInstanceCreation()
    {
        echo "🔍 测试1: AdvanceOrder实例创建\n";
        
        try {
            $mockMessage = ['ComCode' => 'test_app'];
            $advanceOrder = new AdvanceOrder($mockMessage);
            
            if ($advanceOrder instanceof AdvanceOrder) {
                $this->addTestResult('实例创建', 'PASS', '成功创建AdvanceOrder实例');
            } else {
                $this->addTestResult('实例创建', 'FAIL', '实例类型不正确');
            }
            
        } catch (Exception $e) {
            $this->addTestResult('实例创建', 'FAIL', $e->getMessage());
        }
    }
    
    private function testDataValidation()
    {
        echo "🔍 测试2: 数据格式验证\n";
        
        try {
            // 测试缺少必要字段
            $incompleteData = [
                '手机尾号' => '1234',
                '客户姓名' => '测试客户',
                // 缺少 '订单ID(请勿修改)' 字段
            ];
            
            $mockMessage = ['ComCode' => 'test_app'];
            $advanceOrder = new AdvanceOrder($mockMessage);
            
            // 这里应该抛出异常或返回错误
            $result = $advanceOrder->createRecords($incompleteData, 1);
            $this->addTestResult('数据验证', 'INFO', '未检测到数据格式错误（可能需要强化验证）');
            
        } catch (Exception $e) {
            // 如果抛出异常，说明验证有效
            $this->addTestResult('数据验证', 'PASS', '正确捕获数据格式错误');
        }
    }
    
    private function testIdempotency()
    {
        echo "🔍 测试3: 幂等性测试\n";
        
        try {
            // 手动设置缓存模拟重复订单
            $testOrderId = 'IDEMPOTENT_TEST_' . time();
            $date = date('Y-m-d');
            $redisKey = 'AdvanceOrder_' . $date . ':' . $this->config['store_id'];
            $redis = \Yii::$app->cache;
            
            // 设置测试缓存
            $testCacheData = [
                [
                    'order_id' => $testOrderId,
                    'record_id' => 'mock_record_id'
                ]
            ];
            
            $duration = strtotime($date) + 86399 - time();
            $redis->set($redisKey, $testCacheData, $duration);
            
            // 尝试创建重复订单
            $addData = [
                '订单ID(请勿修改)' => $testOrderId,
                '手机尾号' => '5678',
                '客户姓名' => '幂等测试',
                '日期' => time() * 1000,
                '门店名称' => '测试门店'
            ];
            
            $mockMessage = ['ComCode' => 'test_app'];
            $advanceOrder = new AdvanceOrder($mockMessage);
            $result = $advanceOrder->createRecords($addData, $this->config['store_id']);
            
            if ($result === true) {
                $this->addTestResult('幂等性', 'PASS', '重复订单正确返回true');
            } else {
                $this->addTestResult('幂等性', 'FAIL', '重复订单处理有误');
            }
            
            // 清理测试缓存
            $redis->delete($redisKey);
            
        } catch (Exception $e) {
            $this->addTestResult('幂等性', 'FAIL', $e->getMessage());
        }
    }
    
    private function testCacheTTL()
    {
        echo "🔍 测试4: 缓存TTL计算\n";
        
        $date = date('Y-m-d');
        $expectedDuration = strtotime($date) + 86399 - time();
        
        if ($expectedDuration > 0 && $expectedDuration <= 86399) {
            $this->addTestResult('TTL计算', 'PASS', "TTL计算正确: {$expectedDuration}秒");
        } else {
            $this->addTestResult('TTL计算', 'FAIL', "TTL计算错误: {$expectedDuration}秒");
        }
    }
    
    private function addTestResult($testName, $status, $message)
    {
        $this->testResults[] = [
            'name' => $testName,
            'status' => $status,
            'message' => $message
        ];
        
        $icon = $status === 'PASS' ? '✅' : ($status === 'FAIL' ? '❌' : 'ℹ️');
        echo "   {$icon} {$testName}: {$message}\n";
    }
    
    private function showTestResults()
    {
        echo "\n📊 测试结果汇总:\n";
        
        $passCount = 0;
        $failCount = 0;
        $infoCount = 0;
        
        foreach ($this->testResults as $result) {
            switch ($result['status']) {
                case 'PASS': $passCount++; break;
                case 'FAIL': $failCount++; break;
                case 'INFO': $infoCount++; break;
            }
        }
        
        echo "   通过: {$passCount}\n";
        echo "   失败: {$failCount}\n";
        echo "   信息: {$infoCount}\n";
        echo "   总计: " . count($this->testResults) . "\n\n";
    }
}

/**
 * 缓存管理处理器
 */
class CacheHandler
{
    private $config;
    
    public function __construct($config)
    {
        $this->config = $config;
    }
    
    public function execute()
    {
        switch ($this->config['action']) {
            case 'show':
                return $this->showCache();
            case 'clear':
                return $this->clearCache();
            case 'all':
                return $this->showAllStores();
            default:
                echo "❌ 未知操作: {$this->config['action']}\n";
                echo "支持的操作: show, clear, all\n";
                return 1;
        }
    }
    
    private function showCache()
    {
        $storeId = $this->config['store_id'];
        $date = date('Y-m-d');
        $redisKey = 'AdvanceOrder_' . $date . ':' . $storeId;
        $redis = Yii::$app->cache;
        $cacheData = $redis->get($redisKey);
        
        echo "📋 门店 {$storeId} 缓存详情:\n";
        echo "   缓存键: {$redisKey}\n";
        
        if ($cacheData && is_array($cacheData)) {
            echo "   记录数: " . count($cacheData) . "\n";
            echo "   详细信息:\n";
            foreach ($cacheData as $index => $item) {
                if (isset($item['order_id']) && isset($item['record_id'])) {
                    echo "     [{$index}] {$item['order_id']} -> {$item['record_id']}\n";
                }
            }
        } else {
            echo "   状态: 无缓存数据\n";
        }
        
        return 0;
    }
    
    private function clearCache()
    {
        $storeId = $this->config['store_id'];
        $date = date('Y-m-d');
        $redisKey = 'AdvanceOrder_' . $date . ':' . $storeId;
        $redis = Yii::$app->cache;
        
        $result = $redis->delete($redisKey);
        
        if ($result) {
            echo "✅ 门店 {$storeId} 缓存清理成功\n";
        } else {
            echo "❌ 门店 {$storeId} 缓存清理失败\n";
        }
        
        return $result ? 0 : 1;
    }
    
    private function showAllStores()
    {
        echo "📊 所有门店缓存概览:\n";
        
        for ($storeId = 1; $storeId <= 10; $storeId++) {
            $date = date('Y-m-d');
            $redisKey = 'AdvanceOrder_' . $date . ':' . $storeId;
            $redis = Yii::$app->cache;
            $cacheData = $redis->get($redisKey);
            
            $count = $cacheData && is_array($cacheData) ? count($cacheData) : 0;
            echo "   门店 {$storeId}: {$count} 条记录\n";
        }
        
        return 0;
    }
}

// 主程序入口
$tool = new AdvanceOrderTestTool();
$exitCode = $tool->run($argv);

// 显示使用提示
if ($exitCode === 0) {
    echo "\n📋 使用提示:\n";
    echo "   实际调用: php advance-order-test-tool.php --store_id=1 --customer_name=张三\n";
    echo "   测试验证: php advance-order-test-tool.php --mode=test\n";
    echo "   完整验证: php advance-order-test-tool.php --mode=full --store_id=1\n";
    echo "   缓存管理: php advance-order-test-tool.php --mode=cache --action=show --store_id=1\n";
    echo "   批量生成: php advance-order-test-tool.php --count=5\n";
}

exit($exitCode);
