# 广告预算充值业务逻辑文档

## 概述

广告预算充值系统是一个支持多平台（抖音、ADQ）的自动化充值系统，包含手动充值、定时充值和加粉后自动充值三种模式。系统通过队列机制处理充值任务，确保充值操作的可靠性和稳定性。

## 系统架构

### 核心组件

1. **[`TransferMoneyBatchService`](backendapi/services/promote/TransferMoneyBatchService.php)** - 批量充值服务类
2. **[`TransferMoneyJob`](common/queues/TransferMoneyJob.php)** - 队列任务处理类
3. **[`CusCustomerUser`](common/models/wxcom/CusCustomerUser.php:337-349)** - 加粉后自动充值触发

### 支持平台

- **抖音平台** (`reportEnum::TIKTOL`) - 通过 [`Oceanengine`](common/components/promoteData/Oceanengine.php) 组件
- **ADQ平台** (`reportEnum::ADQ`) - 通过 [`Adq`](common/components/promoteData/Adq.php) 组件

## 业务流程详解

### 一、广告预算自动充值入队流程

#### 1. 时间限制检查 - [`timeLimit()`](backendapi/services/promote/TransferMoneyBatchService.php:257-270)

**功能说明：** 限制凌晨2点到6点30分时间段不可充值，避免在系统维护时间段进行充值操作。

#### 2. 参数处理 - [`dealParams()`](backendapi/services/promote/TransferMoneyBatchService.php:348-376)

**功能说明：** 校验和处理飞书传递给充值接口的充值数据，确保必要字段（账户ID、转账金额）存在。

#### 3. 账户验证 - [`verificationAccount()`](backendapi/services/promote/TransferMoneyBatchService.php:202-230)

**功能说明：** 
- 校验充值数据中的账户数据
- 校验充值平台（旧版不支持多平台，新版要支持）
- 确保同一批次充值的账户属于同一平台

#### 4. 金额验证 - [`verificationAmount()`](backendapi/services/promote/TransferMoneyBatchService.php:181-200)

**功能说明：** 
- 校验充值金额必须大于0
- 按平台校验最大充值金额：
  - 抖音平台：单次最大1000元
  - ADQ平台：单次最大2000元

#### 5. 定时充值判断 - [`isTimeRecharge()`](backendapi/services/promote/TransferMoneyBatchService.php:272-299)

**功能说明：** 
- 判断是否是定时充值
- 校验定时充值时间：
  - 不能小于当前时间
  - 只能在今天和明天之间

#### 6. 队列任务添加 - [`addJob()`](common/queues/TransferMoneyJob.php:44-62)

**功能说明：** 
- 将充值动作加入队列
- 定时充值需要根据充值时间和当前时间计算出时间差，通过 `delay()` 设置延迟执行
- 非定时充值设置为重要任务优先处理

### 二、消费队列执行充值流程

#### 1. 批量限制检查 - [`execute()`](backendapi/services/promote/TransferMoneyBatchService.php:87-122)

**功能说明：** 旧版有限制批量充值一次最多50个户，新版不限制。

#### 2. 初始化 - [`initialize()`](backendapi/services/promote/TransferMoneyBatchService.php:124-135)

**功能说明：** 初始化类的一些属性值，确保每次处理账户时状态清洁。

#### 3. 频率控制

**功能说明：** 请求平台的充值接口不能过于频繁，循环中累计计数，每充值10个户睡眠500毫秒。

```php
// 在 execute() 方法中的频率控制逻辑
if ($num % 10 === 0) {
    usleep(500000); //睡眠500毫秒
}
```

#### 4. 目标账户设置 - [`setTargetAdvertiserIds()`](backendapi/services/promote/TransferMoneyBatchService.php:232-255)

**功能说明：**
- a. 根据目标充值账户和账户主体获取备用金账户
- b. 校验备用金账户是否绑定主体（`ads_main_body.name`）
- c. 调用 [`setToken()`](backendapi/services/promote/TransferMoneyBatchService.php:137-163) 校验/获取备用金账户所属主账户的 `access_token`, `advertiser_id`
- d. 调用 [`setPlatform()`](backendapi/services/promote/TransferMoneyBatchService.php:165-179) 校验获取备用金账户所属主账户的 `platform`

#### 5. 金额限制检查 - [`amountLimit()`](backendapi/services/promote/TransferMoneyBatchService.php:301-329)

**功能说明：** 
- 根据目标充值账户从 Redis 中获取一小时内的充值记录和累计充值金额
- 获取对应平台的一小时充值金额上限：
  - 抖音平台：一小时最大3000元
  - ADQ平台：一小时最大20000元
- 校验本次充值是否超限

#### 6. 执行充值 - [`transferMoney()`](backendapi/services/promote/TransferMoneyBatchService.php:381-405)

**功能说明：**
- a. 调用 [`getBalance()`](backendapi/services/promote/TransferMoneyBatchService.php:407-441) 根据备用金账户从 Redis 获取账户的余额，获取不到则通过接口从对应平台重新获取，余额不足则删除 Redis 缓存并报错
- b. 通过对应的平台接口进行充值，成功后将该备用金账户的剩余金额缓存到 Redis

#### 7. 成功处理 - [`success()`](backendapi/services/promote/TransferMoneyBatchService.php:331-346)

**功能说明：** 
- 将本次充值的 `user_name`, `amount`, `time()` 记录加入到第5步获取 `redisCacheData` 里，重新缓存至 Redis（有效期1小时）
- 备用金账户余额 `insufficientNalance` 小于等于3000时，调整返回结果的 `code`

#### 8. 结果数据处理 - [`resRealData()`](backendapi/services/promote/TransferMoneyBatchService.php:560-618)

**功能说明：** 将充值接口返回的多种状态码及数据，按成功、余额不足、失败原因等分类，生成清晰易读的通知文案。

#### 9. 消息发送 - [`sendMsg()`](common/queues/TransferMoneyJob.php:134-193)

**功能说明：** 该方法遍历错误码数组，按成功/失败/部分成功三种场景拼接充值结果消息，并调用飞书机器人推送给指定用户或群聊。

### 三、加粉后账户自动充值流程

#### 触发条件

**触发位置：** [`CusCustomerUser::afterSave()`](common/models/wxcom/CusCustomerUser.php:337-349)

```php
//加粉后账户自动充值
if (isset($changedAttributes['sub_advertiser_id']) && empty($changedAttributes['sub_advertiser_id']) && $this->sub_advertiser_id) {
    try {
        //判断add_time是当天且在凌晨0-8点之间
        if (DateHelper::toDate($this->add_time, 'Y-m-d') >= date('Y-m-d') 
        // && DateHelper::toDate($this->add_time, 'H') < 8
    ) {
            TransferMoneyJob::addFansJob($this->sub_advertiser_id);
        }
    } catch (Exception $e) {
        Yii::error('CusCustomerUserID：' . $this->id . ',加粉后账户自动充值失败:' . $e->getMessage());
    }
}
```

**触发条件：**
- `sub_advertiser_id` 字段从空值变为有值（新绑定广告账户）
- `add_time` 是当天（注释掉了凌晨0-8点的限制）

#### 加粉充值任务 - [`addFansJob()`](common/queues/TransferMoneyJob.php:64-94)

**功能说明：**
- 检查账户是否在允许自动充值的配置列表中
- 固定充值金额为50元
- 设置为系统自动充值，不发送通知消息
- 调用频次检查，防止重复充值

#### 充值频次检查 - [`checkTransferMoneyCount()`](common/queues/TransferMoneyJob.php:99-132)

**功能说明：**
- 检查单个账户在一分钟内的充值次数，不能超过5次
- 当天不允许再充值（超过5次后当天禁止）
- 超限时发送飞书通知给广告管理群

## 错误码体系

### 状态码定义

```php
public $success_code = 200; //成功返回码
private $time_code = 100; //定时返回码
public $success_insufficient_balance_code = 201; //成功但余额不足返回码
private $error_code_it = 422; //技术错误码
private $error_code_promote = 423; //推广错误码
private $error_code_insufficient_balance = 424; //余额不足错误码
```

### 错误分类

1. **成功类（200）**：充值完全成功
2. **成功但余额不足（201）**：充值成功但备用金余额≤3000元
3. **定时充值（100）**：定时充值任务已加入队列
4. **技术错误（422）**：系统技术问题，如Token缺失、平台不存在等
5. **推广错误（423）**：业务逻辑错误，如金额超限、时间限制等
6. **余额不足（424）**：备用金账户余额不足

## 平台差异配置

### 充值金额限制

| 平台 | 单次充值上限 | 一小时充值上限 |
|------|-------------|---------------|
| 抖音 | 1000元 | 3000元 |
| ADQ | 2000元 | 20000元 |

### 接口调用

- **抖音平台**：使用 [`Oceanengine::transferCreate()`](common/components/promoteData/Oceanengine.php) 和 [`Oceanengine::getFund()`](common/components/promoteData/Oceanengine.php)
- **ADQ平台**：使用 [`Adq::subcustomerTransfer()`](common/components/promoteData/Adq.php) 和 [`Adq::getBalance()`](common/components/promoteData/Adq.php)

## 缓存机制

### Redis缓存键

1. **充值记录缓存**：`transferMoneyData:{target_advertiser_id}` - 存储一小时内充值记录
2. **余额缓存**：`transferMoneyBalance:{advertiser_id}` - 存储备用金账户余额（500秒）
3. **加粉充值计数**：`AddFansTransferMoneyCount:{sub_advertiser_id}` - 存储加粉充值次数（5分钟）
4. **加粉充值日限制**：`AddFansTransferMoneyCount:{date}` - 存储当天已限制的账户列表

### 缓存策略

- 充值记录缓存1小时，用于限制一小时内充值金额
- 余额缓存500秒，减少频繁查询平台接口
- 余额不足时立即删除缓存，确保下次重新获取最新余额

## 通知机制

### 飞书通知场景

1. **充值结果通知**：发送给操作人员或默认群聊
2. **余额不足通知**：发送给广告管理群（GGGLGTQ）
3. **加粉异常通知**：发送给广告管理群，提醒充值频次异常

### 通知内容分类

- **完全成功**：仅定时充值发送成功通知
- **部分成功**：显示成功和失败的详细信息
- **完全失败**：显示所有失败原因

## 队列机制

### 队列优先级

- **手动充值**：设置为重要任务（`setImportant()`）
- **定时充值**：普通优先级，通过 `delay()` 设置延迟执行
- **加粉充值**：普通优先级，立即执行

### 重试机制

```php
//重试次数
public $retryTimes = 1;
//延迟时间：单位秒，默认10
public $delay = 10;
```

## 安全控制

### 时间限制

- **系统维护时间**：凌晨2:00-6:30禁止充值
- **定时充值范围**：只能设置今天和明天之间的时间
- **加粉充值时间**：仅限当天添加的客户

### 频率控制

- **接口调用频率**：每10个账户睡眠500毫秒
- **加粉充值频率**：单账户5分钟内最多5次，超过则当天禁止
- **一小时充值限额**：按平台设置不同的金额上限

### 权限控制

- **加粉自动充值**：仅限配置列表中的账户
- **批量充值限制**：旧版最多50个账户（新版无限制）

## 数据流转图

```
飞书充值请求 → TransferMoneyBatchService::run()
    ↓
参数校验 → 账户校验 → 金额校验 → 定时判断
    ↓
TransferMoneyJob::addJob() → 队列系统
    ↓
TransferMoneyJob::run() → TransferMoneyBatchService::execute()
    ↓
循环处理每个账户：
    初始化 → 设置目标账户 → 金额限制检查 → 执行充值 → 成功处理
    ↓
结果数据处理 → 飞书消息通知
```

## 重构建议

基于当前业务逻辑分析，为新架构重构提供以下建议：

### 1. 架构优化

- **服务分离**：将充值逻辑、通知逻辑、缓存逻辑分离为独立服务
- **平台抽象**：创建统一的平台接口，便于扩展新平台
- **配置中心化**：将各种限制参数配置化，便于动态调整

### 2. 业务流程优化

- **异步处理**：所有充值操作都通过队列异步处理
- **批量优化**：优化批量充值的并发处理能力
- **监控告警**：增加充值成功率、响应时间等监控指标

### 3. 数据一致性

- **事务处理**：确保充值记录和缓存数据的一致性
- **幂等性**：防止重复充值的幂等性设计
- **数据恢复**：异常情况下的数据恢复机制

### 4. 扩展性设计

- **多平台支持**：新版本需要支持多平台混合充值
- **规则引擎**：将各种限制规则配置化，便于业务调整
- **插件化**：支持新平台的插件式接入