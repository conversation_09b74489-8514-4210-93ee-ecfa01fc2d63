# 广告预算充值业务架构优化方案

## 概述

本文档描述了基于现有 MVC 架构的广告预算充值业务优化方案。该方案在保持现有架构简洁性的基础上，通过平台抽象层、验证器模式和缓存管理器等设计模式，解决了代码组织、业务分离和扩展性等核心问题。

## 一、现有架构问题分析

### 1.1 代码组织问题
- **单一服务类过于庞大**：[`TransferMoneyBatchService`](../../backendapi/services/promote/TransferMoneyBatchService.php) 承担了太多职责（验证、充值、通知、缓存等）
- **平台耦合严重**：平台相关逻辑直接硬编码在业务逻辑中
- **配置分散**：充值限额、时间限制等配置散布在代码各处

### 1.2 扩展性问题
- **新平台接入困难**：需要修改多个地方的代码
- **多平台混合充值不支持**：现有架构限制同批次只能充值同一平台
- **规则配置不灵活**：各种限制规则硬编码，难以动态调整

### 1.3 业务分离问题
- **充值逻辑与通知逻辑耦合**
- **缓存逻辑与业务逻辑混合**
- **验证逻辑分散在各个方法中**

## 二、优化方案设计

### 2.1 核心设计原则
1. **保持现有 MVC 架构**：不引入过度复杂的分层
2. **职责分离**：将现有的大型 Service 拆分为更小的专职 Service
3. **平台抽象**：创建平台适配器，便于扩展
4. **配置驱动**：将硬编码的规则提取为配置

### 2.2 架构分层设计

```mermaid
graph TB
    A[Controller 控制器层] --> B[Service 业务服务层]
    B --> C[Platform 平台抽象层]
    B --> D[Validator 验证器层]
    B --> E[Cache 缓存管理层]
    C --> F[External APIs 外部接口]
    
    subgraph "业务服务层"
        B1[TransferMoneyService]
        B2[TransferMoneyBatchService]
    end
    
    subgraph "平台抽象层"
        C1[PlatformFactory]
        C2[TiktokAdapter]
        C3[AdqAdapter]
        C4[NewPlatformAdapter]
    end
    
    subgraph "验证器层"
        D1[TransferValidator]
        D2[TimeValidator]
        D3[AmountValidator]
        D4[AccountValidator]
    end
    
    subgraph "缓存管理层"
        E1[TransferCacheManager]
    end
```

### 2.3 优化后的目录结构

```
backendapi/services/promote/transfermoney/
├── TransferMoneyService.php              // 主要业务服务（重构后）
├── platform/                             // 平台适配器
│   ├── PlatformAdapterInterface.php      // 平台接口
│   ├── TiktokAdapter.php                 // 抖音适配器
│   ├── AdqAdapter.php                    // ADQ适配器
│   └── PlatformFactory.php               // 平台工厂
├── validator/                             // 验证器
│   ├── TransferValidator.php             // 充值验证器
│   ├── TimeValidator.php                 // 时间验证器
│   ├── AmountValidator.php               // 金额验证器
│   └── AccountValidator.php              // 账户验证器
├── cache/                                 // 缓存管理
│   └── TransferCacheManager.php          // 充值缓存管理器
└── config/                                // 配置文件
    ├── platform_config.php               // 平台配置
    ├── transfer_limits.php               // 充值限制配置
    └── budget_rules.php                  // 预算规则配置
```

## 三、核心组件设计

### 3.1 平台抽象层

#### 3.1.1 平台适配器接口
```php
interface PlatformAdapterInterface
{
    public function getName(): string;
    public function transferMoney(string $accessToken, array $params): array;
    public function getBalance(string $accessToken, string $accountId): float;
    public function getSingleLimit(): int;
    public function getHourlyLimit(): int;
}
```

#### 3.1.2 抖音平台适配器
```php
class TiktokAdapter implements PlatformAdapterInterface
{
    public function getName(): string
    {
        return reportEnum::TIKTOL;
    }
    
    public function transferMoney(string $accessToken, array $params): array
    {
        $result = Oceanengine::transferCreate(
            $accessToken, 
            $params['organization_id'], 
            $params['from_account'], 
            $params['to_account'], 
            $params['amount']
        );
        
        // 统一返回格式
        return [
            'success' => isset($result['code']) && $result['code'] == 0,
            'message' => $result['message'] ?? '未知错误',
            'data' => $result['data'] ?? null
        ];
    }
    
    public function getBalance(string $accessToken, string $accountId): float
    {
        $result = Oceanengine::getFund($accessToken, $accountId);
        
        if (!isset($result['code']) || $result['code'] != 0) {
            throw new Exception('查询余额失败：' . ($result['message'] ?? '未知错误'));
        }
        
        return $result['data']['balance'] ?? 0;
    }
    
    public function getSingleLimit(): int
    {
        return 1000; // 可以从配置文件读取
    }
    
    public function getHourlyLimit(): int
    {
        return 3000; // 可以从配置文件读取
    }
}
```

#### 3.1.3 ADQ平台适配器
```php
class AdqAdapter implements PlatformAdapterInterface
{
    public function getName(): string
    {
        return reportEnum::ADQ;
    }
    
    public function transferMoney(string $accessToken, array $params): array
    {
        $result = Adq::subcustomerTransfer(
            $accessToken, 
            $params['from_account'], 
            $params['to_account'], 
            $params['amount']
        );
        
        // 统一返回格式
        return [
            'success' => $result['code'] == 0,
            'message' => $result['message_cn'] ?? $result['message'] ?? '未知错误',
            'data' => $result['data'] ?? null
        ];
    }
    
    public function getBalance(string $accessToken, string $accountId): float
    {
        $data = Adq::getBalance($accessToken, $accountId, '', '');
        $balance = 0;
        
        foreach ($data as $item) {
            $balance = BcHelper::add($balance, $item['balance']);
        }
        
        return BcHelper::div($balance, 100);
    }
    
    public function getSingleLimit(): int
    {
        return 2000;
    }
    
    public function getHourlyLimit(): int
    {
        return 20000;
    }
}
```

#### 3.1.4 平台工厂
```php
class PlatformFactory
{
    private static $adapters = [
        reportEnum::TIKTOL => TiktokAdapter::class,
        reportEnum::ADQ => AdqAdapter::class,
    ];
    
    public static function create(string $platform): PlatformAdapterInterface
    {
        if (!isset(self::$adapters[$platform])) {
            throw new Exception("不支持的平台: {$platform}");
        }
        
        $adapterClass = self::$adapters[$platform];
        return new $adapterClass();
    }
    
    // 支持新平台注册
    public static function register(string $platform, string $adapterClass): void
    {
        self::$adapters[$platform] = $adapterClass;
    }
}
```

### 3.2 验证器层

#### 3.2.1 充值验证器
```php
class TransferValidator
{
    private $validators = [];
    
    public function __construct()
    {
        $this->validators = [
            new TimeValidator(),
            new AmountValidator(),
            new AccountValidator(),
        ];
    }
    
    public function validate(array $data): void
    {
        foreach ($this->validators as $validator) {
            $validator->validate($data);
        }
    }
}
```

#### 3.2.2 时间验证器
```php
class TimeValidator
{
    public function validate(array $data): void
    {
        $currentTime = date('H:i');
        $config = require __DIR__ . '/../config/transfer_limits.php';
        
        $startTime = $config['time_restrictions']['forbidden_start'];
        $endTime = $config['time_restrictions']['forbidden_end'];
        
        if ($currentTime >= $startTime && $currentTime <= $endTime) {
            throw new Exception("凌晨{$startTime}到{$endTime}时间段不可充值");
        }
    }
}
```

#### 3.2.3 金额验证器
```php
class AmountValidator
{
    public function validate(array $data): void
    {
        $amount = $data['amount'];
        $platform = $data['platform'];
        
        if ($amount <= 0) {
            throw new Exception('单次充值金额必须大于0');
        }
        
        $adapter = PlatformFactory::create($platform);
        $maxAmount = $adapter->getSingleLimit();
        
        if ($amount > $maxAmount) {
            throw new Exception("单次充值金额不得超过{$maxAmount}");
        }
    }
}
```

### 3.3 缓存管理层

```php
class TransferCacheManager
{
    private $cache;
    
    public function __construct()
    {
        $this->cache = Yii::$app->cache;
    }
    
    public function getTransferHistory(string $accountId): array
    {
        $key = "transferMoneyData:{$accountId}";
        return $this->cache->get($key) ?: [];
    }
    
    public function addTransferRecord(string $accountId, array $record): void
    {
        $key = "transferMoneyData:{$accountId}";
        $history = $this->getTransferHistory($accountId);
        $history[] = $record;
        $this->cache->set($key, $history, 3600);
    }
    
    public function getAccountBalance(string $accountId): ?float
    {
        $key = "transferMoneyBalance:{$accountId}";
        return $this->cache->get($key);
    }
    
    public function setAccountBalance(string $accountId, float $balance): void
    {
        $key = "transferMoneyBalance:{$accountId}";
        $this->cache->set($key, $balance, 500);
    }
    
    public function checkHourlyLimit(string $accountId, float $amount, string $platform): void
    {
        $history = $this->getTransferHistory($accountId);
        $oneHourAgo = time() - 3600;
        $totalAmount = 0;
        
        foreach ($history as $record) {
            if ($record['time'] >= $oneHourAgo) {
                $totalAmount += $record['amount'];
            }
        }
        
        $adapter = PlatformFactory::create($platform);
        $maxAmount = $adapter->getHourlyLimit();
        
        if (($totalAmount + $amount) > $maxAmount) {
            throw new Exception("1小时内限制充值金额不能超过{$maxAmount}，该户已经充值了{$totalAmount}");
        }
    }
}
```

### 3.4 重构后的主业务服务

```php
class TransferMoneyService
{
    private $validator;
    private $cacheManager;
    private $platformAdapter;
    
    // 保持现有的属性和错误码
    public $code = 422;
    private $success_code = 200;
    private $success_insufficient_balance_code = 201;
    private $error_code_it = 422;
    private $error_code_promote = 423;
    private $error_code_insufficient_balance = 424;
    
    public function __construct()
    {
        $this->validator = new TransferValidator();
        $this->cacheManager = new TransferCacheManager();
    }
    
    public function run($params)
    {
        try {
            // 1. 参数处理（保持现有逻辑）
            $data = $this->dealParams($params);
            
            // 2. 设置字段和平台适配器
            $this->setFields($data);
            $this->platformAdapter = PlatformFactory::create($this->platform);
            
            // 3. 验证（使用新的验证器）
            $this->validator->validate([
                'amount' => $this->amount,
                'platform' => $this->platform,
                'account_id' => $this->target_advertiser_id,
            ]);
            
            // 4. 小时限额检查
            $this->cacheManager->checkHourlyLimit(
                $this->target_advertiser_id, 
                $this->amount, 
                $this->platform
            );
            
            // 5. 执行充值
            $this->transferMoney();
            
            // 6. 成功处理
            $this->success();
            
        } catch (Exception $e) {
            Yii::error("充值失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function transferMoney()
    {
        // 获取余额（使用缓存管理器）
        $balance = $this->getBalance();
        $this->insufficientNalance = $balance - $this->amount;
        
        // 使用平台适配器执行充值
        $params = [
            'organization_id' => $this->organization_id,
            'from_account' => $this->advertiser_id,
            'to_account' => $this->target_advertiser_id,
            'amount' => $this->amount,
        ];
        
        $result = $this->platformAdapter->transferMoney($this->accessToken, $params);
        
        // 检查结果
        if (!$result['success']) {
            $this->code = $this->error_code_it;
            throw new Exception('充值失败：' . $result['message']);
        }
        
        // 更新余额缓存
        $this->cacheManager->setAccountBalance($this->advertiser_id, $this->insufficientNalance);
    }
    
    private function getBalance(): float
    {
        // 先从缓存获取
        $balance = $this->cacheManager->getAccountBalance($this->advertiser_id);
        
        if ($balance === null) {
            // 缓存没有，从平台获取
            $balance = $this->platformAdapter->getBalance($this->accessToken, $this->advertiser_id);
        }
        
        if ($this->amount > $balance) {
            $this->code = $this->error_code_insufficient_balance;
            $errorMsg = "充值失败，主体：{$this->mainBody}（{$this->advertiser_id}），账户余额不足，剩余：{$balance}";
            throw new Exception($errorMsg);
        }
        
        return $balance;
    }
    
    private function success()
    {
        // 记录充值历史
        $this->cacheManager->addTransferRecord($this->target_advertiser_id, [
            'user_name' => $this->user_name,
            'amount' => $this->amount,
            'time' => time()
        ]);
        
        // 设置返回码
        if ($this->insufficientNalance <= 3000) {
            $this->code = $this->success_insufficient_balance_code;
        } else {
            $this->code = $this->success_code;
        }
    }
    
    // 保持现有的其他方法...
}
```

## 四、配置文件设计

### 4.1 平台配置
```php
// config/platform_config.php
return [
    'platforms' => [
        reportEnum::TIKTOL => [
            'name' => '抖音',
            'adapter' => TiktokAdapter::class,
            'single_limit' => 1000,
            'hourly_limit' => 3000,
        ],
        reportEnum::ADQ => [
            'name' => 'ADQ',
            'adapter' => AdqAdapter::class,
            'single_limit' => 2000,
            'hourly_limit' => 20000,
        ],
    ],
];
```

### 4.2 充值限制配置
```php
// config/transfer_limits.php
return [
    'time_restrictions' => [
        'forbidden_start' => '02:00',
        'forbidden_end' => '06:30',
    ],
    'batch_limit' => 50, // 批量充值限制
];
```

### 4.3 预算规则配置
```php
// config/budget_rules.php
return [
    'enable_budget_limit' => true,
    'user_daily_limits' => [
        // 按用户配置每日限额
        // 'user_id' => ['platform' => 'daily_limit']
    ],
    'global_daily_limit' => 100000, // 全局每日限额
];
```

## 五、扩展性支持

### 5.1 多平台混合充值支持

```php
// 在 TransferMoneyBatchService 中添加
public function executeMixedPlatform($data)
{
    $results = [];
    $groupedByPlatform = $this->groupAccountsByPlatform($data['target_advertiser_ids']);
    
    foreach ($groupedByPlatform as $platform => $accountIds) {
        $platformData = $data;
        $platformData['target_advertiser_ids'] = $accountIds;
        $results[$platform] = $this->execute($platformData);
    }
    
    return $this->mergePlatformResults($results);
}

private function groupAccountsByPlatform(array $accountIds): array
{
    $grouped = [];
    foreach ($accountIds as $accountId) {
        $platform = $this->getAccountPlatform($accountId);
        $grouped[$platform][] = $accountId;
    }
    return $grouped;
}
```

### 5.2 新平台接入

新平台接入只需要：

1. **实现平台适配器接口**
```php
class NewPlatformAdapter implements PlatformAdapterInterface
{
    public function getName(): string
    {
        return 'new_platform';
    }
    
    public function transferMoney(string $accessToken, array $params): array
    {
        // 调用新平台的充值API
        $result = NewPlatformApi::transfer(...);
        
        // 返回统一格式
        return [
            'success' => $result['status'] == 'success',
            'message' => $result['message'] ?? '未知错误',
            'data' => $result
        ];
    }
    
    // 实现其他接口方法...
}
```

2. **在配置中注册新平台**
```php
// config/platform_config.php 中添加
'new_platform' => [
    'name' => '新平台',
    'adapter' => NewPlatformAdapter::class,
    'single_limit' => 1500,
    'hourly_limit' => 5000,
],
```

3. **业务服务无需任何修改**，自动支持新平台

### 5.3 广告预算限制

```php
// 在验证器中添加预算验证器
class BudgetValidator
{
    public function validate(array $data): void
    {
        $config = require __DIR__ . '/../config/budget_rules.php';
        
        if (!$config['enable_budget_limit']) {
            return;
        }
        
        $userId = $data['user_id'];
        $platform = $data['platform'];
        $amount = $data['amount'];
        
        // 检查用户每日限额
        $userLimits = $config['user_daily_limits'][$userId] ?? [];
        $dailyLimit = $userLimits[$platform] ?? $config['global_daily_limit'];
        
        $todayUsed = $this->getTodayUsedAmount($userId, $platform);
        
        if (($todayUsed + $amount) > $dailyLimit) {
            throw new Exception("超出每日充值限额，今日已充值{$todayUsed}，限额{$dailyLimit}");
        }
    }
    
    private function getTodayUsedAmount(string $userId, string $platform): float
    {
        // 从数据库或缓存获取今日已使用金额
        // 实现逻辑...
    }
}
```

## 六、主要业务服务与平台抽象层的关系

### 6.1 整体架构关系图

```mermaid
graph TB
    A[Controller] --> B[TransferMoneyService 主业务服务]
    B --> C[TransferValidator 验证器]
    B --> D[TransferCacheManager 缓存管理器]
    B --> E[PlatformFactory 平台工厂]
    E --> F[PlatformAdapterInterface 平台接口]
    F --> G[TiktokAdapter 抖音适配器]
    F --> H[AdqAdapter ADQ适配器]
    F --> I[NewPlatformAdapter 新平台适配器]
    G --> J[Oceanengine 抖音API]
    H --> K[Adq ADQ API]
    I --> L[NewPlatform API]
```

### 6.2 核心运作流程

1. **业务服务获取平台适配器**
```php
// 通过工厂获取对应的平台适配器
$this->platformAdapter = PlatformFactory::create($this->platform);
```

2. **统一接口调用**
```php
// 业务服务只需要调用统一接口，不需要了解平台细节
$result = $this->platformAdapter->transferMoney($this->accessToken, $params);
$balance = $this->platformAdapter->getBalance($this->accessToken, $this->advertiser_id);
```

3. **平台适配器处理差异**
```php
// 每个适配器内部处理平台特定的逻辑和返回格式统一化
public function transferMoney(string $accessToken, array $params): array
{
    // 调用平台特定的API
    $result = PlatformApi::transfer(...);
    
    // 统一返回格式
    return [
        'success' => $this->isSuccess($result),
        'message' => $this->getMessage($result),
        'data' => $result
    ];
}
```

## 七、实施优势

### 7.1 代码组织优化
- **职责清晰**：每个组件职责单一，易于维护
- **层次分明**：验证、缓存、平台调用分离
- **可测试性**：组件间依赖注入，便于单元测试

### 7.2 业务分离
- **验证逻辑**：独立的验证器处理各种验证规则
- **平台逻辑**：通过适配器封装平台差异
- **缓存逻辑**：专门的缓存管理器处理 Redis 操作
- **通知逻辑**：保持现有机制，通过 `Yii::info()` 和 `Yii::error()` 记录日志

### 7.3 扩展性提升
- **新平台接入**：只需实现接口和配置，无需修改现有代码
- **多平台混合充值**：支持一次请求充值多个平台的账户
- **规则配置化**：所有业务规则通过配置文件管理，支持动态调整
- **预算限制**：支持按推广人员、按平台、按时间的精细化限制

### 7.4 实施安全性
1. **渐进式重构**：可以逐步替换现有代码，风险可控
2. **向后兼容**：保持对外接口不变，上下游服务无感知
3. **简洁实用**：避免过度设计，符合现有系统架构风格
4. **易于维护**：代码职责清晰，便于后续维护和扩展

## 八、实施建议

### 8.1 分阶段实施
1. **第一阶段**：创建平台适配器和工厂类
2. **第二阶段**：重构验证器和缓存管理器
3. **第三阶段**：逐步替换现有业务服务中的平台判断逻辑
4. **第四阶段**：实现多平台混合充值和预算限制功能

### 8.2 兼容性保证
```php
// 过渡期的兼容写法
public function transferMoney()
{
    if (USE_PLATFORM_ADAPTER) {
        // 使用新的适配器
        $result = $this->platformAdapter->transferMoney($this->accessToken, $params);
        if (!$result['success']) {
            throw new Exception('充值失败：' . $result['message']);
        }
    } else {
        // 保留原有逻辑作为备用
        if ($this->platform == reportEnum::TIKTOL) {
            // 原有抖音逻辑
        } elseif ($this->platform == reportEnum::ADQ) {
            // 原有ADQ逻辑
        }
    }
}
```

### 8.3 测试策略
1. **单元测试**：为每个适配器和验证器编写单元测试
2. **集成测试**：测试新旧代码的兼容性
3. **回归测试**：确保现有功能不受影响
4. **性能测试**：验证新架构的性能表现

## 九、总结

这个优化方案在保持现有 MVC 架构简洁性的基础上，通过平台抽象层、验证器模式和缓存管理器等设计模式，成功解决了代码组织、业务分离和扩展性等核心问题。方案具有以下特点：

- **实用性强**：基于现有架构，避免过度设计
- **扩展性好**：支持新平台接入和多平台混合充值
- **风险可控**：支持渐进式重构，保证向后兼容
- **维护性高**：代码职责清晰，便于后续维护

该方案为广告预算充值业务的长期发展提供了良好的架构基础。