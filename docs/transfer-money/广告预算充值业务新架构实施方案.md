# 广告预算充值业务新架构实施方案（TDD版本）

## 概述

本文档描述了基于测试驱动开发（TDD）的广告预算充值业务新架构实施方案。该方案基于现有 [`TransferMoneyBatchService`](../../backendapi/services/promote/TransferMoneyBatchService.php) 的业务逻辑，采用统一的服务类设计，通过平台抽象层、验证器模式和缓存管理器等设计模式，解决现有系统的代码重复、平台耦合和扩展性等核心问题。

**重要说明**：
1. 本方案是全新的业务实现，不会修改任何现有广告预算充值业务代码，确保现有业务的正常运行
2. 采用测试驱动开发方式，先编写测试用例，再实现功能代码
3. 使用Mock适配器模拟平台接口调用，绝对避免真实充值操作
4. 直接调用现有 [`Oceanengine`](../../common/components/promoteData/Oceanengine.php) 和 [`Adq`](../../common/components/promoteData/Adq.php) 类的方法

## 一、现有业务分析

### 1.1 现有核心组件

基于现有业务逻辑分析，当前系统包含以下核心组件：

1. **[`TransferMoneyBatchService`](../../backendapi/services/promote/TransferMoneyBatchService.php)** - 批量充值服务类
2. **[`TransferMoneyService`](../../backendapi/services/promote/TransferMoneyService.php)** - 单次充值服务类  
3. **[`TransferMoneyJob`](../../common/queues/TransferMoneyJob.php)** - 队列任务处理类
4. **[`CusCustomerUser`](../../common/models/wxcom/CusCustomerUser.php:337-349)** - 加粉后自动充值触发

### 1.2 支持平台

- **抖音平台** (`reportEnum::TIKTOL`) - 通过 [`Oceanengine`](../../common/components/promoteData/Oceanengine.php) 组件
- **ADQ平台** (`reportEnum::ADQ`) - 通过 [`Adq`](../../common/components/promoteData/Adq.php) 组件

### 1.3 现有业务流程

#### 广告预算自动充值入队流程
参考现有 [`TransferMoneyBatchService::run()`](../../backendapi/services/promote/TransferMoneyBatchService.php:53-78) 方法：

1. **时间限制检查** - [`timeLimit()`](../../backendapi/services/promote/TransferMoneyBatchService.php:257-270)
2. **参数处理** - [`dealParams()`](../../backendapi/services/promote/TransferMoneyBatchService.php:348-376)
3. **账户验证** - [`verificationAccount()`](../../backendapi/services/promote/TransferMoneyBatchService.php:202-230)
4. **金额验证** - [`verificationAmount()`](../../backendapi/services/promote/TransferMoneyBatchService.php:181-200)
5. **定时充值判断** - [`isTimeRecharge()`](../../backendapi/services/promote/TransferMoneyBatchService.php:272-299)
6. **队列任务添加** - [`TransferMoneyJob::addJob()`](../../common/queues/TransferMoneyJob.php:44-62)

#### 消费队列执行充值流程
参考现有 [`TransferMoneyBatchService::execute()`](../../backendapi/services/promote/TransferMoneyBatchService.php:87-122) 方法：

1. **批量限制检查** - 旧版限制50个户，新版不限制
2. **初始化** - [`initialize()`](../../backendapi/services/promote/TransferMoneyBatchService.php:124-135)
3. **频率控制** - 每充值10个户睡眠500毫秒
4. **目标账户设置** - [`setTargetAdvertiserIds()`](../../backendapi/services/promote/TransferMoneyBatchService.php:232-255)
5. **金额限制检查** - [`amountLimit()`](../../backendapi/services/promote/TransferMoneyBatchService.php:301-329)
6. **执行充值** - [`transferMoney()`](../../backendapi/services/promote/TransferMoneyBatchService.php:381-405)
7. **成功处理** - [`success()`](../../backendapi/services/promote/TransferMoneyBatchService.php:331-346)
8. **结果数据处理** - [`resRealData()`](../../backendapi/services/promote/TransferMoneyBatchService.php:560-618)
9. **消息发送** - [`TransferMoneyJob::sendMsg()`](../../common/queues/TransferMoneyJob.php:134-193)

#### 加粉后账户自动充值流程
参考现有 [`CusCustomerUser::afterSave()`](../../common/models/wxcom/CusCustomerUser.php:337-349) 方法：

- **触发条件**：`sub_advertiser_id` 字段从空值变为有值且 `add_time` 是当天
- **加粉充值任务** - [`TransferMoneyJob::addFansJob()`](../../common/queues/TransferMoneyJob.php:64-94)
- **充值频次检查** - [`TransferMoneyJob::checkTransferMoneyCount()`](../../common/queues/TransferMoneyJob.php:99-132)

### 1.4 现有问题分析

1. **代码重复严重**：[`TransferMoneyBatchService`](../../backendapi/services/promote/TransferMoneyBatchService.php) 和 [`TransferMoneyService`](../../backendapi/services/promote/TransferMoneyService.php) 存在大量重复逻辑
2. **平台耦合严重**：平台判断逻辑散布在 [`transferMoney()`](../../backendapi/services/promote/TransferMoneyBatchService.php:381-405) 等多个方法中
3. **职责不清晰**：单个服务类承担了验证、充值、缓存、通知等多种职责
4. **配置硬编码**：充值限额等配置直接写在类属性中，如 [`$tiktok_single_recharge_amount = 1000`](../../backendapi/services/promote/TransferMoneyBatchService.php:34)

### 1.5 现有错误码体系

参考现有 [`TransferMoneyBatchService`](../../backendapi/services/promote/TransferMoneyBatchService.php:40-46) 错误码定义：

- **200** - 成功返回码
- **100** - 定时返回码  
- **201** - 成功但余额不足返回码
- **422** - 技术错误码
- **423** - 推广错误码
- **424** - 余额不足错误码

### 1.6 现有平台差异配置

参考现有代码中的硬编码配置：

| 平台 | 单次充值上限 | 一小时充值上限 | 接口调用 |
|------|-------------|---------------|----------|
| 抖音 | 1000元 | 3000元 | [`Oceanengine::transferCreate()`](../../common/components/promoteData/Oceanengine.php:567-607) |
| ADQ | 2000元 | 20000元 | [`Adq::subcustomerTransfer()`](../../common/components/promoteData/Adq.php:600-622) |

### 1.7 现有缓存机制

参考现有 Redis 缓存键设计：

1. **充值记录缓存**：`transferMoneyData:{target_advertiser_id}` - 存储一小时内充值记录
2. **余额缓存**：`transferMoneyBalance:{advertiser_id}` - 存储备用金账户余额（500秒）
3. **加粉充值计数**：`AddFansTransferMoneyCount:{sub_advertiser_id}` - 存储加粉充值次数（5分钟）
4. **加粉充值日限制**：`AddFansTransferMoneyCount:{date}` - 存储当天已限制的账户列表

## 二、新架构设计（基于TDD）

### 2.1 核心设计原则

1. **测试驱动开发**：先编写测试用例，再实现功能代码
2. **统一服务类**：合并单次和批量充值逻辑到一个服务类中
3. **平台抽象**：创建平台适配器，直接调用现有平台类方法
4. **配置驱动**：将硬编码的规则提取为配置
5. **安全第一**：使用Mock适配器避免真实充值操作
6. **业务兼容**：支持正常充值、定时充值、加粉后自动充值三种模式

### 2.2 架构分层设计

```mermaid
graph TB
    A[Controller 控制器层] --> B[TransferMoneyServiceV2 统一业务服务层]
    B --> C[Platform 平台抽象层]
    B --> D[Validator 验证器层]
    B --> E[Cache 缓存管理层]
    C --> F[现有平台类]
    
    subgraph "统一业务服务层"
        B1[TransferMoneyServiceV2]
        B2[支持单次/批量/定时/加粉充值]
    end
    
    subgraph "平台抽象层"
        C1[PlatformFactory]
        C2[TiktokAdapter]
        C3[AdqAdapter]
        C4[MockAdapter 测试用]
    end
    
    subgraph "验证器层"
        D1[TransferValidator]
        D2[TimeValidator]
        D3[AmountValidator]
        D4[AccountValidator]
    end
    
    subgraph "缓存管理层"
        E1[TransferCacheManager]
    end
    
    subgraph "现有平台类"
        F1[Oceanengine]
        F2[Adq]
    end
```

### 2.3 新架构目录结构

```
backendapi/services/promote/transfermoneyv2/
├── TransferMoneyServiceV2.php               // 统一业务服务类
├── platform/                                // 平台适配器
│   ├── PlatformAdapterInterface.php         // 平台接口
│   ├── TiktokAdapter.php                    // 抖音适配器
│   ├── AdqAdapter.php                       // ADQ适配器
│   ├── MockAdapter.php                      // 测试Mock适配器
│   └── PlatformFactory.php                  // 平台工厂
├── validator/                                // 验证器
│   ├── ValidatorInterface.php               // 验证器接口
│   ├── TransferValidator.php                // 充值验证器
│   ├── TimeValidator.php                    // 时间验证器
│   ├── AmountValidator.php                  // 金额验证器
│   └── AccountValidator.php                 // 账户验证器
├── cache/                                    // 缓存管理
│   └── TransferCacheManager.php             // 充值缓存管理器
└── config/                                   // 配置文件
    ├── platform_config.php                  // 平台配置
    ├── transfer_limits.php                   // 充值限制配置
    └── budget_rules.php                     // 预算规则配置

backendapi/tests/unit/promote/transfermoneyv2/
├── TransferMoneyServiceV2Test.php           // 主服务测试
├── platform/                                // 平台适配器测试
│   ├── TiktokAdapterTest.php
│   ├── AdqAdapterTest.php
│   ├── MockAdapterTest.php
│   └── PlatformFactoryTest.php
├── validator/                                // 验证器测试
│   ├── TransferValidatorTest.php
│   ├── TimeValidatorTest.php
│   ├── AmountValidatorTest.php
│   └── AccountValidatorTest.php
└── cache/                                    // 缓存管理测试
    └── TransferCacheManagerTest.php

common/queues/
├── TransferMoneyJobV2.php                   // 新队列任务处理类
```

## 三、核心组件设计

### 3.1 平台抽象层

#### 3.1.1 平台适配器接口设计

**接口职责**：
- 统一不同平台的充值接口调用
- 统一返回格式处理
- 提供平台特定的配置信息

**核心方法**：
- `getName()` - 获取平台名称
- `transferMoney()` - 执行转账，返回统一格式
- `getBalance()` - 获取账户余额
- `getSingleLimit()` - 获取单次充值限额
- `getHourlyLimit()` - 获取小时充值限额

#### 3.1.2 抖音平台适配器

**实现要点**：
- 基于现有 [`Oceanengine::transferCreate()`](../../common/components/promoteData/Oceanengine.php:567-607) 方法
- 基于现有 [`Oceanengine::getFund()`](../../common/components/promoteData/Oceanengine.php:539-550) 方法
- 参考现有限额配置：单次1000元，小时3000元

#### 3.1.3 ADQ平台适配器

**实现要点**：
- 基于现有 [`Adq::subcustomerTransfer()`](../../common/components/promoteData/Adq.php:600-622) 方法
- 基于现有 [`Adq::getBalance()`](../../common/components/promoteData/Adq.php:453-472) 方法
- 参考现有限额配置：单次2000元，小时20000元

#### 3.1.4 平台工厂

**职责**：
- 根据平台类型创建对应的适配器实例
- 支持新平台的动态注册
- 提供平台支持列表查询

### 3.2 验证器层

#### 3.2.1 验证器接口设计

**接口职责**：
- 定义统一的验证接口
- 支持链式验证调用

#### 3.2.2 时间验证器

**实现要点**：
- 基于现有 [`timeLimit()`](../../backendapi/services/promote/TransferMoneyBatchService.php:257-270) 方法逻辑
- 从配置文件读取时间限制规则
- 支持凌晨2:00-6:30禁止充值

#### 3.2.3 金额验证器

**实现要点**：
- 基于现有 [`verificationAmount()`](../../backendapi/services/promote/TransferMoneyBatchService.php:181-200) 方法逻辑
- 验证金额必须大于0
- 根据平台适配器获取单次限额进行验证

#### 3.2.4 账户验证器

**实现要点**：
- 基于现有 [`verificationAccount()`](../../backendapi/services/promote/TransferMoneyBatchService.php:202-230) 方法逻辑
- 验证账户ID不能为空
- 验证同批次账户必须属于同一平台

#### 3.2.5 充值验证器

**职责**：
- 组合所有验证器进行链式验证
- 支持动态添加自定义验证器

### 3.3 缓存管理层

#### 3.3.1 缓存管理器设计

**职责**：
- 统一管理所有充值相关的Redis缓存操作
- 封装缓存键的生成和管理逻辑
- 提供小时限额检查功能

**核心方法**：
- `getTransferHistory()` - 获取充值历史记录
- `addTransferRecord()` - 添加充值记录
- `getAccountBalance()` / `setAccountBalance()` - 余额缓存管理
- `checkHourlyLimit()` - 检查小时限额

**实现要点**：
- 基于现有 [`amountlimit()`](../../backendapi/services/promote/TransferMoneyBatchService.php:301-329) 方法逻辑
- 基于现有 [`success()`](../../backendapi/services/promote/TransferMoneyBatchService.php:331-346) 方法逻辑
- 保持现有缓存键命名规则和过期时间

### 3.4 配置文件系统

#### 3.4.1 平台配置

**配置内容**：
- 平台名称和适配器类映射
- 各平台的充值限额配置
- 平台特定参数配置

#### 3.4.2 充值限制配置

**配置内容**：
- 时间限制规则（凌晨2:00-6:30）
- 批量充值限制（50个户）
- 频率控制参数（每10个户睡眠500毫秒）

#### 3.4.3 预算规则配置

**配置内容**：
- 是否启用预算限制
- 用户每日限额配置
- 全局每日限额
- 余额警告阈值（3000元）

## 四、TDD实施方案

### 4.1 测试驱动开发流程

**TDD三步骤**：
1. **Red（红）**：编写失败的测试用例
2. **Green（绿）**：编写最少代码使测试通过
3. **Refactor（重构）**：优化代码结构

### 4.2 统一业务服务设计

**`TransferMoneyServiceV2` 职责**：
- 统一处理单次充值、批量充值、定时充值、加粉充值
- 基于现有 [`TransferMoneyBatchService`](../../backendapi/services/promote/TransferMoneyBatchService.php) 的业务逻辑
- 使用新的架构组件（验证器、缓存管理器、平台适配器）
- 保持对外接口兼容性

**核心方法**：
- `run($params)` - 主入口方法，支持所有充值类型
- `execute($data)` - 执行充值逻辑
- `transferMoney()` - 单个账户充值
- `getBalance()` - 获取账户余额
- `getAccountBalance()` - 查询所有账户余额

### 4.3 安全的Mock适配器

**`MockAdapter` 设计**：
- 模拟真实平台接口的返回格式
- 记录所有调用日志，便于测试验证
- 支持成功、失败、余额不足等各种场景
- 绝对不会触发真实的充值操作

**Mock场景覆盖**：
- 充值成功场景
- 余额不足场景
- 接口异常场景
- 网络超时场景
- 参数错误场景

## 五、多平台混合充值支持

### 5.1 设计思路

**核心思想**：
- 将混合平台的账户按平台分组
- 每个平台使用对应的适配器并行处理
- 最后合并各平台的处理结果

### 5.2 实现方案

**关键方法**：
- `executeMixedPlatform()` - 混合平台充值入口
- `groupAccountsByPlatform()` - 账户按平台分组
- `mergePlatformResults()` - 结果合并

### 5.3 扩展性支持

**新平台接入流程**：
1. 实现 `PlatformAdapterInterface` 接口
2. 在平台配置中注册新平台
3. 业务服务自动支持新平台，无需修改

## 五、TDD实施步骤

### 5.1 第一阶段：测试环境搭建和Mock适配器（1天）

**TDD任务清单**：
- [ ] 编写 `MockAdapterTest.php` 测试用例
- [ ] 实现 `MockAdapter` 类，模拟平台接口
- [ ] 编写 `PlatformFactoryTest.php` 测试用例
- [ ] 实现 `PlatformFactory` 工厂类
- [ ] 验证Mock适配器安全性（确保不会真实充值）

**验收标准**：
- Mock适配器通过所有测试用例
- 工厂类能正确创建Mock适配器
- 测试环境完全隔离，无真实充值风险

### 5.2 第二阶段：平台适配器实现（1天）

**TDD任务清单**：
- [ ] 编写 `TiktokAdapterTest.php` 测试用例
- [ ] 实现 `TiktokAdapter`，调用现有 [`Oceanengine`](../../common/components/promoteData/Oceanengine.php) 方法
- [ ] 编写 `AdqAdapterTest.php` 测试用例
- [ ] 实现 `AdqAdapter`，调用现有 [`Adq`](../../common/components/promoteData/Adq.php) 方法
- [ ] 编写 `PlatformAdapterInterface` 接口

**验收标准**：
- 所有适配器测试通过
- 适配器正确调用现有平台类方法
- 返回格式统一标准化

### 5.3 第三阶段：验证器和缓存管理器（1天）

**TDD任务清单**：
- [ ] 编写验证器测试用例（时间、金额、账户验证）
- [ ] 实现各种验证器，复用现有验证逻辑
- [ ] 编写 `TransferCacheManagerTest.php` 测试用例
- [ ] 实现缓存管理器，基于现有Redis操作
- [ ] 编写 `TransferValidatorTest.php` 主验证器测试

**验收标准**：
- 验证器正确实现现有业务规则
- 缓存管理器兼容现有Redis键结构
- 小时限额检查功能完全一致

### 5.4 第四阶段：统一业务服务实现（2天）

**TDD任务清单**：
- [ ] 编写 `TransferMoneyServiceV2Test.php` 核心测试用例
- [ ] 实现 `TransferMoneyServiceV2` 主服务类
- [ ] 测试正常充值功能（基于现有 `run()` 方法逻辑）
- [ ] 测试批量充值功能（基于现有 `execute()` 方法逻辑）
- [ ] 测试定时充值功能（基于现有 `isTimeRecharge()` 逻辑）
- [ ] 测试加粉充值功能（基于现有 `addFansJob()` 逻辑）

**验收标准**：
- 新服务通过所有测试用例
- 功能与现有服务完全一致
- 支持三种充值模式

### 5.5 第五阶段：队列任务和配置系统（1天）

**TDD任务清单**：
- [ ] 编写 `TransferMoneyJobV2Test.php` 队列测试
- [ ] 实现 `TransferMoneyJobV2`，使用新服务类
- [ ] 编写配置文件测试用例
- [ ] 实现配置文件系统
- [ ] 测试配置加载和验证

**验收标准**：
- 队列任务正确使用新服务
- 配置系统灵活可扩展
- 保持现有通知机制

### 5.6 第六阶段：集成测试和文档（1天）

**TDD任务清单**：
- [ ] 编写端到端集成测试
- [ ] 测试与现有系统的兼容性
- [ ] 性能测试和基准对比
- [ ] 完善测试覆盖率报告
- [ ] 更新技术文档

**验收标准**：
- 集成测试全部通过
- 测试覆盖率达到95%以上
- 性能不低于现有系统
- 文档完整准确

## 七、风险控制和迁移策略

### 7.1 渐进式迁移

**迁移策略**：
1. **并行运行期**：新旧系统同时运行，通过配置开关控制
2. **灰度测试期**：部分流量使用新系统，监控稳定性
3. **全量切换期**：逐步将所有流量切换到新系统
4. **清理期**：确认稳定后清理旧代码

**配置开关设计**：
```php
// 在配置文件中添加开关
'use_new_transfer_system' => false, // 默认使用旧系统
'new_system_traffic_ratio' => 0,    // 新系统流量比例
```

### 7.2 兼容性保证

**兼容性措施**：
- 保持对外接口不变
- 保持错误码体系不变
- 保持缓存键命名不变
- 保持数据库操作不变

### 7.3 监控和告警

**监控指标**：
- 充值成功率
- 接口响应时间
- 错误率统计
- 缓存命中率

**告警机制**：
- 成功率低于95%时告警
- 响应时间超过5秒告警
- 错误率超过1%时告警

### 7.4 回滚机制

**回滚条件**：
- 充值成功率显著下降
- 出现严重功能异常
- 性能严重下降

**回滚步骤**：
1. 立即切换配置开关到旧系统
2. 分析问题原因
3. 修复问题后重新测试
4. 确认无误后重新切换

## 六、TDD实施优势

### 6.1 开发优势

1. **安全第一**：Mock适配器确保开发过程中绝无真实充值风险
2. **质量保证**：测试先行，确保每个功能都有测试覆盖
3. **快速反馈**：测试用例提供即时的功能验证
4. **重构安全**：完善的测试用例保证重构不会破坏功能
5. **文档作用**：测试用例本身就是最好的功能文档

### 6.2 架构优势

1. **统一服务**：消除单次和批量充值的代码重复
2. **平台解耦**：直接调用现有平台类，无需重新实现
3. **配置驱动**：业务规则可通过配置文件灵活调整
4. **职责清晰**：每个组件职责单一，便于测试和维护
5. **向后兼容**：保持现有接口，确保业务连续性

### 6.3 业务优势

1. **功能完整**：支持正常充值、定时充值、加粉充值三种模式
2. **监控完善**：使用 `Yii::info()` 和 `Yii::error()` 记录关键信息
3. **错误处理**：保持现有错误码体系和通知机制
4. **性能一致**：基于现有业务逻辑，性能表现一致

### 6.4 维护优势

1. **测试覆盖**：高测试覆盖率确保代码质量
2. **易于调试**：清晰的日志记录和错误信息
3. **扩展简单**：新平台接入只需实现适配器接口
4. **文档完善**：测试用例和技术文档双重保障

## 七、总结

本TDD实施方案通过测试驱动开发的方式，基于现有 [`TransferMoneyBatchService`](../../backendapi/services/promote/TransferMoneyBatchService.php) 的成熟业务逻辑，设计了统一的服务架构。方案具有以下特点：

- **安全可靠**：Mock适配器确保开发过程中绝无真实充值风险
- **质量保证**：测试先行的开发方式确保代码质量
- **架构统一**：消除单次和批量充值的代码重复问题
- **平台复用**：直接调用现有平台类方法，避免重复实现
- **业务完整**：支持正常充值、定时充值、加粉充值三种模式
- **向后兼容**：保持现有接口和错误码体系不变

该方案为广告预算充值业务提供了安全、可靠的重构路径，预计总工期6个工作日，能够在确保开发安全的前提下，实现系统架构的优化升级。

## 八、安全声明

**重要提醒**：
1. 开发和测试过程中，必须使用 `MockAdapter` 进行所有测试
2. 绝对禁止在开发环境中调用真实的平台充值接口
3. 所有测试用例必须通过Mock适配器验证功能正确性
4. 生产环境部署前，必须经过完整的集成测试验证
5. 任何涉及真实充值的操作都必须在生产环境中进行，且需要相关负责人确认