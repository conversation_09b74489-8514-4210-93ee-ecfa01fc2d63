# 广告预算充值业务新架构开发文档

## 概述

本文档为广告预算充值业务新架构的开发指南，包含代码规范、最佳实践、扩展开发指南、故障排查指南和性能优化建议。

## 代码规范和最佳实践

### 1. 编码规范

#### 1.1 命名规范

```php
// 类名使用 PascalCase
class TransferMoneyServiceV2 {}

// 方法名使用 camelCase
public function transferMoney() {}

// 常量使用 UPPER_SNAKE_CASE
const DEFAULT_RECHARGE_AMOUNT = 50;

// 变量使用 camelCase
$targetAdvertiserId = '1234567890';

// 私有属性使用下划线前缀
private $_cacheManager;
```

#### 1.2 文件组织

```
services/promote/transfermoneyv2/
├── TransferMoneyServiceV2.php          // 主服务类
├── platform/                           // 平台适配器
│   ├── PlatformAdapterInterface.php    // 接口定义
│   ├── AbstractPlatformAdapter.php     // 抽象基类
│   ├── TiktokAdapter.php               // 具体实现
│   └── PlatformFactory.php             // 工厂类
├── validator/                           // 验证器
├── cache/                              // 缓存管理
├── config/                             // 配置管理
└── workflow/                           // 工作流
```

#### 1.3 注释规范

```php
/**
 * 执行充值操作
 * 
 * @param string $sourceAccount 源账户ID
 * @param string $targetAccount 目标账户ID
 * @param float $amount 充值金额
 * @param string $token 访问令牌
 * @param string $organizationId 组织ID
 * @return array 充值结果
 * @throws Exception 当充值失败时抛出异常
 */
public function transferMoney(
    string $sourceAccount,
    string $targetAccount,
    float $amount,
    string $token,
    string $organizationId = ''
): array {
    // 实现代码
}
```

### 2. 设计模式最佳实践

#### 2.1 工厂模式

```php
// 平台适配器工厂
class PlatformFactory
{
    private static $adapters = [];
    
    public function create(string $platform): PlatformAdapterInterface
    {
        if (!isset(self::$adapters[$platform])) {
            $config = ConfigManager::getPlatformConfig();
            $adapterClass = $config['platforms'][$platform]['adapter_class'];
            self::$adapters[$platform] = new $adapterClass();
        }
        
        return self::$adapters[$platform];
    }
}
```

#### 2.2 策略模式

```php
// 验证器策略
interface ValidatorInterface
{
    public function validate(array $data): bool;
}

class TransferValidator
{
    private $validators = [];
    
    public function addValidator(ValidatorInterface $validator): self
    {
        $this->validators[] = $validator;
        return $this;
    }
    
    public function validate(array $data): bool
    {
        foreach ($this->validators as $validator) {
            if (!$validator->validate($data)) {
                return false;
            }
        }
        return true;
    }
}
```

#### 2.3 适配器模式

```php
// 平台适配器
class TiktokAdapter implements PlatformAdapterInterface
{
    public function transferMoney(/* ... */): array
    {
        // 调用现有的 Oceanengine 类
        $result = Oceanengine::transferCreate(/* ... */);
        
        // 转换为统一格式
        return $this->formatResponse($result);
    }
    
    private function formatResponse(array $originalResponse): array
    {
        return [
            'success' => $originalResponse['code'] === 0,
            'message' => $originalResponse['message'] ?? '',
            'platform' => 'tiktok',
            'data' => $originalResponse['data'] ?? []
        ];
    }
}
```

### 3. 错误处理最佳实践

#### 3.1 异常层次结构

```php
// 基础异常
class TransferMoneyException extends Exception {}

// 具体异常类型
class ValidationException extends TransferMoneyException {}
class PlatformException extends TransferMoneyException {}
class CacheException extends TransferMoneyException {}
class ConfigException extends TransferMoneyException {}
```

#### 3.2 错误处理模式

```php
public function transferMoney(): array
{
    try {
        // 1. 参数验证
        $this->validateParameters();
        
        // 2. 业务逻辑处理
        $result = $this->processTransfer();
        
        // 3. 成功处理
        $this->handleSuccess($result);
        
        return $result;
        
    } catch (ValidationException $e) {
        $this->setCode(423); // 推广错误码
        throw $e;
    } catch (PlatformException $e) {
        $this->setCode(422); // 技术错误码
        throw $e;
    } catch (Exception $e) {
        $this->setCode(422); // 默认技术错误码
        Yii::error('未预期的错误: ' . $e->getMessage(), 'transfer_money');
        throw new TransferMoneyException('系统内部错误', 422, $e);
    }
}
```

### 4. 日志记录最佳实践

#### 4.1 日志级别使用

```php
// 信息日志 - 正常业务流程
Yii::info('开始执行充值操作', 'transfer_money');

// 警告日志 - 可恢复的异常情况
Yii::warning('余额不足，但充值成功', 'transfer_money');

// 错误日志 - 需要关注的错误
Yii::error('充值失败: ' . $e->getMessage(), 'transfer_money');

// 调试日志 - 开发调试信息
Yii::debug('验证器链执行结果: ' . json_encode($result), 'transfer_money');
```

#### 4.2 结构化日志

```php
public function logTransferOperation(array $context): void
{
    $logData = [
        'operation' => 'transfer_money',
        'user_id' => $context['user_id'],
        'target_accounts' => $context['target_advertiser_ids'],
        'amount' => $context['amount'],
        'platform' => $this->platform,
        'timestamp' => time(),
        'execution_time' => microtime(true) - $context['start_time']
    ];
    
    Yii::info($logData, 'transfer_money_operation');
}
```

## 扩展开发指南

### 1. 添加新平台适配器

#### 1.1 创建适配器类

```php
// 1. 创建新的适配器类
class NewPlatformAdapter implements PlatformAdapterInterface
{
    public function getName(): string
    {
        return 'new_platform';
    }
    
    public function transferMoney(/* ... */): array
    {
        // 实现具体的充值逻辑
        // 调用新平台的API
        $result = NewPlatformApi::transfer(/* ... */);
        
        // 转换为统一格式
        return $this->formatResponse($result);
    }
    
    public function getBalance(string $accountId, string $token): float
    {
        // 实现余额查询逻辑
    }
    
    public function getSingleLimit(): float
    {
        return ConfigManager::getPlatformLimit('new_platform', 'single_recharge_amount');
    }
    
    public function getHourlyLimit(): float
    {
        return ConfigManager::getPlatformLimit('new_platform', 'hourly_max_amount');
    }
}
```

#### 1.2 更新配置文件

```php
// config/platform_config.php
return [
    'platforms' => [
        // 现有平台...
        'new_platform' => [
            'name' => '新平台',
            'adapter_class' => 'backendapi\services\promote\transfermoneyv2\platform\NewPlatformAdapter',
            'enabled' => true,
            'description' => '新平台适配器'
        ]
    ],
    'platform_limits' => [
        // 现有限制...
        'new_platform' => [
            'single_recharge_amount' => 1500,
            'hourly_max_amount' => 5000,
        ]
    ]
];
```

#### 1.3 编写测试用例

```php
class NewPlatformAdapterTest extends Unit
{
    public function testTransferMoney()
    {
        $adapter = new NewPlatformAdapter();
        $result = $adapter->transferMoney(/* ... */);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('new_platform', $result['platform']);
    }
    
    public function testGetBalance()
    {
        $adapter = new NewPlatformAdapter();
        $balance = $adapter->getBalance('test_account', 'test_token');
        
        $this->assertGreaterThanOrEqual(0, $balance);
    }
}
```

### 2. 添加新验证器

#### 2.1 创建验证器类

```php
class CustomValidator extends AbstractValidator
{
    public function validate(array $data): bool
    {
        // 实现自定义验证逻辑
        if (!$this->validateCustomRule($data)) {
            throw new ValidationException('自定义验证失败');
        }
        
        return true;
    }
    
    private function validateCustomRule(array $data): bool
    {
        // 具体验证逻辑
        return true;
    }
    
    public function getValidatorInfo(): array
    {
        return [
            'name' => 'CustomValidator',
            'description' => '自定义验证器',
            'rules' => ['custom_rule']
        ];
    }
}
```

#### 2.2 注册验证器

```php
// 在 TransferValidator 中添加
public static function createWithCustomValidator(): TransferValidator
{
    return self::createDefault()
        ->addValidator(new CustomValidator());
}
```

### 3. 添加新工作流

#### 3.1 创建工作流类

```php
class CustomWorkflow
{
    private $service;
    private $config;
    
    public function __construct(TransferMoneyServiceV2 $service = null, array $config = [])
    {
        $this->service = $service ?: new TransferMoneyServiceV2();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }
    
    public function execute(array $params): array
    {
        try {
            // 1. 初始化
            $this->initialize($params);
            
            // 2. 执行自定义逻辑
            $result = $this->processCustomLogic($params);
            
            // 3. 后处理
            $this->finalize($result);
            
            return [
                'success' => true,
                'result' => $result,
                'workflow' => 'custom'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'workflow' => 'custom'
            ];
        }
    }
    
    private function processCustomLogic(array $params): array
    {
        // 实现自定义工作流逻辑
        return [];
    }
}
```

## 故障排查指南

### 1. 常见问题和解决方案

#### 1.1 充值失败问题

**问题现象**: 充值操作返回失败状态

**排查步骤**:
1. 检查日志文件 `@runtime/logs/app.log`
2. 确认平台适配器配置正确
3. 验证账户ID和金额参数
4. 检查网络连接和API可用性

```php
// 调试代码示例
public function debugTransferFailure(array $params): array
{
    $debugInfo = [
        'params' => $params,
        'platform' => $this->platform,
        'adapter_config' => $this->platformFactory->getAdapterConfig($this->platform),
        'validation_result' => null,
        'api_response' => null
    ];
    
    try {
        // 验证参数
        $validator = TransferValidator::createDefault();
        $debugInfo['validation_result'] = $validator->validate($params);
        
        // 调用API
        $adapter = $this->platformFactory->create($this->platform);
        $debugInfo['api_response'] = $adapter->transferMoney(/* ... */);
        
    } catch (Exception $e) {
        $debugInfo['error'] = [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
    
    Yii::error('充值失败调试信息: ' . json_encode($debugInfo), 'transfer_debug');
    return $debugInfo;
}
```

#### 1.2 配置加载问题

**问题现象**: 配置文件无法加载或配置项缺失

**排查步骤**:
1. 检查配置文件路径和权限
2. 验证配置文件语法
3. 清除配置缓存

```php
// 配置诊断工具
public function diagnoseConfig(): array
{
    $diagnosis = [
        'config_files' => [],
        'cache_status' => [],
        'validation_errors' => []
    ];
    
    $configFiles = [
        'platform' => 'config/platform_config.php',
        'limits' => 'config/transfer_limits.php',
        'budget' => 'config/budget_rules.php'
    ];
    
    foreach ($configFiles as $type => $file) {
        $filePath = __DIR__ . '/' . $file;
        $diagnosis['config_files'][$type] = [
            'path' => $filePath,
            'exists' => file_exists($filePath),
            'readable' => is_readable($filePath),
            'size' => file_exists($filePath) ? filesize($filePath) : 0
        ];
        
        if (file_exists($filePath)) {
            try {
                $config = require $filePath;
                $diagnosis['config_files'][$type]['valid'] = is_array($config);
            } catch (Exception $e) {
                $diagnosis['validation_errors'][$type] = $e->getMessage();
            }
        }
    }
    
    return $diagnosis;
}
```

#### 1.3 缓存问题

**问题现象**: 缓存数据不一致或缓存失效

**排查步骤**:
1. 检查Redis连接状态
2. 验证缓存键命名
3. 检查缓存过期时间设置

```php
// 缓存诊断工具
public function diagnoseCache(): array
{
    $diagnosis = [
        'connection' => false,
        'keys' => [],
        'operations' => []
    ];
    
    try {
        $cache = Yii::$app->cache;
        
        // 测试连接
        $testKey = 'cache_test_' . time();
        $testValue = 'test_value';
        
        $diagnosis['operations']['set'] = $cache->set($testKey, $testValue, 60);
        $diagnosis['operations']['get'] = $cache->get($testKey) === $testValue;
        $diagnosis['operations']['delete'] = $cache->delete($testKey);
        
        $diagnosis['connection'] = $diagnosis['operations']['set'] && 
                                  $diagnosis['operations']['get'] && 
                                  $diagnosis['operations']['delete'];
        
        // 检查现有缓存键
        $commonKeys = [
            'transferMoneyData:1234567890',
            'transferMoneyBalance:1234567890',
            'AddFansTransferMoneyCount:1234567890'
        ];
        
        foreach ($commonKeys as $key) {
            $diagnosis['keys'][$key] = [
                'exists' => $cache->exists($key),
                'value' => $cache->get($key)
            ];
        }
        
    } catch (Exception $e) {
        $diagnosis['error'] = $e->getMessage();
    }
    
    return $diagnosis;
}
```

### 2. 性能问题排查

#### 2.1 慢查询分析

```php
public function analyzePerformance(array $params): array
{
    $startTime = microtime(true);
    $startMemory = memory_get_usage();
    
    $performance = [
        'steps' => [],
        'total_time' => 0,
        'memory_usage' => 0,
        'bottlenecks' => []
    ];
    
    // 步骤1: 参数验证
    $stepStart = microtime(true);
    $validator = TransferValidator::createDefault();
    $validator->validate($params);
    $stepTime = microtime(true) - $stepStart;
    $performance['steps']['validation'] = $stepTime;
    
    if ($stepTime > 0.1) {
        $performance['bottlenecks'][] = 'validation_slow';
    }
    
    // 步骤2: 平台适配器调用
    $stepStart = microtime(true);
    $adapter = $this->platformFactory->create($this->platform);
    $stepTime = microtime(true) - $stepStart;
    $performance['steps']['adapter_creation'] = $stepTime;
    
    // 步骤3: 缓存操作
    $stepStart = microtime(true);
    $this->cacheManager->getTransferHistory('test_account');
    $stepTime = microtime(true) - $stepStart;
    $performance['steps']['cache_operation'] = $stepTime;
    
    if ($stepTime > 0.05) {
        $performance['bottlenecks'][] = 'cache_slow';
    }
    
    $performance['total_time'] = microtime(true) - $startTime;
    $performance['memory_usage'] = memory_get_usage() - $startMemory;
    
    return $performance;
}
```

## 性能优化建议

### 1. 缓存优化

#### 1.1 缓存策略

```php
class OptimizedCacheManager extends TransferCacheManager
{
    // 使用多级缓存
    private $localCache = [];
    
    public function getTransferHistory(string $targetId): array
    {
        // 1. 本地缓存
        if (isset($this->localCache[$targetId])) {
            return $this->localCache[$targetId];
        }
        
        // 2. Redis缓存
        $key = $this->getTransferDataKey($targetId);
        $data = $this->cache->get($key);
        
        if ($data !== false) {
            $this->localCache[$targetId] = $data;
            return $data;
        }
        
        // 3. 数据库查询（如果需要）
        $data = $this->loadFromDatabase($targetId);
        
        // 更新缓存
        $this->cache->set($key, $data, 3600);
        $this->localCache[$targetId] = $data;
        
        return $data;
    }
}
```

#### 1.2 缓存预热

```php
public function warmupCache(): void
{
    // 预热常用配置
    ConfigManager::getPlatformConfig();
    ConfigManager::getTransferLimitsConfig();
    ConfigManager::getBudgetRulesConfig();
    
    // 预热平台适配器
    $platforms = ['tiktok', 'adq', 'mock'];
    foreach ($platforms as $platform) {
        $this->platformFactory->create($platform);
    }
    
    // 预热验证器
    TransferValidator::createDefault();
}
```

### 2. 数据库优化

#### 2.1 查询优化

```php
// 使用索引优化查询
class OptimizedQueries
{
    public function getAccountInfo(string $subAdvertiserId): array
    {
        // 使用索引字段查询
        return AdsAccountSub::find()
            ->select(['id', 'sub_advertiser_id', 'sub_advertiser_name', 'main_body_id'])
            ->where(['sub_advertiser_id' => $subAdvertiserId])
            ->with(['mainBody' => function($query) {
                $query->select(['id', 'name']);
            }])
            ->asArray()
            ->one();
    }
}
```

#### 2.2 批量操作优化

```php
public function batchUpdateAccounts(array $accounts): void
{
    // 使用批量更新而不是逐个更新
    $cases = [];
    $ids = [];
    
    foreach ($accounts as $account) {
        $cases[] = "WHEN id = {$account['id']} THEN '{$account['status']}'";
        $ids[] = $account['id'];
    }
    
    $caseSql = implode(' ', $cases);
    $idsSql = implode(',', $ids);
    
    $sql = "UPDATE ads_account_sub SET status = CASE {$caseSql} END WHERE id IN ({$idsSql})";
    Yii::$app->db->createCommand($sql)->execute();
}
```

### 3. 内存优化

#### 3.1 对象池模式

```php
class AdapterPool
{
    private static $pool = [];
    private static $maxSize = 10;
    
    public static function getAdapter(string $platform): PlatformAdapterInterface
    {
        if (!isset(self::$pool[$platform])) {
            self::$pool[$platform] = [];
        }
        
        if (!empty(self::$pool[$platform])) {
            return array_pop(self::$pool[$platform]);
        }
        
        return PlatformFactory::create($platform);
    }
    
    public static function returnAdapter(string $platform, PlatformAdapterInterface $adapter): void
    {
        if (count(self::$pool[$platform]) < self::$maxSize) {
            $adapter->reset(); // 重置状态
            self::$pool[$platform][] = $adapter;
        }
    }
}
```

#### 3.2 内存监控

```php
public function monitorMemoryUsage(): array
{
    return [
        'current_usage' => memory_get_usage(true),
        'peak_usage' => memory_get_peak_usage(true),
        'limit' => ini_get('memory_limit'),
        'usage_percentage' => (memory_get_usage(true) / $this->parseMemoryLimit(ini_get('memory_limit'))) * 100
    ];
}

private function parseMemoryLimit(string $limit): int
{
    $unit = strtolower(substr($limit, -1));
    $value = (int)substr($limit, 0, -1);
    
    switch ($unit) {
        case 'g': return $value * 1024 * 1024 * 1024;
        case 'm': return $value * 1024 * 1024;
        case 'k': return $value * 1024;
        default: return $value;
    }
}
```

### 4. 并发优化

#### 4.1 异步处理

```php
class AsyncTransferProcessor
{
    public function processAsync(array $batchData): array
    {
        $promises = [];
        
        foreach ($batchData as $data) {
            $promises[] = $this->createAsyncTask($data);
        }
        
        // 等待所有任务完成
        return $this->waitForAll($promises);
    }
    
    private function createAsyncTask(array $data): callable
    {
        return function() use ($data) {
            $service = new TransferMoneyServiceV2();
            return $service->execute($data);
        };
    }
}
```

#### 4.2 连接池

```php
class ConnectionPool
{
    private static $connections = [];
    private static $maxConnections = 20;
    
    public static function getConnection(string $type): object
    {
        if (!isset(self::$connections[$type])) {
            self::$connections[$type] = [];
        }
        
        if (!empty(self::$connections[$type])) {
            return array_pop(self::$connections[$type]);
        }
        
        return self::createConnection($type);
    }
    
    public static function returnConnection(string $type, object $connection): void
    {
        if (count(self::$connections[$type]) < self::$maxConnections) {
            self::$connections[$type][] = $connection;
        }
    }
}
```

## 总结

本开发文档提供了：

1. **代码规范**: 统一的编码标准和最佳实践
2. **设计模式**: 常用设计模式的实现指南
3. **扩展开发**: 新功能开发的详细步骤
4. **故障排查**: 常见问题的诊断和解决方案
5. **性能优化**: 系统性能优化的具体建议

遵循这些指南可以确保代码质量、系统稳定性和开发效率。