<?php

namespace common\helpers;

use Yii;
use yii\helpers\Html;
use yii\helpers\Json;
use common\components\AliyunOss;
use Exception;

/**
 * Class ImageHelper
 * @package common\helpers
 *
 */
class ImageHelper
{
    /**
     * 默认图片
     *
     * @param $imgSrc
     * @param string $defaultImgSre
     * @return string
     */
    public static function default($imgSrc, $defaultImgSre = '/resources/img/error.png')
    {
        return !empty($imgSrc) ? $imgSrc : Yii::getAlias('@web') . $defaultImgSre;
    }

    /**
     * 默认头像
     *
     * @param $imgSrc
     * @param string $defaultImgSre
     * @return string
     */
    public static function defaultHeaderPortrait($imgSrc, $defaultImgSre = '/resources/img/profile_small.jpg')
    {
        return !empty($imgSrc) ? $imgSrc : Yii::getAlias('@web') . $defaultImgSre;
    }

    /**
     * 点击大图
     *
     * @param string $imgSrc
     * @param int $width 宽度 默认45px
     * @param int $height 高度 默认45px
     * @return string
     */
    public static function fancyBox($imgSrc, $width = 45, $height = 45)
    {
        $image = Html::img($imgSrc, [
            'width' => $width,
            'height' => $height,
        ]);

        return Html::a($image, $imgSrc, [
            'data-fancybox' => 'gallery'
        ]);
    }

    /**
     * 显示图片列表
     *
     * @param $covers
     * @return string
     */
    public static function fancyBoxs($covers, $width = 45, $height = 45)
    {
        $image = '';
        if (empty($covers)) {
            return $image;
        }

        !is_array($covers) && $covers = Json::decode($covers);

        foreach ($covers as $cover) {
            $image .= Html::tag('span', self::fancyBox($cover, $width, $height), [
                'style' => 'padding-right:5px;padding-bottom:5px'
            ]);
        }

        return $image;
    }

    /**
     * 判断是否图片地址
     *
     * @param string $imgSrc
     * @return bool
     */
    public static function isImg($imgSrc)
    {
        $extend = StringHelper::clipping($imgSrc, '.', 1);

        $imgExtends = [
            'bmp',
            'jpg',
            'gif',
            'jpeg',
            'jpe',
            'jpg',
            'png',
            'jif',
            'dib',
            'rle',
            'emf',
            'pcx',
            'dcx',
            'pic',
            'tga',
            'tif',
            'tiffxif',
            'wmf',
            'jfif'
        ];
        if (in_array($extend, $imgExtends) || strpos($imgSrc, 'http://wx.qlogo.cn') !== false) {
            return true;
        }

        return false;
    }

    /**
     * 处理飞书多维表格图片，下载并上传到OSS
     *
     * @param array $images 飞书图片数据数组
     * @param object $feishuComponent 飞书组件实例，用于获取临时下载地址
     * @param string $ossSaveFilePath OSS保存路径
     * @return array OSS图片URL数组
     */
    public static function processFeishuImages(array $images, $feishuComponent, $ossSaveFilePath = 'chz/erp_bucket/images/')
    {
        if (empty($images)) {
            return [];
        }
        
        $ossUrls = [];
        
        // 提取所有 file_token
        $fileTokens = [];
        foreach ($images as $image) {
            if (isset($image['file_token'])) {
                $fileTokens[] = $image['file_token'];
            }
        }
        
        if (empty($fileTokens)) {
            return [];
        }
        
        foreach ($fileTokens as $fileToken) {
            $tmpUrls = $feishuComponent->getTmpDownloadUrl($fileToken);

            if ($tmpUrls['code'] != 0) {
                Yii::error('获取飞书图片临时下载地址失败：' . $tmpUrls['msg'], __METHOD__);
                continue;
            }

            $item = $tmpUrls['data']['tmp_download_urls'][0];
            try {
                $fileToken = $item['file_token'];
                $tmpDownloadUrl = $item['tmp_download_url'];
                $fileName = $fileToken . '.jpg';  // 使用file_token作为文件名
                
                // 下载并上传到OSS
                $result = AliyunOss::saveImgToOss($tmpDownloadUrl, $fileName, $ossSaveFilePath);
                
                if ($result && isset($result['url'])) {
                    $ossUrls[] = $result['url'];
                }
            } catch (Exception $e) {
                Yii::error('处理飞书图片失败：' . $e->getMessage(), __METHOD__);
                // 继续处理其他图片，不中断整个流程
            }
        }
        
        return $ossUrls;
    }
}