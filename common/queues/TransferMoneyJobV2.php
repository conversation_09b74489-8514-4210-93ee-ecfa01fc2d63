<?php

namespace common\queues;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\config\ConfigManager;
use common\models\Config;
use common\models\backend\Member;
use common\helpers\BcHelper;
use Exception;
use services\common\FeishuExamineService;
use Yii;

/**
 * 推广定时充值队列任务 V2
 *
 * 基于现有 TransferMoneyJob 的业务逻辑，使用新的 TransferMoneyServiceV2 服务
 * 保持与现有队列系统的完全兼容性
 * 
 * 核心功能：
 * - 支持立即充值和定时充值
 * - 支持加粉后自动充值
 * - 支持批量充值处理
 * - 支持充值频次控制
 * - 支持消息通知机制
 */
class TransferMoneyJobV2 extends BaseJob
{
    /**
     * 延迟时间：单位秒，默认10
     */
    public $delay = 10;

    /**
     * 重试次数
     */
    public $retryTimes = 1;

    /**
     * 数据
     */
    public $data;

    /**
     * 是否发送消息
     */
    public $isSendMessage = true;

    /**
     * @var TransferMoneyServiceV2 充值服务
     */
    private $transferService;

    /**
     * 构造函数
     * 
     * @param array $config 配置参数
     */
    public function __construct($config = [])
    {
        parent::__construct($config);
        $this->transferService = new TransferMoneyServiceV2();
    }

    /**
     * 执行队列任务
     * 
     * @param mixed $queue 队列实例
     * @return bool
     */
    public function run($queue)
    {
        try {
            $data = $this->data;
            $res = $this->transferService->execute($data);
            $arrError = $this->transferService->resRealData($res);
        } catch (Exception $e) {
            $arrError[422] = ['422' => $e->getMessage()];
        }

        $this->sendMsg($arrError);
        return true;
    }

    /**
     * 添加任务到队列
     * 
     * 基于现有 TransferMoneyJob::addJob() 方法逻辑
     * 
     * @param array $data 任务数据
     * @return bool
     */
    public static function addJob(array $data)
    {
        $data['create_time'] = time();
        $que = Yii::$app->que;
        
        $job = new static([
            'data' => $data
        ]);

        // 非定时充值设置为重要任务
        if (!$data['isTimeRecharge']) {
            $que->setImportant();
        }

        // 检查任务是否已存在
        if ($que->has($job)) {
            return true;
        }

        // 计算延迟时间
        $delay = BcHelper::sub($data['execute_time'], $data['create_time']);
        $que->delay($delay)->push($job);
        
        return true;
    }

    /**
     * 添加加粉充值任务
     * 
     * 基于现有 TransferMoneyJob::addFansJob() 方法逻辑
     * 
     * @param string $sub_advertiser_id 子账户ID
     * @return bool
     */
    public static function addFansJob($sub_advertiser_id)
    {
        // 获取充值白名单账户
        $transferAccount = Config::getByName('transferAccount');
        $transferAccount = explode("\n", $transferAccount);

        if (empty($sub_advertiser_id) || !in_array($sub_advertiser_id, $transferAccount)) {
            return false;
        }

        // 检查充值频次
        if (!self::checkTransferMoneyCount($sub_advertiser_id)) {
            return false;
        }

        // 构建充值数据
        $transferData = [
            'target_advertiser_ids' => [$sub_advertiser_id],
            'amount' => ConfigManager::getTransferLimitsConfig('fans_recharge_limits.auto_amount', 50),
            'user_name' => '系统自动充值',
        ];

        $que = Yii::$app->que;
        $job = new static([
            'data' => $transferData,
            'isSendMessage' => false
        ]);

        // 检查任务是否已存在
        if ($que->has($job)) {
            return true;
        }

        $que->push($job);
        return true;
    }

    /**
     * 检查单个账户充值频次
     * 
     * 基于现有 TransferMoneyJob::checkTransferMoneyCount() 方法逻辑
     * 检查单个账户在一分钟内的充值次数，不能超过5次，且当天不允许再充值
     * 
     * @param string $sub_advertiser_id 子账户ID
     * @return bool
     */
    public static function checkTransferMoneyCount($sub_advertiser_id)
    {
        $redis = Yii::$app->cache;
        $today = date('Y-m-d');
        $dayKey = 'AddFansTransferMoneyCount:' . $today;
        
        // 检查当天是否已被限制
        $dayAccount = $redis->get($dayKey);
        if ($dayAccount && in_array($sub_advertiser_id, $dayAccount)) {
            return false;
        }

        $redisKey = 'AddFansTransferMoneyCount:' . $sub_advertiser_id;
        $count = $redis->get($redisKey);

        if ($count === false) {
            $count = 1;
        } else {
            $count = intval($count) + 1;
            
            // 检查是否超过限制次数
            $maxAttempts = ConfigManager::getTransferLimitsConfig('fans_recharge_limits.max_attempts_per_minute', 5);
            if ($count > $maxAttempts) {
                // 添加到当天限制列表
                $dayDelayTime = strtotime($today . ' +1 day') - time();
                $dayAccount = $dayAccount ?: [];
                $dayAccount[] = $sub_advertiser_id;
                $redis->set($dayKey, $dayAccount, $dayDelayTime);

                // 发送告警通知
                $group = FeishuExamineService::arrGroup('GGGLGTQ');
                $error = '账户ID：' . $sub_advertiser_id . PHP_EOL;
                $error .= '加粉异常,在一分钟内充值超过' . $maxAttempts . '次，已被限制充值';
                Yii::$app->feishuNotice->text($error, $group['chat_id']);
                
                return false;
            }
        }

        // 设置计数缓存，5分钟过期
        $cacheTime = ConfigManager::getTransferLimitsConfig('fans_recharge_limits.check_interval', 300);
        $redis->set($redisKey, $count, $cacheTime);
        
        return true;
    }

    /**
     * 发送消息通知
     * 
     * 基于现有 TransferMoneyJob::sendMsg() 方法逻辑
     * 
     * @param array $arrError 错误信息数组
     * @return bool
     */
    public function sendMsg($arrError)
    {
        if (!$this->isSendMessage) {
            return false;
        }

        $content = '';
        $index = 0;
        $total = count($arrError);
        $is_success = false;

        foreach ($arrError as $code => $value) {
            if ($index > 0 && $index < $total) {
                $content .= PHP_EOL;
            }

            if ($code == 200) {
                $is_success = true;
                continue;
            }

            if ($code == 201) {
                $group = FeishuExamineService::arrGroup('GGGLGTQ');
                Yii::$app->feishuNotice->text($value['msg'], $group['chat_id']);
                $is_success = true;
                continue;
            }

            $content .= $value['msg'];
            $index++;
        }

        $sendUserId = $this->getSendUserId();
        $msg = '';
        $title = $this->data['isTimeRecharge'] ? '定时充值' : '推广充值';

        if (empty($content) && $is_success) {
            if (!$this->data['isTimeRecharge']) {
                return true;
            }
            $msg = '<b>' . $title . '成功</b>' . PHP_EOL . PHP_EOL;
            $msg .= '充值账户ID：' . implode(',', $this->data['target_advertiser_ids']) . PHP_EOL;
            $msg .= '充值金额：' . $this->data['amount'];
        }

        if ($content && $is_success) {
            $msg = '<b>' . $title . '部分充值成功</b>' . PHP_EOL . PHP_EOL;
            $msg .= '充值账户ID：' . implode(',', $this->data['target_advertiser_ids']) . PHP_EOL;
            $msg .= '充值金额：' . $this->data['amount'] . PHP_EOL;
            $msg .= '充值失败项：' . PHP_EOL . $content;
        }

        if ($content && !$is_success) {
            $msg = '<b>' . $title . '失败</b>' . PHP_EOL . PHP_EOL;
            $msg .= '充值账户ID：' . implode(',', $this->data['target_advertiser_ids']) . PHP_EOL;
            $msg .= '充值金额：' . $this->data['amount'] . PHP_EOL;
            $msg .= '充值失败项：' . PHP_EOL . $content;
        }

        if ($sendUserId && $msg) {
            Yii::$app->feishuNotice->text($msg, $sendUserId, 'user_id');
        } else {
            $msg .= PHP_EOL . '操作人：' . $this->data['user_name'];
            Yii::$app->feishuNotice->text($msg);
        }

        return true;
    }

    /**
     * 获取发送用户ID
     * 
     * 基于现有 TransferMoneyJob::getSendUserId() 方法逻辑
     * 
     * @return string|null
     */
    public function getSendUserId()
    {
        $feishu_user_id = Member::find()
            ->select('feishu_userid')
            ->where(['username' => $this->data['user_name']])
            ->scalar();
            
        return $feishu_user_id;
    }

    /**
     * 获取任务唯一标识
     * 
     * @return string
     */
    public function getJobKey()
    {
        // 基于任务数据生成唯一标识
        $key = parent::getJobKey();
        if (isset($this->data['target_advertiser_ids']) && isset($this->data['amount'])) {
            $key .= md5(serialize([
                'target_advertiser_ids' => $this->data['target_advertiser_ids'],
                'amount' => $this->data['amount'],
                'execute_time' => $this->data['execute_time'] ?? time(),
            ]));
        }
        return $key;
    }

    /**
     * 设置充值服务实例（用于测试）
     * 
     * @param TransferMoneyServiceV2 $service
     */
    public function setTransferService(TransferMoneyServiceV2 $service)
    {
        $this->transferService = $service;
    }

    /**
     * 获取充值服务实例
     * 
     * @return TransferMoneyServiceV2
     */
    public function getTransferService()
    {
        return $this->transferService;
    }

    /**
     * 批量添加充值任务
     * 
     * @param array $batchData 批量任务数据
     * @return array 添加结果
     */
    public static function addBatchJobs(array $batchData)
    {
        $results = [];
        $maxBatchSize = ConfigManager::getBatchLimit('max_accounts_per_batch');
        
        foreach ($batchData as $index => $data) {
            try {
                // 检查批量大小限制
                if (isset($data['target_advertiser_ids']) && 
                    count($data['target_advertiser_ids']) > $maxBatchSize) {
                    throw new Exception("批量充值账户数量不能超过 {$maxBatchSize} 个");
                }
                
                $result = self::addJob($data);
                $results[$index] = [
                    'success' => $result,
                    'message' => $result ? '任务添加成功' : '任务添加失败'
                ];
            } catch (Exception $e) {
                $results[$index] = [
                    'success' => false,
                    'message' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }

    /**
     * 获取任务统计信息
     * 
     * @return array
     */
    public static function getJobStats()
    {
        $redis = Yii::$app->cache;
        $today = date('Y-m-d');
        
        return [
            'daily_restricted_accounts' => count($redis->get('AddFansTransferMoneyCount:' . $today) ?: []),
            'config_version' => ConfigManager::getAllConfigVersions(),
            'limits' => [
                'max_batch_size' => ConfigManager::getBatchLimit('max_accounts_per_batch'),
                'fans_auto_amount' => ConfigManager::getTransferLimitsConfig('fans_recharge_limits.auto_amount'),
                'max_attempts_per_minute' => ConfigManager::getTransferLimitsConfig('fans_recharge_limits.max_attempts_per_minute'),
            ]
        ];
    }

    /**
     * 清理过期的限制记录
     * 
     * @param int $days 清理多少天前的记录
     * @return int 清理的记录数
     */
    public static function cleanupExpiredRestrictions($days = 7)
    {
        $redis = Yii::$app->cache;
        $cleanupCount = 0;
        
        for ($i = $days; $i > 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $key = 'AddFansTransferMoneyCount:' . $date;
            
            if ($redis->delete($key)) {
                $cleanupCount++;
            }
        }
        
        return $cleanupCount;
    }
}