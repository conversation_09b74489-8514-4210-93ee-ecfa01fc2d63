<?php

namespace common\queues;

/**
 * 飞书消息通知
 *
 * Class FeishuNoticeJob
 */

use common\components\Feishu;
use common\models\backend\Member;
use common\models\feishu\App;
use common\models\member\FeishuUser;
use common\queues\BaseJob;
use Yii;
use Exception;

class FeishuNoticeJob extends BaseJob
{
    // 时间内限制的次数：默认0不限制
    public $times = 5;
    // 限制的时间范围：单位秒，默认一分钟
    public $time = 1;
    // 延迟时间：单位秒，默认10
    public $delay = 1;
    //重试次数
    public $retryTimes = 0;
    //消息接收者的ID
    public $receive_id;
    //消息接受者类型
    public $receive_id_type = 'chat_id';
    //消息类型
    public $msg_type = 'text';
    //消息内容
    public $content = '';

    public function run($queue)
    {
        try {
            if ($this->isSendSameMsg()) {
                return true;
            }

            if (empty($this->content)) {
                return true;
            }

            if ($this->receive_id_type == 'union_id') {
                $appCode = FeishuUser::find()->alias('fu')->select(['fa.code'])->leftJoin(['fa' => App::tableName()], 'fa.id = fu.feishu_app_id')->where(['fu.feishu_unionid' => $this->receive_id])->scalar();
                if (empty($appCode)) {
                    throw new Exception('飞书union_id为' . $this->receive_id . '的用户没有绑定飞书应用');
                }
                $robot = new Feishu($appCode);
            } elseif ($this->receive_id_type == 'user_id') {
                $appCode = Member::find()->alias('m')->select(['fa.code'])
                    ->leftJoin(['fa' => App::tableName()], 'fa.id = m.current_feishu_app_id')
                    ->where(['m.feishu_userid' => $this->receive_id])
                    ->scalar();
                if (empty($appCode)) {
                    throw new Exception('飞书user_id为' . $this->receive_id . '的用户没有绑定飞书应用');
                }
                $robot = new Feishu($appCode);
            } else {
                $robot = new Feishu();
            }
            if ($this->msg_type == 'interactive') { //卡片
                $robot->sendWorkNotice($this->content,  $this->receive_id, 'interactive', $this->receive_id_type);
            } else { //消息通知
                $content = ['text' => $this->content];
                $robot->sendWorkNotice($content, $this->receive_id, $this->msg_type, $this->receive_id_type);
            }
        } catch (Exception $e) {
            echo "飞书消息发送失败:" . $e->getMessage();
        }
        return true;
    }

    /**
     * 5 秒内发送过相同的内容则视为相同消息
     *
     * @return boolean
     */
    public function isSendSameMsg()
    {
        if (!is_string($this->content)) {
            $content = json_encode($this->content);
        } else {
            $content = $this->content;
        }
        $sameKey = "feishuLog:{$this->receive_id}:" . md5($content);
        $isSendTheSameMsg = Yii::$app->cache->exists($sameKey);
        Yii::$app->cache->set($sameKey, $this->content . '------time: ' . time(), 5);
        return $isSendTheSameMsg;
    }

    public static function addJob($receive_id, $msg_type, $content, $receive_id_type = 'chat_id')
    {
        Yii::$app->que->push(new self(['receive_id' => $receive_id, 'msg_type' => $msg_type, 'content' => $content, 'receive_id_type' => $receive_id_type]));
    }
}
