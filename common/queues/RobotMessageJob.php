<?php

namespace common\queues;

use common\services\wxcom\RobotMessageService;
use Yii;
use Exception;

/**
 * Class 企业微信机器人会话存档队列
 * @package common\queues
 *
 */
class RobotMessageJob extends BaseJob
{
    public $message;

    /**
     * 任务执行
     *
     * @return void
     */
    public function run($queue)
    {
        if (empty($this->message) || !is_array($this->message)) {
            return true;
        }

        try {
            RobotMessageService::receiveMsg($this->message);
        } catch (Exception $e) {
            $error = [
                'type' => '企业微信机器人会话存档队列,报错',
                'message' => $this->message,
                'error' => $e->getMessage(),
            ];
            Yii::$app->feishuNotice->text($error);
            Yii::error($error, 'RobotMessageJob');
        }

        return true;
    }

    /**
     * 添加任务
     *
     * @param array $data
     * @return void
     */
    public static function addJob(array $message)
    {
        // $job = new self([
        //     'message' => $message,
        // ]);
        // $job->run(Yii::$app->queue);
        Yii::$app->que->push(new self(['message' => $message]));
    }
}
