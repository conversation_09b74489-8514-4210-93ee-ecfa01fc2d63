<?php

namespace common\queues;

/**
 * 偏远标签数据处理
 *
 * Class RemoteTagDataJob
 */

use auth\services\data\ServicerAnalysisService;
use common\models\common\AdsAccountData;
use common\services\promote\AdsAccountDataService;
use Exception;
use Yii;

class RemoteTagDataJob extends BaseJob
{
    //延迟时间：单位秒，默认10
    public $delay = 30;
    //重试次数
    public $retryTimes = 1;
    //数据
    public $data;

    public function run($queue)
    {
        try {
            if (empty($this->data['add_time']) || empty($this->data['entity_id'])) {
                return;
            }

            //统计客服数据分析
            ServicerAnalysisService::cal(date("Y-m-d", $this->data['add_time']));

            $date = date("Ymd", $this->data['add_time']);
            $current_date = date("Ymd", time());
            if ($date == $current_date) {
                return true;
            }

            $startTime = strtotime($date);
            $endTime = $startTime + 86399;
            // 更新广告每日数据-偏远数
            AdsAccountData::updateAll(['remote_count' => 0], ['date' => $date, 'entity_id' => $this->data['entity_id']]);
            AdsAccountDataService::getRemoteCount($startTime, $endTime, $this->data['entity_id']);
        } catch (Exception $e) {
            if ($this->retryTimes >= 2) {
                Yii::$app->feishuNotice->error('处理偏远标签数据数据失败了,原因：' . $e->getMessage());
                Yii::info('处理偏远标签数据数据失败原因:' . $e->getMessage() . ',data=>' . json_encode($this->data));
                return true;
            }
            $this->retryTimes++;
            Yii::$app->que->delay(10)->push($this);
        }

        return true;
    }

    public static function addJob(array $data)
    {
        $que = Yii::$app->que;
        $job = new static([
            'data' => $data
        ]);

        if ($que->has($job)) {
            return true;
        }

        $que->push($job);
    }
}
