<?php

namespace common\queues;

/**
 * 财务数据
 *
 * Class FinancialDataJob
 */

use console\services\OrderHeaderService;
use Exception;
use Yii;

class FinancialDataJob extends BaseJob
{
    // 延迟时间：单位秒，默认10
    public $delay = 30;
    //重试次数
    public $retryTimes = 1;
    //日期
    public $date = '';
    //数据
    public $data;

    public function run($queue)
    {
        try {
            $data = $this->data;
            OrderHeaderService::calcFinancialData($data['entity_id'], $data['order_id'], $data['start_time'], $data['end_time']);
        } catch (Exception $e) {
            if ($this->retryTimes >= 3) {
                Yii::info('订单:'.$this->data['order_id'].',执行财务数据失败,原因:'. $e->getMessage().',data=>'.json_encode($this->data));
                return true;
            }
            $this->retryTimes++;
            Yii::$app->que->delay(10)->push($this);
        }

        return true;
    }

    public static function addJob(array $data)
    {
        $que = Yii::$app->que;
        $job = new static([
            'data' => $data
        ]);

        $isExists = $que->has($job);
        if ($isExists) {
            return true;
        }
        
        $que->delay(3)->push($job);
    }

    protected function buildJobId()
    {
        $this->jobId = $this->data['order_id'];
    }

}