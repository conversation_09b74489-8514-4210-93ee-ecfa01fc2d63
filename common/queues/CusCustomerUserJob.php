<?php

namespace common\queues;

use common\models\wxcom\CusCustomerUser;
use common\services\wxcom\CusCustomerUserService;
use Exception;
use Yii;

/**
 * 客户-处理
 * 
 * @package common\queues
 *
 */
class CusCustomerUserJob extends BaseJob
{
    public $id;
    //操作类型
    public $type;
    public function run($queue)
    {
        try {
            /**@var CusCustomerUserService $cusUser */
            $cusUser = CusCustomerUserService::find()->where(['id' => $this->id])->one();
            if (!$cusUser) {
                return false;
            }

            if ($this->type == 'dealIp') {
                //处理ip
                $cusUser->dealIp();
            } else {
                //检测是否”疑似钓鱼“打标签
                $cusUser->isPossibleFishing();
            }

            return true;
        } catch (Exception $e) {
            $data = [
                'id' => $this->id,
                'type' => $this->type
            ];
            Yii::error('客户-处理失败：' . $e->getMessage() . ',数据：' . json_encode($data, 256), 'CusCustomerUserJob');
        }
    }

    public static function addJob(int $id)
    {
        $job = new static([
            'id' => $id,
            'type' => 'isPossibleFishing',
        ]);

        $que = Yii::$app->que;
        if ($que->has($job)) {
            return true;
        }

        $que->delay(60)->push($job);
    }

    public static function addJobDealIp(int $id)
    {
        $job = new static([
            'id' => $id,
            'type' => 'dealIp',
        ]);

        $que = Yii::$app->que;
        if ($que->has($job)) {
            return true;
        }

        $que->delay(10)->push($job);
    }
}
