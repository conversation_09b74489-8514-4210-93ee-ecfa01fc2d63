<?php

namespace common\queues;

use common\services\log\CommonLogService;
use Yii;

/**
 * Class 日志保存类
 * @package common\queues
 *
 */
class LogJob extends BaseJob
{
    public $data;

    /**
     * 任务执行
     *
     * @return void
     */
    public function run($queue)
    {
        if (empty($this->data) || !is_array($this->data)) {
            return true;
        }

        return CommonLogService::dealData($this->data);
    }

    /**
     * 延迟执行
     *
     * @param $queue
     */
    public function delay($queue)
    {
        if ($this->retryTimes >= 2) {
            Yii::$app->notice->happy('日志保存失败', '', $this->data);
            return true;
        }
        parent::delay($queue);
    }

    /**
     * 添加任务
     *
     * @param array $data
     * @return void
     */
    public static function addJob(array $data)
    {
        Yii::$app->que->push(new self(['data' => $data]));
    }
}
