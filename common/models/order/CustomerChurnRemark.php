<?php

namespace common\models\order;

use common\enums\CustomerChurnRemarkReachStatusEnum;
use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\models\backend\order\OrderHeader;
use common\queues\CustomerChurnRemarkJob;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%customer_churn_remark}}".
 *
 * @property int $id 客户ID
 * @property int $order_id 订单ID,order_header表ID
 * @property int $plan_teacher_id 预约老师Id,erp_backend_member表ID
 * @property int $plan_time 订单预约时间
 * @property string $servicer_remark 客服备注
 * @property string $store_remark 门店备注
 * @property string $store_real_remark 门店真实备注
 * @property string $edit_remark 修改到店备注原因
 * @property int $reason_status 原因状态 表erp_customer_churn_reason.Id
 * @property int $reach_status 到店状态：1 已做 2 到店未做
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $created_at 创建时间
 * @property int $updated_by 修改人
 * @property int $updated_at 修改时间
 */
class CustomerChurnRemark extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%customer_churn_remark}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id', 'plan_teacher_id'], 'required'],
            [['order_id', 'plan_teacher_id', 'plan_time'], 'integer', 'min' => 0],
            [['servicer_remark'], 'string', 'max' => 500],
            [['store_remark', 'store_real_remark'], 'string', 'max' => 500],
            [['edit_remark'], 'string', 'max' => 500],
            [['order_id', 'plan_teacher_id', 'plan_time', 'reason_status', 'reach_status'], 'integer'],
            [['servicer_remark', 'store_remark', 'store_real_remark', 'edit_remark'], 'trim'],
            ['reach_status', 'in', 'range' => CustomerChurnRemarkReachStatusEnum::getKeys()],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '客户ID',
            'order_id' => '订单ID',
            'plan_teacher_id' => '责任老师',
            'plan_time' => '订单预约时间',
            'servicer_remark' => '客服备注',
            'store_remark' => '门店备注',
            'store_real_remark' => '门店备注',
            'edit_remark' => '修改到店备注原因',
            'reason_status' => '原因状态',
            'reach_status' => '到店状态',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'created_at' => '创建时间',
            'updated_by' => '修改人',
            'updated_at' => '修改时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['order_id', 'plan_teacher_id', 'servicer_remark', 'store_remark', 'store_real_remark', 'edit_remark', 'reason_status', 'reach_status'],
            'cancle' => ['reach_status', 'edit_remark'],
            'store' => ['store_real_remark', 'plan_teacher_id', 'reach_status'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->where(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_by = UserService::getInst()->id;
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }
        $this->updated_by = UserService::getInst()->id;
        $this->updated_at = time();
        return parent::beforeSave($isInsert);
    }

    public static function isRealLoss($orderId) {
        return static::find()->select('id')->where(['order_id' => $orderId, 'reach_status' => CustomerChurnRemarkReachStatusEnum::REACH_STORE_NOT_DONE])->scalar();
    }

    public function afterSave($insert, $changedAttributes)
    {
        CustomerChurnRemarkJob::addJob(['plan_time' => $this->plan_time, 'order_id' => $this->order_id, 'entity_id' => $this->entity_id]);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }

    public function getPlanTimeText()
    {
        return DateHelper::toDate($this->plan_time, 'Y-m-d H:i');
    }

    public function getOrderHeader()
    {
        return $this->hasOne(OrderHeader::class, ['id' => 'order_id']);
    }

    public function getReachStatusText()
    {
        return CustomerChurnRemarkReachStatusEnum::getValue($this->reach_status);
    }

    public function getReasonStatus()
    {
        return $this->hasOne(CustomerChurnReason::class, ['id' => 'reason_status']);
    }

    public function getReasonStatusText()
    {
        return $this->reasonStatus->reason ?: '';
    }

    public function getPlanTeacher()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'plan_teacher_id']);
    }

    public function getPlanTeacherNameText()
    {
        return $this->planTeacher->username ?: '';
    }
}
