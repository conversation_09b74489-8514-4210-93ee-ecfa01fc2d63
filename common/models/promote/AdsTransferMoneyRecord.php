<?php

namespace common\models\promote;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%ads_transfer_money_record}}".
 *
 * @property int $id ID
 * @property string $serial_number 充值序号
 * @property int $user_id 用户ID，backend_member表ID
 * @property string $user_name 用户姓名
 * @property string $transfer_advertiser_id 备用金账户ID，ads_main_body表sub_advertiser_id
 * @property string $target_advertiser_id 广告子账户ID，ads_account_sub表sub_advertiser_id
 * @property string $platform 充值平台
 * @property double $amount 充值金额
 * @property int $status 充值状态
 * @property int $entity_id 企业ID
 * @property int $created_at 充值时间
 */
class AdsTransferMoneyRecord extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%ads_transfer_money_record}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['serial_number'], 'string', 'max' => 50],
            [['user_name'], 'string', 'max' => 50],
            [['transfer_advertiser_id'], 'string', 'max' => 255],
            [['target_advertiser_id'], 'string', 'max' => 255],
            [['platform'], 'string', 'max' => 255],
            [['serial_number', 'user_id', 'user_name', 'transfer_advertiser_id', 'target_advertiser_id', 'platform', 'amount', 'status'], 'required'],
            [['user_id', 'status'], 'integer'],
            [['amount'], 'double'],
            [['serial_number', 'user_name', 'transfer_advertiser_id', 'target_advertiser_id', 'platform'], 'trim'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'serial_number' => '充值序号',
            'user_id' => '用户ID，backend_member表ID',
            'user_name' => '用户姓名',
            'transfer_advertiser_id' => '备用金账户ID，ads_main_body表sub_advertiser_id',
            'target_advertiser_id' => '广告子账户ID，ads_account_sub表sub_advertiser_id',
            'platform' => '充值平台',
            'amount' => '充值金额',
            'status' => '充值状态',
            'entity_id' => '企业ID',
            'created_at' => '充值时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['serial_number', 'user_id', 'user_name', 'transfer_advertiser_id', 'target_advertiser_id', 'platform', 'amount', 'status'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    /**
     * 获取指定账户在指定时间内的充值总额
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param int $hours 查询小时数（默认1小时）
     * @return float 充值总额
     */
    public static function getHourlyTransferAmount($targetAdvertiserId, $hours = 1)
    {
        $timeLimit = time() - ($hours * 3600);
        
        $amount = self::find()
            ->where(['target_advertiser_id' => $targetAdvertiserId])
            ->andWhere(['status' => 1]) // 只统计成功的充值
            ->andWhere(['>=', 'created_at', $timeLimit])
            ->sum('amount');
            
        return (float)($amount ?: 0);
    }

    /**
     * 获取指定账户的充值历史记录
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param int $hours 查询小时数（默认1小时）
     * @param int $limit 记录数量限制
     * @return array 充值历史记录
     */
    public static function getTransferHistory($targetAdvertiserId, $hours = 1, $limit = 100)
    {
        $timeLimit = time() - ($hours * 3600);
        
        return self::find()
            ->select(['user_name', 'amount', 'created_at', 'platform', 'serial_number'])
            ->where(['target_advertiser_id' => $targetAdvertiserId])
            ->andWhere(['status' => 1])
            ->andWhere(['>=', 'created_at', $timeLimit])
            ->orderBy(['created_at' => SORT_DESC])
            ->limit($limit)
            ->asArray()
            ->all();
    }

    /**
     * 检查指定账户的小时充值限额
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param float $newAmount 新充值金额
     * @param int $hourlyLimit 小时限额
     * @return bool 是否通过限额检查
     * @throws \Exception 超过限额时抛出异常
     */
    public static function checkHourlyLimit($targetAdvertiserId, $newAmount, $hourlyLimit)
    {
        $currentAmount = self::getHourlyTransferAmount($targetAdvertiserId, 1);
        
        if (($currentAmount + $newAmount) > $hourlyLimit) {
            throw new \Exception(sprintf(
                '1小时内限制充值金额不能超过%d，账户%s已经充值了%s',
                $hourlyLimit,
                $targetAdvertiserId,
                $currentAmount
            ));
        }
        
        return true;
    }

    /**
     * 获取加粉充值统计（近期充值次数）
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param int $minutes 统计分钟数（默认5分钟）
     * @return int 充值次数
     */
    public static function getAddFansTransferCount($subAdvertiserId, $minutes = 5)
    {
        $timeLimit = time() - ($minutes * 60);
        
        return self::find()
            ->where(['target_advertiser_id' => $subAdvertiserId])
            ->andWhere(['status' => 1])
            ->andWhere(['>=', 'created_at', $timeLimit])
            ->andWhere(['user_name' => '系统自动充值']) // 加粉充值的标识
            ->count();
    }

    /**
     * 获取账户充值统计信息
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param string $startDate 开始日期 (Y-m-d)
     * @param string $endDate 结束日期 (Y-m-d)
     * @return array 统计信息
     */
    public static function getTransferStats($targetAdvertiserId, $startDate = null, $endDate = null)
    {
        $query = self::find()
            ->where(['target_advertiser_id' => $targetAdvertiserId])
            ->andWhere(['status' => 1]);
            
        if ($startDate) {
            $query->andWhere(['>=', 'created_at', strtotime($startDate)]);
        }
        
        if ($endDate) {
            $query->andWhere(['<=', 'created_at', strtotime($endDate . ' 23:59:59')]);
        }
        
        $records = $query->asArray()->all();
        
        $stats = [
            'total_count' => count($records),
            'total_amount' => array_sum(array_column($records, 'amount')),
            'avg_amount' => 0,
            'platforms' => [],
            'users' => []
        ];
        
        if ($stats['total_count'] > 0) {
            $stats['avg_amount'] = $stats['total_amount'] / $stats['total_count'];
            
            // 按平台统计
            foreach ($records as $record) {
                $platform = $record['platform'];
                if (!isset($stats['platforms'][$platform])) {
                    $stats['platforms'][$platform] = ['count' => 0, 'amount' => 0];
                }
                $stats['platforms'][$platform]['count']++;
                $stats['platforms'][$platform]['amount'] += $record['amount'];
            }
            
            // 按用户统计
            foreach ($records as $record) {
                $user = $record['user_name'];
                if (!isset($stats['users'][$user])) {
                    $stats['users'][$user] = ['count' => 0, 'amount' => 0];
                }
                $stats['users'][$user]['count']++;
                $stats['users'][$user]['amount'] += $record['amount'];
            }
        }
        
        return $stats;
    }
}
