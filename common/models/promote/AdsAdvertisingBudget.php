<?php

namespace common\models\promote;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%ads_advertising_budget}}".
 *
 * @property int $id ID
 * @property int $promote_user_id 推广人员ID，backend_member表ID
 * @property double $budget 每日预算
 * @property string $platform 广告平台
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $created_at 创建时间
 * @property int $updated_by 更新人
 * @property int $updated_at 更新时间
 */
class AdsAdvertisingBudget extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%ads_advertising_budget}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['promote_user_id'], 'integer', 'min' => 0],
            [['budget'], 'double', 'min' => 0, 'max' => 99999],
            [['platform'], 'string', 'max' => 255],
            [['promote_user_id', 'budget', 'platform'], 'required'],
            [['platform'], 'trim'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'promote_user_id' => '推广人员ID，backend_member表ID',
            'budget' => '每日预算',
            'platform' => '广告平台',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'created_at' => '创建时间',
            'updated_by' => '更新人',
            'updated_at' => '更新时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['promote_user_id', 'budget', 'platform'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_by = UserService::getInst()->id;
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }
        $this->updated_by = UserService::getInst()->id;
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }
}
