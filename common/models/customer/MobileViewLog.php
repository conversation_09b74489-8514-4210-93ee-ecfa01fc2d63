<?php

namespace common\models\customer;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use services\UserService;
use common\models\backend\Member;
use Yii;

/**
 * This is the model class for table "{{%customer_mobile_view_log}}".
 *
 * @property int $id ID
 * @property string $mobile 手机号
 * @property int $order_id 订单ID
 * @property int $customer_id 客户ID
 * @property int $store_id 门店ID
 * @property int $created_by 查看人ID
 * @property int $created_at 创建时间
 */
class MobileViewLog extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%customer_mobile_view_log}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['mobile'], 'string', 'max' => 20],
            [['order_id'], 'integer', 'min' => 0],
            [['customer_id'], 'integer', 'min' => 0],
            [['store_id'], 'integer', 'min' => 0],
            [['mobile', 'order_id', 'customer_id', 'store_id'], 'required'],
            [['order_id', 'customer_id', 'store_id'], 'integer'],
            [['mobile'], 'trim'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'mobile' => '手机号',
            'order_id' => '订单ID',
            'customer_id' => '客户ID',
            'store_id' => '门店ID',
            'created_by' => '查看人ID',
            'created_at' => '创建时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['mobile', 'order_id', 'customer_id', 'store_id'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
            $this->created_by = UserService::getInst()->id;
        }

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }
}
