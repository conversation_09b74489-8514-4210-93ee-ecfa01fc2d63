<?php

namespace common\models\customer;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\enums\CustomerAgeBracket;
use common\enums\AdPositionEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%customer_feedback}}".
 *
 * @property int $id ID
 * @property int $order_id 订单ID，order_header表ID
 * @property int $cus_id 客户ID，customer表ID
 * @property int $plan_time 到店时间，order_header表plan_time
 * @property int $store_id 门店ID，store表ID
 * @property int $age_bracket 年龄段
 * @property string $feedback 反馈内容
 * @property string $images 图片
 * @property int $entity_id 企业ID
 * @property int $created_at 创建时间
 * @property int $created_by 创建人
 * 
 * 虚拟属性 - 查询结果中的关联数据（必须声明为public以支持toArray序列化）
 * @property string $customer_name 客户姓名
 * @property string $nick_name 昵称
 * @property string $avatar 头像
 * @property string $mobile 手机号
 * @property int $material_id 素材ID
 * @property string $store_name 门店名称
 * @property string $order_no 订单号
 * @property int $order_status 订单状态
 * @property int $order_plan_time 订单预约时间
 * @property int $promoter_user_id 推广员ID
 * @property string $mid3 mid3
 * @property int $add_time 加粉时间
 * @property string $csite 站点
 * @property string $sub_advertiser_name 子广告主名称
 * @property int $sub_advertiser_id 子广告主ID
 * @property string $adid 广告ID
 * @property string $promoter_username 推广员用户名
 * @property string $video_img 视频图片
 *
 * 虚拟属性 - 计算得出的数据
 * @property array $customer_info 客户信息
 * @property string $plan_time_text 到店时间文本
 * @property string $age_bracket_text 年龄段文本
 * @property string $status_text 状态文本
 * @property string $feedback_short 反馈内容简短版
 * @property array $images_parsed 解析后的图片
 * @property string $promoter_name 推广员姓名
 * @property array $material_info 素材信息
 * @property array $account_info 账户信息
 * @property string $created_at_text 创建时间文本
 * 
 * 关联模型
 * @property \common\models\backend\Member $createdPerson 创建人
 */
class Feedback extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * 虚拟属性，用于存储查询结果中的关联数据
     */
    public $customer_name;
    public $nick_name;
    public $avatar;
    public $mobile;
    public $material_id;
    public $store_name;
    public $customer_status_text;
    public $order_no;
    public $order_status;
    public $order_plan_time;
    public $promoter_user_id;
    public $mid3;
    public $add_time;
    public $csite;
    public $sub_advertiser_name;
    public $sub_advertiser_id;
    public $adid;
    public $promoter_username;
    public $video_img;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%customer_feedback}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id'], 'integer', 'min' => 0],
            [['cus_id'], 'integer', 'min' => 0],
            [['plan_time'], 'integer', 'min' => 0],
            [['store_id'], 'integer', 'min' => 0],
            [['age_bracket'], 'integer', 'min' => 0],
            [['feedback'], 'string', 'max' => 500],
            [['order_id', 'cus_id', 'plan_time', 'store_id', 'age_bracket', 'feedback', 'images'], 'required'],
            [['order_id', 'cus_id', 'plan_time', 'store_id', 'age_bracket'], 'integer'],
            [['feedback', 'images'], 'trim'],
            [['images'], 'string'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'order_id' => '订单ID，order_header表ID',
            'cus_id' => '客户ID，customer表ID',
            'plan_time' => '到店时间，order_header表plan_time',
            'store_id' => '门店ID，store表ID',
            'age_bracket' => '年龄段',
            'feedback' => '反馈内容',
            'images' => '图片',
            'entity_id' => '企业ID',
            'created_at' => '创建时间',
            'created_by' => '创建人',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['order_id', 'cus_id', 'plan_time', 'store_id', 'age_bracket', 'feedback', 'images'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            // $this->created_by = UserService::getInst()->id;
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getAddTimeText()
    {
        return DateHelper::toDate($this->add_time, 'Y-m-d H:i:s');
    }

    /**
     * 获取客户信息
     * @return array
     */
    public function getCustomerInfo()
    {
        return [
            'name' => $this->customer_name ?: '',
            'nick_name' => $this->nick_name ?: '',
            'avatar' => $this->avatar ?: '',
            'mobile' => $this->mobile ?: '',
            'type' => 'customer',
        ];
    }

    /**
     * 获取到店时间文本（订单预约时间）
     * @return string
     */
    public function getPlanTimeText()
    {
        return $this->order_plan_time ? DateHelper::toDate($this->order_plan_time, 'Y-m-d H:i:s') : '';
    }

    /**
     * 获取年龄段文本
     * @return string
     */
    public function getAgeBracketText()
    {
        return CustomerAgeBracket::getValue($this->age_bracket) ?: '其他';
    }

    /**
     * 获取状态文本
     * @return string
     */
    public function getStatusText()
    {
        return $this->customer_status_text;
    }

    /**
     * 获取反馈内容简短版
     * @return string
     */
    public function getFeedbackShort()
    {
        return mb_strlen($this->feedback) > 50 ? 
            mb_substr($this->feedback, 0, 50) . '...' : $this->feedback;
    }

    /**
     * 获取解析后的图片
     * @return array
     */
    public function getImagesParsed()
    {
        if (empty($this->images)) {
            return [];
        }
        
        try {
            $images = json_decode($this->images, true);
            if (!is_array($images)) {
                return [];
            }
            
            $result = [];
            foreach ($images as $image) {
                if (is_string($image)) {
                    $result[] = [
                        'url' => $image,
                        'name' => basename($image), // 从 URL 中提取文件名
                        'size' => 0,
                    ];
                }
            }
            
            return $result;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取推广员姓名
     * @return string
     */
    public function getPromoterName()
    {
        return $this->promoter_username ?: '';
    }

    /**
     * 获取素材信息
     * @return array
     */
    public function getMaterialInfo()
    {
        return [
            'mid3' => $this->mid3 ?: '',
            'csite_text' => AdPositionEnum::getPositionByCsite($this->csite ?: 0),
            'video_img' => $this->video_img ?: '',
        ];
    }

    /**
     * 获取账户信息
     * @return array
     */
    public function getAccountInfo()
    {
        return [
            'sub_advertiser_name' => $this->sub_advertiser_name ?: '',
            'sub_advertiser_id' => $this->sub_advertiser_id ?: '',
            'adid' => $this->adid ?: '',
        ];
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }
}
