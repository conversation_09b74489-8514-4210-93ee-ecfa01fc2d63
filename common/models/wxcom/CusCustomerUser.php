<?php

namespace common\models\wxcom;

use backendapi\services\wxcom\ComService;
use backendapi\services\wxcom\CusTagService;
use common\components\PromoteReport;
use common\components\report\ReportFactory;
use common\components\wxcom\CusTagSDK;
use common\components\wxcom\CustomerSDK;
use common\enums\AdPositionEnum;
use common\enums\CusCustomerUserTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\reportEnum;
use common\enums\StatusEnum;
use common\enums\WxcomAddTypeEnum;
use common\helpers\DateHelper;
use common\models\backend\Member;
use common\models\backend\order\OrderHeader;
use common\models\backend\promote\PromoteLink;
use common\models\backend\promote\PromoteProject;
use common\models\backendapi\PromoteChannel;
use common\models\common\ApiLog;
use common\models\common\LandPageReport;
use common\models\common\Provinces;
use common\models\promote\Direction;
use common\models\wechat\WechatAllot;
use common\models\wechat\WechatAllotDetail;
use common\queues\CusCustomerUserJob;
use common\queues\FansCountJob;
use common\queues\QrcodeCusCountJob;
use common\queues\RemoteTagDataJob;
use common\queues\TransferMoneyJob;
use common\models\common\AdsMaterial;
use Exception;
use Yii;

/**
 * This is the model class for table "{{%wxcom_cus_customer_user}}".
 *
 * @property int $id 客户成员ID
 * @property int $com_id 企微公司ID：wxcom_com表ID
 * @property int $cus_id 客户id
 * @property string $sub_advertiser_name 广告子账户名称
 * @property string $sub_advertiser_id 广告子账户id
 * @property string $callback 回调信息
 * @property string $cl_code 落地页上报参数
 * @property string $system 系统标识：lzn莲姿娜、kemei珂美、meizi盯颜、amd倾城工坊、td永恩
 * @property string $code 代号
 * @property int $channel_id 渠道ID
 * @property int $link_id 链路ID
 * @property int $project_id 项目ID
 * @property int $direction_id 定向ID
 * @property int $responsible_id 推广责任人ID
 * @property int $qrcode_created_by 活码创建人
 * @property int $qrcode_dept_id 活码创建部门ID
 * @property string $wxcom_user_id 客服企微id
 * @property int $user_id 客服id
 * @property string $remark 客服备注
 * @property string $remark_mobiles 客服备注手机号
 * @property string $description 客服描述
 * @property string $tag_ids 标签ids
 * @property int $add_way 添加方式
 * @property int $add_time 添加时间
 * @property string $oper_user_id 添加人的id
 * @property string $state state
 * @property int $dynamic_qrcode_id 动态码ID
 * @property int $button_id 企微直加ID：wxcom_cus_button表ID
 * @property int $type 活码类型：1静态码、2动态码、3企微直加
 * @property string $adid 计划id
 * @property string $mid3 视频素材id
 * @property string $csite 广告投放位置
 * @property int $add_type 添加类型：0客户主动，1员工主动
 * @property int $is_deleted 是否删除
 * @property int $deleted_type 删除类型：0客户主动，1员工主动
 * @property int $deleted_time 删除时间
 * @property int $entity_id 企业ID
 * @property int $is_extend 1是否被继承 2为客户拒绝
 * @property string $ip ip地址
 * @property int $province_id 省份ID：common_provinces表ID
 * @property int $city_id 城市ID：common_provinces表ID
 * @property int $district_id 区县ID：common_provinces表ID
 * @property int $first_opening_time 客户首次开口时间
 * @property int $is_order_report 是否下订上报
 * @property int $is_heavy_powder_do_report 是否重粉已做上报
 * @property int $is_heavy_powder_order_report 是否重粉已订上报
 * @property int $created_at 创建时间
 * @property int $created_by 创建人ID：backend_member表ID
 */
class CusCustomerUser extends \yii\db\ActiveRecord
{
    public $cus_count;

    const ADD_TYPE_CUSTOMER = 0;
    const ADD_TYPE_USER = 1;

    const DELETED_TYPE_CUSTOMER = 0;
    const DELETED_TYPE_USER = 1;
    const DELETED_TYPE_ADMIN = 2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%wxcom_cus_customer_user}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sub_advertiser_name', 'sub_advertiser_id', 'callback', 'wxcom_user_id', 'remark', 'remark_mobiles', 'description', 'tag_ids', 'oper_user_id', 'state', 'adid'], 'trim'],
            [['cus_id', 'channel_id', 'link_id', 'project_id', 'direction_id', 'responsible_id', 'qrcode_created_by', 'qrcode_dept_id', 'user_id', 'add_way', 'add_time', 'dynamic_qrcode_id', 'type', 'add_type', 'is_deleted', 'deleted_type', 'deleted_time', 'entity_id', 'first_opening_time', 'is_order_report', 'is_heavy_powder_do_report', 'is_heavy_powder_order_report'], 'integer'],
            [['sub_advertiser_name', 'sub_advertiser_id'], 'string', 'max' => 255],
            [['callback'], 'string', 'max' => 500],
            [['cl_code', 'system'], 'string', 'max' => 10],
            [['code'], 'string', 'max' => 20],
            [['wxcom_user_id', 'remark', 'remark_mobiles', 'oper_user_id', 'state'], 'string', 'max' => 60],
            [['description'], 'string', 'max' => 160],
            [['tag_ids', 'adid', 'mid3', 'csite'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '客户成员ID',
            'com_id' => '企微公司ID',
            'cus_id' => '客户id',
            'sub_advertiser_name' => '广告子账户名称',
            'sub_advertiser_id' => '广告子账户id',
            'callback' => '回调信息',
            'cl_code' => '落地页上报参数',
            'system' => '系统标识',
            'code' => '代号',
            'channel_id' => '渠道ID',
            'link_id' => '链路ID',
            'project_id' => '项目ID',
            'direction_id' => '定向ID',
            'responsible_id' => '推广责任人ID',
            'qrcode_created_by' => '活码创建人',
            'qrcode_dept_id' => '活码创建部门ID',
            'wxcom_user_id' => '客服企微id',
            'user_id' => '客服id',
            'remark' => '客服备注',
            'remark_mobiles' => '客服备注手机号',
            'description' => '客服描述',
            'tag_ids' => '标签ids',
            'add_way' => '添加方式',
            'add_time' => '添加时间',
            'oper_user_id' => '添加人的id',
            'state' => 'state',
            'dynamic_qrcode_id' => '动态码ID',
            'button_id' => '企微直加ID',
            'type' => '活码类型',
            'adid' => '计划id',
            'mid3' => '视频素材id',
            'csite' => '广告投放位置',
            'add_type' => '添加类型',
            'is_deleted' => '是否删除',
            'deleted_type' => '删除类型',
            'deleted_time' => '删除时间',
            'entity_id' => '企业ID',
            'is_extend' => '是否被继承',
            'ip' => 'ip地址',
            'province_id' => '省份ID',
            'city_id' => '城市ID',
            'district_id' => '区县ID',
            'first_opening_time' => '客户首次开口时间',
            'is_order_report' => '是否下订上报',
            'is_heavy_powder_do_report' => '是否重粉已做上报',
            'is_heavy_powder_order_report' => '是否重粉已订上报',
            'created_at' => '创建时间',
            'created_by' => '创建人ID',
        ];
    }

    public function getCustomer()
    {
        return $this->hasOne(CusCustomer::class, ['id' => 'cus_id']);
    }

    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * 获取关联的素材信息
     * @return \yii\db\ActiveQuery
     */
    public function getAdsMaterial()
    {
        return $this->hasOne(AdsMaterial::class, ['material_id' => 'mid3']);
    }

    /**
     * 获取素材图片
     * @return string
     */
    public function getVideoImg()
    {
        return $this->adsMaterial ? $this->adsMaterial->video_img : '';
    }

    public function getUserName()
    {
        return $this->user->name ?: '';
    }

    public function getAddTimeText()
    {
        return DateHelper::toDate($this->add_time);
    }

    /**
     * 关联渠道
     *
     * @return \yii\db\ActiveQuery
     */
    public function getChannel()
    {
        return $this->hasOne(PromoteChannel::class, ['id' => 'channel_id']);
    }

    public function getLandPageReport()
    {
        return $this->hasOne(LandPageReport::class, ['params' => 'cl_code']);
    }

    public function getLandPageReportName()
    {
        return $this->landPageReport->name ?: '';
    }

    public function getDirection()
    {
        return $this->hasOne(Direction::class, ['id' => 'direction_id']);
    }

    /**
     * 获取渠道名称
     *
     * @return string
     */
    public function getChannelName()
    {
        return $this->channel->name ?: '';
    }

    public function getLink()
    {
        return $this->hasOne(PromoteLink::class, ['id' => 'link_id']);
    }

    public function getLinkName()
    {
        return $this->link->name ?: '';
    }

    public function getProject()
    {
        return $this->hasOne(PromoteProject::class, ['id' => 'project_id']);
    }

    public function getProjectName()
    {
        return $this->project->name ?: '';
    }

    public function getResponsible()
    {
        return $this->hasOne(Member::class, ['id' => 'responsible_id']);
    }

    public function getResponsibleName()
    {
        return $this->responsible->username ?: '';
    }

    public function getComName()
    {
        return $this->customer->com->name ?: '';
    }

    public function getQrcode()
    {
        if ($this->type == CusCustomerUserTypeEnum::DYNAMIC_CODE) {
            if (!$this->dynamic_qrcode_id) {
                $this->dynamic_qrcode_id = DynamicMapping::find()
                    ->select('dynamic_qrcode_id')
                    ->andWhere([
                        'qrcode_id' => $this->state,
                        'wxcom_user_id' => $this->wxcom_user_id,
                        'external_user_id' => $this->oper_user_id,
                    ])->scalar();
            }

            return $this->hasOne(CusDynamicQrcode::class, ['id' => 'dynamic_qrcode_id']);
        } else if ($this->type == CusCustomerUserTypeEnum::AD_BUTTON) {
            return $this->hasOne(CusButton::class, ['id' => 'button_id']);
        } else {
            return $this->hasOne(CusQrcode::class, ['id' => 'state']);
        }
    }

    public function beforeSave($isInsert)
    {
        $jobConfig = $this->jobConfig();
        $fansJobConfig = $this->fansJobConfig($jobConfig);
        if ($isInsert) {
            $this->created_at = time();
            if (in_array($this->add_way, CusQrcodeCount::getPromoteAddWay())) {
                QrcodeCusCountJob::addJobForAdd($jobConfig);
            }
            if (($this->state && in_array($this->add_way, CusQrcodeCount::getPromoteAddWay())) || $this->add_way == WxcomAddTypeEnum::WECHAT_TO_WXCOM) {
                FansCountJob::addJobForAdd($fansJobConfig);
            }
            if ($this->add_way == WxcomAddTypeEnum::BUTTON) {
                FansCountJob::addJobForAdd2($fansJobConfig);
            }
        }

        return parent::beforeSave($isInsert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        $this->tagEventJob($insert, $changedAttributes);

        //加粉后账户自动充值
        if (isset($changedAttributes['sub_advertiser_id']) && empty($changedAttributes['sub_advertiser_id']) && $this->sub_advertiser_id) {
            try {
                //判断add_time是当天且在凌晨0-8点之间
                if (DateHelper::toDate($this->add_time, 'Y-m-d') >= date('Y-m-d') 
                // && DateHelper::toDate($this->add_time, 'H') < 8
            ) {
                    TransferMoneyJob::addFansJob($this->sub_advertiser_id);
                }
            } catch (Exception $e) {
                Yii::error('CusCustomerUserID：' . $this->id . ',加粉后账户自动充值失败:' . $e->getMessage());
            }
        }

        if (isset($changedAttributes['ip']) && empty($changedAttributes['ip']) && $this->ip) {
            CusCustomerUserJob::addJobDealIp($this->id);
        }

        if ($insert) {
            CusCustomerUserJob::addJob($this->id);
        }
        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }

    public function tagEventJob($insert, $changedAttributes)
    {
        if (!in_array($this->add_way, self::getComputeAddWay())) {
            return true;
        }

        $is_job = false;
        if ($insert) {
            if (CusTagService::isRemoteTag($this->tag_ids, $this->entity_id)) {
                $is_job = true;
            }
        } else {
            if (
                isset($changedAttributes['tag_ids']) && (
                    CusTagService::isRemoteTag($changedAttributes['tag_ids'], $this->entity_id)
                    || CusTagService::isRemoteTag($this->tag_ids, $this->entity_id))
            ) {
                $is_job = true;
            }
        }

        if ($is_job) {
            RemoteTagDataJob::addJob(['add_time' => $this->add_time, 'entity_id' => $this->entity_id]);
        }
    }

    public function jobConfig()
    {
        $jobConfig = [
            'qrcode_id' => $this->state,
            'user_id' => $this->user_id,
            'entity_id' => $this->entity_id,
            'date_time' => strtotime(date('Y-m-d', $this->add_time)),
            'type' => $this->type,
        ];

        if ($this->type == CusCustomerUserTypeEnum::DYNAMIC_CODE) { //动态活码匹配
            if (!$this->dynamic_qrcode_id) {
                $this->dynamic_qrcode_id = $this->qrcode->id;
            }

            $jobConfig['qrcode_id'] = $this->dynamic_qrcode_id;
        } else if ($this->type == CusCustomerUserTypeEnum::AD_BUTTON) {
            $jobConfig['qrcode_id'] = $this->button_id;
        }

        return $jobConfig;
    }

    public function fansJobConfig($jobConfig)
    {
        $fansJobConfig = [
            'wxcom_user_id' => $this->user_id,
            'date_time' => $jobConfig['date_time'],
        ];

        return $fansJobConfig;
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getTagNameArray()
    {
        $tagNames = [];
        if ($this->tag_ids) {
            $tagIds = explode(',', $this->tag_ids);
            $tags = CusTag::find()->select('name')->where(['in', 'id', $tagIds])->all();
            $tagNames = array_column($tags, 'name');
        }
        return $tagNames;
    }

    public function getQrcodeCreatedPerson()
    {
        return $this->hasOne(Member::class, ['id' => 'qrcode_created_by']);
    }

    public function getQrcodeCreatedPersonName()
    {
        return $this->qrcodeCreatedPerson->username ?: '';
    }

    /**
     * 获取计算推广加粉添加的方式
     *
     * @return array
     */
    public static function getComputeAddWay()
    {
        /**@var WxcomAddTypeEnum 中的值*/
        return [0, 1, 16, 24, 300];
    }

    public function updateRemarkToWxcom()
    {
        $customer = $this->customer;
        $qrcode = $this->qrcode;
        if ($qrcode && $qrcode->cus_remark) {
            $remark = $customer->name . '-' . $qrcode->cus_remark;
            $this->remark = $remark;
            $this->save();
            $config = ComService::getWxcomConfigByIdForCus($customer->com_id);
            $cusSDK = new CustomerSDK($config['corp_id'], $config['secret']);
            $ret = $cusSDK->updateRemark($this->wxcom_user_id, $customer->external_user_id, $remark);
            if ($ret['errcode'] != 0) {
                Yii::$app->notice->important('同步备注到企微报错', null, $ret);
            }
        }
    }

    public function updateTagsToWxcom()
    {
        $customer = $this->customer;
        $qrcode = $this->qrcode;
        if ($qrcode && $qrcode->tag_ids) {
            $this->tag_ids = $qrcode->tag_ids;
            $this->save();
            $tagIds = explode(',', $qrcode->tag_ids);
            $tags = CusTagService::find()->select('wxcom_id')->where(['in', 'id', $tagIds])->all();
            $wxcomTagIds = array_column($tags, 'wxcom_id');
            $config = ComService::getWxcomConfigByIdForCus($customer->com_id);
            $cusTagSDK = new CusTagSDK($config['corp_id'], $config['secret']);
            $ret = $cusTagSDK->updateCustomerTags($this->wxcom_user_id, $customer->external_user_id, $wxcomTagIds);
            if ($ret['errcode'] != 0) {
                Yii::$app->notice->important('同步标签到企微报错', null, $ret);
            }
        }
    }

    /**
     * 修改记录为已继承
     *
     * @return void
     */
    public function updateToExtend()
    {
        $this->is_extend = 1;
        if (!$this->save()) {
            throw new Exception('企微客户关系表继承状态改为已继承失败');
        }
    }
    /**
     * 修改记录为继承失败(客户拒绝)
     * @throws Exception
     */
    public function updateToExtendFail()
    {
        $this->is_extend = 2;
        if (!$this->save()) {
            throw new Exception('企微客户关系表继承状态改为已继承失败');
        }
    }

    /**
     * 填充地址信息
     * 通过 ua
     *
     * @param string $ua
     * @return boolean
     */
    public function fillLocationFromUA($ua = null)
    {
        if (empty($ua)) {
            return false;
        }

        list($ip) = explode('Mozilla', $ua);
        if (empty($ip)) {
            return false;
        }
        $this->ip = $ip;
        return true;
    }

    /**
     * 获取行政区id
     *
     * @param string $name
     * @param integer $parentId
     * @return integer
     */
    public function getDistrictId($name, $parentId = 0)
    {
        return Provinces::find()
            ->andWhere(['like', 'title', $name])
            ->andWhere(['pid' => $parentId])
            ->select('id')
            ->scalar();
    }

    public function getProvince()
    {
        return $this->hasOne(Provinces::class, ['id' => 'province_id']);
    }

    public function getCity()
    {
        return $this->hasOne(Provinces::class, ['id' => 'city_id']);
    }

    public function getDistrict()
    {
        return $this->hasOne(Provinces::class, ['id' => 'district_id']);
    }

    public function getLocation()
    {
        $location = [];

        if ($this->province) {
            $location[] = $this->province->title;
        }

        if ($this->city) {
            $location[] = $this->city->title;
        }

        if ($this->district) {
            $location[] = $this->district->title;
        }

        return implode('', $location);
    }

    public function getCsiteText()
    { 
        return AdPositionEnum::getPositionByCsite($this->csite);
    }

    /**
     * 删粉上报
     *
     * @return array|bool
     * @throws Exception
     */
    public function delReport()
    {
        // 验证加粉方式
        if (!in_array($this->add_way, static::getComputeAddWay())) {
            return true;
        }
        // 验证流失才上报
        if ($this->deleted_type != static::DELETED_TYPE_CUSTOMER) {
            return true;
        }
        // 验证删除时间
        if (!$this->is_deleted || $this->deleted_time - $this->add_time > 120) {
            return true;
        }
        // 验证渠道
        $channelIds = PromoteChannel::find()->where(['platform' => [reportEnum::PANGOLIN, reportEnum::TIKTOL]])->select('id')->column();
        if (!in_array($this->channel_id, $channelIds)) {
            return true;
        }
        // 验证回调参数
        if (!$this->callback) {
            return true;
        }
        // 上报秒删事件
        $res = PromoteReport::cusDelReport($this->callback);

        if ($res['code'] != 0) {
            $code = '0';
            $title = '删粉-上报失败';
        } else {
            $code = '200';
            $title = '删粉-上报成功';
        }

        $apiLog = new ApiLog();
        $apiLog->type = $title;
        $apiLog->code = $code;
        $apiLog->content = $this->id;
        $apiLog->callback_pack = json_encode($res, JSON_UNESCAPED_UNICODE);
        $apiLog->save();

        return true;
    }

    public function getAddWayText()
    {
        $text = WxcomAddTypeEnum::getValue($this->add_way);

        // 加粉方式非 QRCODE、BUTTON 直接返回
        if (!in_array($this->add_way, [WxcomAddTypeEnum::QRCODE, WxcomAddTypeEnum::BUTTON, WxcomAddTypeEnum::LINK])) {
            return $text;
        }

        switch ($this->type) {
            case CusCustomerUserTypeEnum::DYNAMIC_CODE:
                if ($this->state) {
                    $text = "通过\"{$this->qrcode->name}\"渠道动态码扫码添加";
                }
                break;
            case CusCustomerUserTypeEnum::STATIC_CODE:
                if ($this->state) {
                    $text = "通过\"{$this->qrcode->name}\"渠道静态码扫码添加";
                    if ($this->add_way == WxcomAddTypeEnum::LINK) {
                        $text = "通过\"{$this->qrcode->name}\"获客助手添加";
                    }
                }
                break;
            case CusCustomerUserTypeEnum::AD_BUTTON:
                if ($this->button_id) {
                    $text = "通过\"{$this->qrcode->name}\"企微直加添加";
                }
                break;
        }
        return $text;
    }

    /**
     * 客户打标签
     */
    public function cusAddTag()
    {
        // 验证加粉方式
        if (!in_array($this->add_way, static::getComputeAddWay())) {
            return true;
        }

        $arrTagName = [];
        if ($this->add_way == WxcomAddTypeEnum::LINK) {
            $arrTagName[] = '不回复';
        }

        $moreFanTag = $this->moreAddFanTag(); //客户重粉打标签
        $arrTagName = array_merge($arrTagName, $moreFanTag);

        $this->addTag($arrTagName);
        return true;
    }

    /**
     * 客户重粉标签
     */
    public function moreAddFanTag()
    {
        $tagName = [];
        if ($this->is_completed_order()) {
            $tagName[] = '重粉已做';
        } elseif ($this->is_exist_order()) {
            $tagName[] = '重粉已订';
        }

        $count_num = self::find()->where(['cus_id' => $this->cus_id])->andWhere(['<>', 'id', $this->id])->count();
        if ($count_num >= 3) {
            $tagName[] = '多次添加';
        }

        return $tagName;
    }

    public function heavyPowderReport()
    {
        try {
            $tagOne = CusTagService::getIdByName('重粉已做', $this->entity_id);
            $tagTwo = CusTagService::getIdByName('重粉已订', $this->entity_id);
            $tagIds = explode(',', $this->tag_ids);
            if (!in_array($tagOne, $tagIds) && !in_array($tagTwo, $tagIds)) {
                return false;
            }

           if (in_array($tagOne, $tagIds)) {
                $reportType = 'heavyPowderDoReport';
            }else{
                $reportType = 'heavyPowderOrderReport';
            }

            //上报
            $reportService = ReportFactory::getReportServiceByChannelId($this->channel_id);
            $reportService->reportType = $reportType;
            $reportService->cusUserCreatedReport($this);
        } catch (Exception $e) {
            return false;
        }
    }

    public function isPossibleFishing()
    {
        try {
            // 验证加粉方式
            if (!in_array($this->add_way, static::getComputeAddWay()) || $this->channel_id != 1) {
                return true;
            }

            if (
                ($this->mid3 == '' && $this->adid == '' && $this->state != '' && $this->type == CusCustomerUserTypeEnum::STATIC_CODE)
                || ($this->mid3 == '__MID3__' && $this->adid == '__PROMOTION_ID__')
            ) {
                $tagName = ['疑似钓鱼'];
                return $this->addTag($tagName);
            }

            return true;
        } catch (Exception $e) {
            $error = [
                'title' => '疑似钓鱼打标',
                'id' => $this->id,
                'error_msg' => $e->getMessage()
            ];
            Yii::error($error, 'isPossibleFishing');
            return false;
        }
    }

    /**
     * 添加用户标签
     */
    public function addTag(array $tagNames)
    {
        $tagInfo = CusTag::find()->cache(60)->select('id,wxcom_id')
            ->where(['name' => $tagNames])->andWhere(['entity_id' => $this->entity_id])
            ->all();
        if (empty($tagInfo)) {
            return true;
        }

        $addTagIds =  array_column($tagInfo, 'id');
        $addTagWxcomIds = array_column($tagInfo, 'wxcom_id');
        $cusTagIds = $this->tag_ids ? explode(',', $this->tag_ids) : [];
        $cusTagIds = array_merge($cusTagIds, $addTagIds);
        $this->tag_ids = implode(',', array_unique($cusTagIds));
        if (!$this->save(false)) {
            return false;
        }

        $customer = $this->customer;
        $config = ComService::getWxcomConfigByIdForCus($customer->com_id);
        $cusTagSDK = new CusTagSDK($config['corp_id'], $config['secret']);
        $ret = $cusTagSDK->updateCustomerTags($this->wxcom_user_id, $customer->external_user_id, $addTagWxcomIds);
        if ($ret['errcode'] != 0) {
            Yii::error('客户新增标签同步企微失败,表【erp_wxcom_cus_customer_user】Id:' . $this->id, '新增标签：' . json_encode($tagNames, 256));
            Yii::$app->feishuNotice->text('客户新增标签同步企微失败,表【erp_wxcom_cus_customer_user】Id:' . $this->id, '请到日志查看数据');
        }

        return true;
    }

    /**
     * 删除用户标签
     */
    public function delTag(array $tagNames)
    {
        $delFanTag = CusTag::find()->cache(60)->select('id,wxcom_id')->where(['name' => $tagNames])->andWhere(['entity_id' => $this->entity_id])->all();
        if (empty($delFanTag)) {
            return true;
        }

        $delFanTagIds =  array_column($delFanTag, 'id');
        $cusTagIds = $this->tag_ids ? explode(',', $this->tag_ids) : [];
        $this->tag_ids = implode(',', array_diff($cusTagIds, $delFanTagIds));
        if (!$this->save(false)) {
            return false;
        }

        $customer = $this->customer;
        $delFanTagWxcomIds = array_column($delFanTag, 'wxcom_id');

        $config = ComService::getWxcomConfigByIdForCus($customer->com_id);
        $cusTagSDK = new CusTagSDK($config['corp_id'], $config['secret']);
        $ret = $cusTagSDK->updateCustomerTags($this->wxcom_user_id, $customer->external_user_id, [], $delFanTagWxcomIds);
        if ($ret['errcode'] != 0) {
            Yii::error('客户删除标签同步企微信失败,表【erp_wxcom_cus_customer_user】Id:' . $this->id, '删除标签：' . json_encode($tagNames, 256));
            Yii::$app->feishuNotice->text('客户删除标签同步企微信失败,表【erp_wxcom_cus_customer_user】Id:' . $this->id, '请到日志查看数据');
            return false;
        }

        return true;
    }

    /**
     * 判断用户是否完成订单
     */
    public function is_completed_order()
    {
        $is_completed_order = OrderHeader::find()->alias('oh')
            ->leftJoin('{{%customer}} c', 'c.id = oh.cus_id')
            ->where(['c.wxcom_cus_id' => $this->cus_id])
            ->andWhere(['oh.order_status' => [OrderHeaderStatusEnum::STATUS_COMPLETED, OrderHeaderStatusEnum::STATUS_OTHER_SETTLEMENT]])
            ->count();

        return $is_completed_order ? true : false;
    }

    /**
     * 判断用户是否存在订单
     */
    public function is_exist_order()
    {
        $is_exist_order = OrderHeader::find()->alias('oh')
            ->leftJoin('{{%customer}} c', 'c.id = oh.cus_id')
            ->where(['c.wxcom_cus_id' => $this->cus_id])
            ->count();

        return $is_exist_order ? true : false;
    }

    /**
     * 删粉给用户打删粉标签
     */
    public function addDelFanTag()
    {
        // 验证加粉方式
        if (!in_array($this->add_way, static::getComputeAddWay())) {
            return true;
        }
        // 验证流失是否是客户删除
        if ($this->deleted_type != static::DELETED_TYPE_CUSTOMER) {
            return true;
        }
        // 验证删除时间
        if ($this->deleted_time - $this->add_time > 120) { //删除时间大于添加时间120秒 是删除标签
            $tagName = ['删粉'];
        } else { //删除时间小于添加时间120秒 是秒删标签 
            $tagName = ['秒删'];
        }

        return $this->addTag($tagName);
    }

    /**
     * 再次添加用户，去掉删粉/秒删标签
     */
    public function againAddDelTag()
    {
        return $this->delTag(['删粉', '秒删']);
    }

    /**
     * 未知来源方式，设置用户信息
     */
    public function unknownSourceWay()
    {
        if (!in_array($this->add_way, [WxcomAddTypeEnum::UNKNOWN, WxcomAddTypeEnum::WECHAT_TO_WXCOM])) {
            return true;
        }

        $wechat_allot = WechatAllot::find()->alias('wa')
            ->select('wa.*')
            ->leftJoin('{{%wechat_allot_detail}} wad', 'wa.id = wad.allot_id')
            ->leftJoin('{{%wxcom_user}} wu', 'wu.id = wad.account_id')
            ->where(['wu.wxcom_user_id' => $this->wxcom_user_id, 'wa.status' => StatusEnum::ENABLED])
            ->andWhere(['wa.entity_id' => $this->entity_id])
            ->andWhere(['wad.type' => WechatAllotDetail::TYPE_WXCOM])
            ->one();

        if (empty($wechat_allot)) {
            return true;
        }

        $this->responsible_id = $wechat_allot->responsible_id;
        $this->qrcode_created_by = $wechat_allot->responsible_id;
        $this->qrcode_dept_id = $wechat_allot->responsible->departmentAssignment->dept_id;
        $this->channel_id = $wechat_allot->channel_id;
        $this->link_id = $wechat_allot->link_id;
        $this->project_id = $wechat_allot->project_id;
        $this->direction_id = $wechat_allot->direction_id;
        $this->code = $wechat_allot->code;

        $this->save();
    }
}
