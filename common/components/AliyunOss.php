<?php

/**
 * Created by PhpStorm
 * User: ldz
 * Date:2020/9/10
 * Time:17:19
 */

namespace common\components;

use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\Url;
use Exception;
use linslin\yii2\curl\Curl;
use Xxtime\Flysystem\Aliyun\OssAdapter;
use Yii;
use yii\helpers\Json;

class AliyunOss
{
    public $accessKeyID;

    public $accessKeySecret;

    public $host;

    public $callbackUrl;

    public function __construct()
    {
        $config = Yii::$app->params['AliyunOss'];
        $this->host = $config['host'];
        $this->accessKeyID = $config['AccessKeyID'];
        $this->accessKeySecret = $config['AccessKeySecret'];
        $this->callbackUrl = 'https://erpapi.radilush.com' . Url::toRoute(['site/oss']);
    }

    /**
     * 获取签名
     *
     * @param float|int $maxSize 文件最大上传大小,默认2M
     * @return array
     * @throws \Exception
     */
    public function getSignature($maxSize = 1024 * 1024 * 2)
    {
        $expire = 30; //设置该policy超时时间是10s. 即这个policy过了这个有效时间，将不能访问

        $callback_param = [
            'callbackUrl' => $this->callbackUrl,
            'callbackBody' => 'filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}&format=${imageInfo.format}&md5=${x:md5}&merchant_id=${x:merchant_id}&type=${x:type}&host=${x:host}&upload_id=${x:upload_id}',
            'callbackBodyType' => "application/x-www-form-urlencoded"
        ];

        $base64_callback_body = base64_encode(Json::encode($callback_param));

        $expiration = $this->expiration(time() + $expire);
        // 最大文件大小
        $conditions[] = ['content-length-range', 0, $maxSize];
        $arr = [
            'expiration' => $expiration,
            'conditions' => $conditions
        ];
        $policy = Json::encode($arr);
        $base64_policy = base64_encode($policy);
        $signature = base64_encode(hash_hmac('sha1', $base64_policy, $this->accessKeySecret, true));

        $data = [
            'host' => $this->host,
            'accessID' => $this->accessKeyID,
            'policy' => $base64_policy,
            'signature' => $signature,
            'callback' => $base64_callback_body
        ];

        return $data;
    }

    /**
     * 截止日期
     *
     * @param $time
     * @return string
     * @throws \Exception
     */
    protected function expiration($time)
    {
        $dtStr = date("c", $time);
        $datatime = new \DateTime($dtStr);
        $expiration = $datatime->format(\DateTime::ISO8601);
        $pos = strpos($expiration, '+');
        $expiration = substr($expiration, 0, $pos);

        return $expiration . "Z";
    }

    /**
     * @return array|bool
     * @throws \Exception
     */
    public static function signVerify()
    {
        // 1.获取OSS的签名header和公钥url header
        $authorizationBase64 = Yii::$app->request->headers->get('authorization');
        $pubKeyUrlBase64 = Yii::$app->request->headers->get('x-oss-pub-key-url');
        if (!$authorizationBase64 || !$pubKeyUrlBase64) {
            return false;
        }

        // 2.获取OSS的签名
        $authorization = base64_decode($authorizationBase64);

        // 3.获取公钥
        $pubKeyUrl = base64_decode($pubKeyUrlBase64);
        $curl = new Curl();
        $pubKey = $curl->get($pubKeyUrl);
        if ($pubKey == "") {
            return false;
        }

        // 4.获取回调body
        $body = file_get_contents('php://input');

        // 5.拼接待签名字符串
        $path = $_SERVER['REQUEST_URI'];
        $pos = strpos($path, '?');
        if ($pos === false) {
            $authStr = urldecode($path) . "\n" . $body;
        } else {
            $authStr = urldecode(substr($path, 0, $pos)) . substr($path, $pos, strlen($path) - $pos) . "\n" . $body;
        }

        // 6.验证签名
        $res = openssl_verify($authStr, $authorization, $pubKey, OPENSSL_ALGO_MD5);
        if ($res == 1) {
            return true;
        }

        return false;
    }

    public static function saveImgToOss($imageUrl, $fileName = '', $ossSaveFilePath = '')
    {
        try {
            // 保存路径和文件名
            $path = Yii::getAlias('@backendapi/runtime/img');
            FileHelper::createDirectory($path);
            if (empty($fileName)) {
                $fileName = time() . round(microtime(true) * 1000) . '.jpg';
            }
            $savePath =  $path . '/' . $fileName;

            $curl = new \linslin\yii2\curl\Curl();
            $curl->setOption(CURLOPT_TIMEOUT, 30);
            $curl->setOption(CURLOPT_CONNECTTIMEOUT, 10);
            $curl->setOption(CURLOPT_SSL_VERIFYPEER, false);
            $curl->setOption(CURLOPT_SSL_VERIFYHOST, false);
            
            $response = $curl->get($imageUrl);
            if ($curl->responseCode !== 200) {
                throw new Exception('下载图片失败，HTTP状态码：' . $curl->responseCode);
            }
            
            if (!file_put_contents($savePath, $response)) {
                throw new Exception('保存图片时发生错误。');
            }

            $config = Yii::$app->params['AliyunOss'];
            $aliOss = new OssAdapter([
                'accessId'       => $config['AccessKeyID'],
                'accessSecret'   => $config['AccessKeySecret'],
                'bucket'         => $config['Bucket'],
                'endpoint'       => $config['Endpoint'],
            ]);

            // 上传到OSS  
            $configNew = new \League\Flysystem\Config();
            if (empty($ossSaveFilePath)) {
                $ossSaveFilePath = 'chz/erp_bucket/images/img/';
            }
            $ossSaveFilePath = $ossSaveFilePath . $fileName;
            $is_exit = $aliOss->has($ossSaveFilePath);
            if (!$is_exit) {
                $result = $aliOss->write($ossSaveFilePath, file_get_contents($savePath), $configNew);
            } else {
                $result = $aliOss->getMetadata($ossSaveFilePath);
            }
            $imgUrl = ArrayHelper::getValue($result, 'info.url', '');
            if ($imgUrl) {
                $search = "http://";
                $replace = "https://";
                $imgUrl = str_ireplace($search, $replace, $imgUrl);
            }
            if (file_exists($savePath)) {
                unlink($savePath);
            }
            return ['url' => $imgUrl];
        } catch (Exception $e) {
            echo $e->getMessage();
            if (file_exists($savePath)) {
                unlink($savePath);
            }
            return false;
        }
    }

    /**
     * 文件上传
     * @param $fieldPath 文件路径绝对路径
     * @param $fileName 文件名称
     * @param string $ossSaveFilePath 上传到oss文件路径
     * @return array|bool
     * @throws Exception
     */
    public static function saveImgV2ToOss($fieldPath, $fileName, $ossSaveFilePath = 'chz/erp_bucket/files/common/')
    {
        try {
            $config = Yii::$app->params['AliyunOss'];
            $aliOss = new OssAdapter([
                'accessId'       => $config['AccessKeyID'],
                'accessSecret'   => $config['AccessKeySecret'],
                'bucket'         => $config['Bucket'],
                'endpoint'       => $config['Endpoint'],
            ]);

            $savePath =  $fieldPath . '/' . $fileName;

            if (!file_exists($savePath)) {
                throw new Exception('文件不存在。');
            }

            // 上传到OSS  
            $configNew = new \League\Flysystem\Config();
            $ossSaveFilePath = $ossSaveFilePath . $fileName;
            $is_exit = $aliOss->has($ossSaveFilePath);
            if (!$is_exit) {
                $result = $aliOss->write($ossSaveFilePath, file_get_contents($savePath), $configNew);
            } else {
                $result = $aliOss->getMetadata($ossSaveFilePath);
            }
            $imgUrl = ArrayHelper::getValue($result, 'info.url', '');

            if ($imgUrl) {
                $search = "http://";
                $replace = "https://";
                $imgUrl = str_ireplace($search, $replace, $imgUrl);
            }
            if (file_exists($savePath)) {
                unlink($savePath);
            }
            return ['url' => $imgUrl];
        } catch (Exception $e) {
            echo $e->getMessage();
            return false;
        }
    }
}
