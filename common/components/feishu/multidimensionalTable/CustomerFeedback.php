<?php

namespace common\components\feishu\multidimensionalTable;

use common\enums\CustomerAgeBracket;
use common\helpers\ImageHelper;
use common\models\backend\order\OrderHeader;
use common\models\customer\Feedback;
use common\services\member\FeishuUserService;
use common\models\Customer;
use Exception;
use Yii;

class CustomerFeedback extends Base
{
    protected $appToken = 'LmXFbT1H8aTP4rsBw9NcQ7j2nyc';
    protected $tableId = 'tblBRKmMK8NxUd1V';

    /**
     * 处理飞书多维表格的客资反馈数据
     *
     * @return bool
     * @throws Exception
     */
    public function run()
    {
        $app_token = $this->message['event']['file_token'];
        $table_id = $this->message['event']['table_id'];
        $record_id = $this->message['event']['action_list'][0]['record_id'];

        $action = $this->message['event']['action_list'][0]['action'];
        if ($action != 'record_added') return true;

        $info = $this->feishuClass->getMoreTableSingleData($app_token, $table_id, $record_id);
        if ($info['code'] != 0) throw new Exception('获取数据失败，原因：' . $info['msg']);

        $fields = $info['data']['record']['fields'];
        $orderId = $fields['订单ID'] ?? 0;
        if (empty($orderId)) throw new Exception('获取数据失败，订单ID为空');

        $feishuUserId = $this->message['event']['operator_id']['user_id'] ?? '';
        $fields['created_by'] = FeishuUserService::getMemberId($feishuUserId, $this->message['ComCode']);
        $fields['feishu_userid'] = $feishuUserId;

        $result = $this->processCustomerFeedbackRecord($fields);
        if (!$result) throw new Exception('处理客资反馈表数据失败，订单ID：' . $orderId);

        return true;
    }

    private function processCustomerFeedbackRecord(array $fields)
    {
        $orderId = $fields['订单ID'];
        $orderInfo = OrderHeader::find()
            ->select(['id', 'cus_id', 'store_id', 'plan_time'])
            ->where(['id' => $orderId])
            ->one();
        if (empty($orderInfo)) {
            Yii::error('订单信息不存在，订单ID：' . $orderId, __FUNCTION__);
            return false;
        }

        $feedback = new Feedback();
        $feedback->order_id = $orderId;
        $feedback->cus_id = $orderInfo->cus_id;
        $feedback->store_id = $orderInfo->store_id;
        $feedback->plan_time = $orderInfo->plan_time;
        $feedback->age_bracket = CustomerAgeBracket::getKeyByValue($fields['年龄段']);
        $feedback->feedback = $fields['反馈内容'] ?? '';
        $ossImages = ImageHelper::processFeishuImages($fields['图片'] ?? [], $this->feishuClass, 'chz/erp_bucket/images/customer_feedback/');
        $feedback->images = json_encode($ossImages, JSON_UNESCAPED_UNICODE);
        $feedback->created_by = $fields['created_by'];

        // 先给提交人发送飞书卡片消息
        $this->sendCustomerFeedbackCardMessage([
            'customer_name' => $fields['客户姓名'] ?? '',
            'age_bracket' => $fields['年龄段'] ?? '',
            'feedback' => $fields['反馈内容'] ?? '',
            'images' => $ossImages,
            'feishu_userid' => $fields['feishu_userid'] ?? '',
            'com_code' => $this->message['ComCode'],
        ]);

        if (!$feedback->save()) {
            Yii::error('保存客资反馈数据失败，订单ID：' . $orderId . '，错误信息：' . json_encode($feedback->getErrors()), __FUNCTION__);
            return false;
        }

        $customer = Customer::findOne($orderInfo->cus_id);
        $customer->age_bracket = $feedback->age_bracket;
        if (!$customer->save()) {
            Yii::error('更新客户年龄段失败，客户ID：' . $customer->id . '，错误信息：' . json_encode($customer->getErrors()), __FUNCTION__);
            return false;
        }

        return true;
    }

    protected function sendCustomerFeedbackCardMessage(array $data)
    {
        $imageKeys = $this->prepareImageKeys($data['images']);
        $cardContent = $this->buildFeedbackCardContent($data, $imageKeys);
        
        Yii::$app->feishuNotice->card($cardContent, $data['feishu_userid'], 'user_id');
    }

    /**
     * 准备图片键值列表
     * 
     * @param array $images 图片URL列表
     * @return array 图片键值对象列表
     */
    private function prepareImageKeys(array $images)
    {
        $imageKeys = [];
        
        foreach ($images as $imageUrl) {
            $uploadResult = $this->feishuClass->uploadImage('message', $imageUrl, 'url');
            
            if (!$uploadResult) {
                Yii::error("上传图片到飞书失败，图片URL：{$imageUrl}，错误信息：{$this->feishuClass->error_msg}", __FUNCTION__);
                continue;
            }
            
            $imageKey = new \stdClass();
            $imageKey->img_key = $uploadResult['data']['image_key'];
            $imageKeys[] = $imageKey;
        }
        
        return $imageKeys;
    }

    /**
     * 构建客资反馈卡片内容
     * 
     * @param array $data 反馈数据
     * @param array $imageKeys 图片键值列表
     * @return array 卡片内容配置
     */
    private function buildFeedbackCardContent(array $data, array $imageKeys)
    {
        return [
            'schema' => '2.0',
            'config' => $this->getCardConfig(),
            'header' => $this->getCardHeader(),
            'body' => $this->getCardBody($data, $imageKeys)
        ];
    }

    /**
     * 获取卡片配置
     * 
     * @return array
     */
    private function getCardConfig()
    {
        return [
            'update_multi' => true,
            'style' => [
                'text_size' => [
                    'normal_v2' => [
                        'default' => 'normal',
                        'pc' => 'normal',
                        'mobile' => 'heading'
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取卡片头部
     * 
     * @return array
     */
    private function getCardHeader()
    {
        return [
            'title' => [
                'tag' => 'plain_text',
                'content' => '客资反馈-添加成功'
            ],
            'subtitle' => [
                'tag' => 'plain_text',
                'content' => ''
            ],
            'template' => 'green',
            'padding' => '12px 12px 12px 12px'
        ];
    }

    /**
     * 获取卡片主体内容
     * 
     * @param array $data 反馈数据
     * @param array $imageKeys 图片键值列表
     * @return array
     */
    private function getCardBody(array $data, array $imageKeys)
    {
        return [
            'direction' => 'vertical',
            'horizontal_spacing' => '8px',
            'vertical_spacing' => '8px',
            'horizontal_align' => 'right',
            'vertical_align' => 'center',
            'padding' => '12px 12px 12px 12px',
            'elements' => [
                $this->createTextElement('客户姓名：' . $data['customer_name']),
                $this->createTextElement('年龄段：' . $data['age_bracket']),
                $this->createTextElement('反馈内容：' . $data['feedback'], '0px 0px 10px 0px'),
                $this->createImageElement($imageKeys)
            ]
        ];
    }

    /**
     * 创建文本元素
     * 
     * @param string $content 文本内容
     * @param string $margin 边距，默认为 '0px 0px 0px 0px'
     * @return array
     */
    private function createTextElement($content, $margin = '0px 0px 0px 0px')
    {
        return [
            'tag' => 'div',
            'text' => [
                'tag' => 'plain_text',
                'content' => $content,
                'text_size' => 'normal_v2',
                'text_align' => 'left',
                'text_color' => 'default'
            ],
            'margin' => $margin
        ];
    }

    /**
     * 创建图片组合元素
     * 
     * @param array $imageKeys 图片键值列表
     * @return array
     */
    private function createImageElement(array $imageKeys)
    {
        return [
            'tag' => 'img_combination',
            'combination_mode' => 'trisect',
            'img_list' => $imageKeys,
            'img_list_length' => 6,
            'combination_transparent' => true,
            'margin' => '0px 0px 0px 0px'
        ];
    }
}