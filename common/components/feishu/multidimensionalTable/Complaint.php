<?php

/**
 * 投诉建议-客诉工单多维表格
 */

namespace common\components\feishu\multidimensionalTable;

use auth\services\feishu\ToolService;
use common\models\backend\Member;
use common\models\complaint\Complaint as ComplaintComplaint;
use yii\db\Exception;
use Yii;

class Complaint extends Base
{
    /**
     * 新增数据
     * 
     * 数据格式
     */
    public function createRecords(ComplaintComplaint $complaint)
    {
        try {
            $addData = [
                '工单ID' => (string)$complaint->id,
                '投诉时间' => $complaint->created_at * 1000,
                '客户' => $complaint->name,
                '联系方式' => $complaint->mobile,
                '投诉账号' => $complaint->respondent->name,
                '投诉原因' => $complaint->reason->content,
                '投诉内容' => $complaint->content,
            ];

            $storeTokenInfo = [
                'table_file_token' => 'Ocw6ba6hna4edQs4FbAc5dQCnfh',
                'table_id' => 'tblTv2JxQrk7TVW9'
            ];

            $parameters = '';
            if ($complaint->respondent_user_id) {
                $union_id = \common\services\feishu\UserService::getCusServiceUnionIdByID($complaint->respondent_user_id);
                $union_ids = [
                    ['id' => $union_id],
                ];
                $addData['对应人员'] = $union_ids;
                $parameters = '?user_id_type=union_id';
            }

            $imageUrl = $complaint->imgText;
            if ($imageUrl) {
                $imgDir = Yii::getAlias('@mobileapi/runtime/img');
                $uploadRes = ToolService::uploadImageUrlToMultidimensionalTable($imageUrl, $storeTokenInfo['table_file_token'], $imgDir);
                $fileData = ToolService::realToMultidimensionalTableImage($uploadRes);
                $addData['图片举证'] = $fileData;
            }

            $res = $this->feishuClass->createMoreTableData($storeTokenInfo['table_file_token'], $storeTokenInfo['table_id'], $addData, $parameters);
            unset($this->feishuClass);

            if ($res['code'] != 0) {
                throw new Exception('”投诉建议“新增到多维表格数据失败，原因：' . $res['msg'] . ',请联系信息部门');
            }
        } catch (Exception $e) {
            $error = 'ID：' . $complaint->id . '，' . $e->getMessage();
            Yii::$app->feishuNotice->text($error);
            Yii::info($error, 'Complaint');
        }

        return true;
    }
}
