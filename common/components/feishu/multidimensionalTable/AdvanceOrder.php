<?php

/**
 * 已预约订单-多维表格
 */

namespace common\components\feishu\multidimensionalTable;

use common\models\backend\Store;
use auth\models\TeacherJob;
use common\components\Feishu;
use common\enums\MultidimensionalTableConfigEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\WhetherEnum;
use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\models\backend\Member;
use common\models\Customer;
use common\models\feishu\App;
use common\models\feishu\MultidimensionalTableConfig;
use common\models\StoreTableAssociation;
use console\models\OrderHeader;
use yii\db\Exception;
use Yii;

class AdvanceOrder extends Base
{
    const CUSTOMER_FEEDBACK_URL = 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnjdvWVhs5rbVugyoTNporwc';

    /**
     * 回调事件执行
     */
    public function run()
    {
        $app_token = $this->message['event']['file_token'];
        $table_id = $this->message['event']['table_id'];
        $record_id = $this->message['event']['action_list'][0]['record_id'];
        $operator_id = $this->message['event']['operator_id']['user_id'];
        // action只有编辑才操作，其他不做操作
        if ($this->message['event']['action_list'][0]['action'] != 'record_edited') {
            return true;
        }

        $info = $this->feishuClass->getMoreTableSingleData($app_token, $table_id, $record_id);
        if ($info['code'] != 0) {
            throw new Exception('获取数据失败，原因：' . $info['msg']);
        }
        $is_acceptance = $info['data']['record']['fields']['是否到店'];
        if ($is_acceptance != '已到店') return true;

        $after_value = $this->message['event']['action_list'][0]['after_value'];
        $feishuUserInfo = [];
        foreach ($after_value as $v) {
            if ($v['field_id'] == 'fldzMcgS6F') {
                $feishuUserInfo = ArrayHelper::getValue($v, 'field_identity_value.users.0.user_id');
                break;
            }
        }

        if (empty($feishuUserInfo)) {
            return true;
        }

        $orderID = $info['data']['record']['fields']['订单ID(请勿修改)'];
        $orderModel = OrderHeader::findOne($orderID);
        if (empty($orderModel)) {
            throw new Exception('订单不存在，订单ID' . $orderID);
        }
        $status = [
            OrderHeaderStatusEnum::STATUS_PLAN,
            OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
            OrderHeaderStatusEnum::STATUS_SETTLEMENT,
            OrderHeaderStatusEnum::STATUS_COMPLETED,
        ];
        if (!in_array($orderModel->order_status, $status)) {
            return true;
        }

        $user_id = Member::find()->select('id')->where(['feishu_unionid' => $feishuUserInfo['union_id'], 'feishu_userid' => $feishuUserInfo['user_id']])->scalar() ?: 0;
        if (empty($user_id)) {
            return true;
        }

        $teacherInfo =  TeacherJob::find()->alias('tj')
            ->select('tj.id,tj.user_id')
            ->where(['user_id' => $user_id, 'teacher_type_id' => 1, 'order_type_id' => 1])
            ->one();

        if ($teacherInfo && $teacherInfo->user_id) {
            $orderModel->plan_user_id = $teacherInfo->user_id;
            $orderModel->plan_teacher_id = $teacherInfo->id;
        } else {
            return true;
        }

        $isSendMsg = false;
        if ($orderModel->order_status == OrderHeaderStatusEnum::STATUS_PLAN) {
            $isSendMsg = true;
            $orderModel->order_status = OrderHeaderStatusEnum::STATUS_ARRIVED_STORE;
            $orderModel->reach_time = time();
        }

        $res = $orderModel->save(false);
        if ($res === false) {
            throw new Exception('订单保存失败,失败原因：' . current($orderModel->getFirstErrors()));
        }

        if ($isSendMsg) {
            // 发送站通知
            static::sendInform($orderModel);
        }

        return true;
    }

    /**
     * 客户到店通知
     */
    public static function sendInform($orderInfo)
    {
        if (!$orderInfo->plan_by) {
            return true;
        }

        //正式站才发送
        if (YII_ENV != 'prod') {
            return true;
        }

        $getUnionIdError = '';
        try {
            $feishu_unionid = \common\services\feishu\UserService::getCusServiceUnionIdByID($orderInfo->plan_by);
        } catch (Exception $e) {
            $feishu_unionid = '';
            $getUnionIdError = $e->getMessage();
        }

        $error  = '';
        if ($feishu_unionid) {
            $storeName = Store::find()->select('store_name')->where(['id' => $orderInfo->store_id])->scalar();
            $customer = Customer::find()->select('name,mobile')->where(['id' => $orderInfo->cus_id])->asArray()->one();
            $content = '<b>客户已到店</b>' . PHP_EOL . PHP_EOL;
            $content .= '门店名称：' . $storeName . PHP_EOL;
            $content .= '预约时间：' . DateHelper::toDate($orderInfo->plan_time, 'Y-m-d H:i') . PHP_EOL;
            $content .= '客户姓名：' . $customer['name'] . PHP_EOL;
            $content .= '手机号码：' . $customer['mobile'] . PHP_EOL;

            $msg = ['text' => $content];
            $feishu = new Feishu();
            $res = $feishu->sendWorkNotice($msg, $feishu_unionid, 'text', 'union_id');
            if ($res['code'] != 0) {
                $error = '<b>点到店通知失败</b>' . PHP_EOL . PHP_EOL;
                $error .= '客户到店通知失败,操作订单号:' . $orderInfo->order_no . PHP_EOL;
                $error .= '失败原因：' . json_encode($res, 256);
            }
        } else {
            $error = '<b>点到店通知失败</b>' . PHP_EOL . PHP_EOL;
            $error .= '客户到店通知失败,操作订单号:' . $orderInfo->order_no . PHP_EOL;
            $error .= '失败原因：feishu_unionid不存在原因，' . $getUnionIdError;
        }
        if ($error) {
            Yii::$app->feishuNotice->barkMsg($error, '到店通知');
        }
    }

    /**
     * 新增数据
     * 
     * 数据格式
     * $addData =$data = ['订单ID' => '100','手机尾号' => '5011','客户姓名' => '石义','日期' => 1736589238715,'门店名称'=> '榆林测试店'];
     * 
     */
    public function createRecords($addData, $storeId)
    {
        $date = date('Y-m-d');
        $redis = Yii::$app->cache;
        $redisKey = 'AdvanceOrder_' . $date . ':' . $storeId;
        $addOrderData = $redis->get($redisKey);
        $order_id = $addData['订单ID(请勿修改)'];
        if ($addOrderData) {
            $arrAddOrderId = ArrayHelper::getColumn($addOrderData, 'order_id');
            if (in_array($order_id, $arrAddOrderId)) {
                return true;
            }
        }

        $storeTokenInfo = static::getTokenByStoreID($storeId);
        $res = $this->feishuClass->createMoreTableData($storeTokenInfo['table_file_token'], $storeTokenInfo['table_id'], $addData);
        unset($this->feishuClass);
        if ($res['code'] != 0) {
            Yii::info($addData, 'createRecords');
            throw new Exception('新增到多维表格数据失败，原因：' . $res['msg'] . ',请联系信息部门');
        }

        $addOrderData[] = [
            'order_id' => $order_id,
            'record_id' => $res['data']['record']['record_id'],
        ];

        $duration = strtotime($date) + 86399 -  time();
        $redis->set($redisKey, $addOrderData, $duration);
        return true;
    }

    /**
     * 获取token
     */
    public function getTokenByStoreID($storeId)
    {
        $info = MultidimensionalTableConfig::storeTableConfig($storeId, MultidimensionalTableConfigEnum::ArriveStore);
        if (empty($info) || empty($info['table_file_token']) || empty($info['table_id'])) {
            throw new Exception('同步到多维度表格数据失败，原因：未配置多维度表格file_token和table_id，请联系信息部门');
        }

        return $info;
    }

    public function updateTable($record_id, $data)
    {
        if (empty($record_id)) {
            throw new Exception('多维表格数据修改失败，原因：表格record_id为空');
        }
        $storeTokenInfo = static::getTokenByStoreID($data['store_id']);
        $parameters = '';
        if (isset($data['plan_time']) && !empty($data['plan_time'])) {
            $changeData = [
                '日期' => $data['plan_time'] * 1000
            ];
        } else {
            $changeData = [
                '是否到店' => '已到店'
            ];

            $union_id = $this->getUnionId($data['plan_user_id']);
            if ($union_id) {
                $union_ids = [
                    ['id' => $union_id],
                ];

                $changeData['接待老师'] = $union_ids;
                $parameters = '?user_id_type=union_id';
            }
        }

        $res = $this->feishuClass->updateMoreTableData($storeTokenInfo['table_file_token'], $storeTokenInfo['table_id'], $record_id, $changeData, $parameters);
        if ($res['code'] != 0) {
            throw new Exception('多维表格数据修改失败，原因：' . $res['msg'] . ',请联系信息部门');
        }

        return true;
    }

    public function deleteTable($record_id, $storeId)
    {
        if (empty($record_id)) {
            throw new Exception('多维表格数据删除失败，原因：表格record_id为空');
        }
        $storeTokenInfo = static::getTokenByStoreID($storeId);
        $res = $this->feishuClass->deleteMoreTableData($storeTokenInfo['table_file_token'], $storeTokenInfo['table_id'], $record_id);
        if ($res['code'] != 0) {
            throw new Exception('多维表格数据删除失败，原因：' . $res['msg'] . ',请联系信息部门');
        }
        return true;
    }

    /**
     * 删除所有数据
     */
    public function deleteAll()
    {
        $tokenList = MultidimensionalTableConfig::find()->alias('mt')
            ->select('mt.id,st.store_id,mt.table_file_token,mt.table_id')
            ->leftJoin(['st' => StoreTableAssociation::tableName()], 'st.table_config_id = mt.id')
            ->where(['type' => MultidimensionalTableConfigEnum::ArriveStore])
            ->asArray()
            ->all();

        $arrStoreId = [];
        foreach ($tokenList as $token) {
            $arrStoreId[] = $token['store_id'];
            $data = $this->feishuClass->getMoreTableDataAll($token['table_file_token'], $token['table_id']);
            if (empty($data)) {
                continue;
            }
            $record_ids = array_column($data, 'record_id');
            $res = $this->feishuClass->multipleDeleteMoreTableData($token['table_file_token'], $token['table_id'], $record_ids);
            if ($res['code'] != 0) {
                $error = [
                    '门店ID' => $token['store_id'],
                    'MultidimensionalTableConfigID' => $token['id'],
                    'error_msg' => $res
                ];
                Yii::error($error, '删除已预约多维表数据');
            }
        }

        if (empty($arrStoreId)) {
            return true;
        }

        $arrStoreId = array_unique($arrStoreId);
        $redis = Yii::$app->cache;
        $date = date('Y-m-d');
        foreach ($arrStoreId as $storeId) {
            $redisKey = 'AdvanceOrder_' . $date . ':' . $storeId;
            $redis->delete($redisKey);
        }
    }

    public function getUnionId($user_id)
    {
        return Member::find()->select('feishu_unionid')->where(['id' => $user_id])->scalar();
    }
}
