<?php

/**
 * 飞书
 *
 * <AUTHOR> 
 */

namespace common\components;

use common\helpers\DateHelper;
use common\helpers\Tool;
use common\services\member\FeishuUserService;
use common\components\AliyunOss;
use GuzzleHttp\Client;
use Yii;
use Exception;

/**
 * Class Feishu
 * @package common\components
 * <AUTHOR>
 */
class Feishu
{
    public $appId;
    public $appSecret;
    public $access_token;
    public $entity_id;
    public $token;
    public $error_msg = '';
    public $fieldFilterList = '';
    public $feishu_app_id; //指的系统表erp_feishu_app的id

    #飞书请求地址
    const url_server = 'https://open.feishu.cn/';
    const url_passport_server = 'https://passport.feishu.cn/';
    // const live_time = 3600; //token有效时间1个小时

    /**
     * Feishu constructor.
     *
     * Feishu constructor.
     * @param string $appCode 飞书应用编码
     * @throws \Exception
     */
    public function __construct($appCode = 'chz')
    {
        if ($appCode == 'endurise') {
            $appCode = 'chz';
        }

        $feishuApp = FeishuUserService::getFeishuAppInfo($appCode);

        $this->feishu_app_id = $feishuApp->id;
        $this->entity_id = $feishuApp->entity_id;
        $this->appId = $feishuApp->appId;
        $this->appSecret = $feishuApp->appSecret;
        $this->token = $this->getTenantAccessToken();
    }

    /**
     * @brief 获得token
     *
     * @param string $code
     * @return bool|mixed
     * @throws \Exception
     */
    public function getAccessToken($code = '', $redirect_uri)
    {
        $token = '';

        if (!$token) {
            $url = self::url_passport_server . 'suite/passport/oauth/token';
            $params = array(
                'grant_type'    => 'authorization_code',
                'client_id'     => $this->appId,
                'client_secret' => $this->appSecret,
                'code'          => $code,
                'redirect_uri'  => $redirect_uri
            );
            $header = array(
                'Content-Type: application/x-www-form-urlencoded'
            );

            $res = Tool::feishuPost($header, $url, $params, true);
            if (isset($res['access_token'])) {
                return $res['access_token'];
            } else {
                $this->error_msg = $res['error_description'];
                return false;
            }
        }
        return $token;
    }

    /**
     * @brief 通过临时授权码获取授权用户的个人信息
     *
     * @param $code
     * @return bool|mixed
     * @throws \Exception
     */
    public function getSweepLoginUserInfo($access_token)
    {
        $url = self::url_passport_server . 'suite/passport/oauth/userinfo';
        $header = array(
            'Authorization: Bearer ' . $access_token,
        );
        $params = [];

        $res = Tool::feishuGet($header, $url, $params, false);
        if (isset($res['user_id'])) {
            return $this->resData($res);
        } else {
            $this->error_msg = $res['error_description'];
            return false;
        }
    }

    /**
     * @brief 通过userID获取用户信息
     *
     * @param $userID
     * @return bool
     * @throws \Exception
     */
    public function getUserInfoByUserID($feishu_user_id)
    {
        $url = self::url_server . 'open-apis/contact/v3/users/' . $feishu_user_id;

        $header = [
            'Authorization: Bearer ' . $this->token,
        ];
        $data = [
            'user_id_type'  => 'user_id',
        ];
        $res = Tool::feishuGet($header, $url, $data, false);
        return $this->resData($res);
    }

    /**
     * @brief 获取应用access_token
     *
     * @return array|bool
     * @throws \Exception
     */
    public function getTenantAccessToken()
    {
        $redis = Yii::$app->cache;
        $key = 'feishu_token:' . $this->feishu_app_id;
        $token = $redis->get($key);

        if (!$token) {
            $url = self::url_server . 'open-apis/auth/v3/tenant_access_token/internal';
            $header = array(
                'Content-Type: application/json; charset=utf-8'
            );
            $data = array(
                'app_id' => $this->appId,
                'app_secret'    => $this->appSecret,
            );

            $res = Tool::feishuPost($header, $url, $data, false);
            if ($res['code'] == 0) {
                $redis->set($key, $res['tenant_access_token'], $res['expire']);
                return $res['tenant_access_token'];
            } else {
                $this->error_msg = $res['msg'];
                return false;
            }
        }

        return $token;
    }

    /**
     * 获取节点信息
     * 文档：https://open.feishu.cn/document/server-docs/docs/wiki-v2/space-node/get_node?appId=cli_a4804fd7ca71d00d
     */
    public function getNodeInfo($app_token, $obj_type)
    {
        $url = self::url_server . 'open-apis/wiki/v2/spaces/get_node?token=' . $app_token . '&obj_type=' . $obj_type;

        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->getTenantAccessToken(),
                'Content-Type' => 'application/json; charset=utf-8'
            ],
        ]);
      
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * 获取花名册人员信息
     *
     * @param $useridList
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getFeishuUserMsg($useridList)
    {
        $url = self::url_server . 'open-apis/ehr/v1/employees';
        $header = array(
            'Authorization: Bearer ' . $this->token
        );
        $data = array(
            "view" => 'full',
            'user_id_type' => 'user_id',
            "user_ids" => $useridList,
            "page_size" => 100,
        );

        $res = Tool::feishuGet($header, $url, $data, false);
        if ($res['code'] == 0) {
            return $res;
        } else {
            $this->error_msg = $res['error_description'];
            return false;
        }
    }

    /**
     * @brief 获取公司所有用户信息
     *
     * @return array|bool
     * @throws \Exception
     */
    public function getAllUser()
    {
        $department = $this->getDepartment($id = 1);
        if ($department) {
            $department_ids = array_map(function ($v) {
                return $v['id'];
            }, $department['department']);
            $userList = [];
            foreach ($department_ids as $k => $v) {
                $user = $this->getDepartmentUser($v);
                if ($user) {
                    $userList = array_merge($userList, $user['userlist']);
                }
            }
            //记录到指定文件
            //            file_put_contents("dingDing_userList.txt", var_export($userList, true) . "\r\n", FILE_APPEND);
            return $userList;
        }
        return false;
    }

    /**
     * @brief 获得部门
     *
     * @param int $id 上级部门id  1为根部门
     * @return bool|mixed
     * @throws \Exception
     */
    public function getDepartment($id = 1)
    {
        $url = self::url_server . 'department/list?access_token=' . $this->token . '&id=' . $id;
        $res = Tool::curlRequest($url);
        return $this->resData($res);
    }

    /**
     * 获取部门信息
     *
     * @param integer $id
     * @return void
     */
    public function getDepartmentInfoById($id = 1)
    {
        $url = self::url_server . 'topapi/v2/department/get?access_token=' . $this->token;
        return Tool::curlRequest($url, [
            'dept_id' => $id,
        ], 1);
    }

    /**
     * @brief 获得部门成员
     *
     * @param int $id 部门id
     * @return bool|mixed
     * @throws \Exception
     */
    public function getDepartmentUser($id = 1)
    {
        $url = self::url_server . 'user/simplelist?access_token=' . $this->token . '&department_id=' . $id;
        $res = Tool::curlRequest($url);
        return $this->resData($res);
    }

    /**
     * @brief 发送工作通知
     *
     * @param array $userid_list 用户id列表
     * @param array $data 消息内容
     * @param string $msgType 类型
     * @return bool|mixed
     * @throws \Exception
     */
    public function sendWorkNotice($msg, $feishu_userid, $msg_type, $receive_id_type)
    {
        $url = self::url_server . 'open-apis/im/v1/messages';
        $url .= '?receive_id_type=' . $receive_id_type;
        $header = array(
            'Authorization: Bearer ' . $this->token,
            'Content-Type: application/json; charset=utf-8',
        );

        if (!is_string($msg)) {
            $msg = json_encode($msg);
        }

        $reqData = array(
            'receive_id' => $feishu_userid,
            'msg_type' => $msg_type,
            'content' => $msg,
        );

        $res = Tool::feishuPost($header, $url, $reqData, false);
        return $res;
    }

    /**
     * 获取机器人群列表
     */
    public function getBotInfo()
    {
        $url = self::url_server . 'open-apis/bot/v3/info';
        $header = [
            'Authorization: Bearer ' . $this->token,
        ];
        $res = Tool::feishuGet($header, $url, array(), false);
        return $this->resData($res);
    }

    /**
     * 获取机器人群列表
     */
    public function getChatList()
    {
        $page_size = 100;
        $url = self::url_server . 'open-apis/im/v1/chats?page_size=' . $page_size;
        $header = [
            'Authorization: Bearer ' . $this->token,
        ];
        $res = Tool::feishuGet($header, $url, array(), false);
        return $this->resData($res);
    }

    /**
     * 获取群成员列表
     * 
     * api文档：https://open.feishu.cn/document/server-docs/group/chat-member/get?appId=cli_a4804fd7ca71d00d
     * 
     * @param string $chat_id 群id
     * @param string $member_id_type 成员id类型 默认：open_id，可选：open_id、union_id、user_id
     */
    public function getChatListOne($chat_id, $member_id_type = 'open_id')
    {
        $page_size = 100;
        $baseUrl = self::url_server . 'open-apis/im/v1/chats/' . $chat_id . '/members?member_id_type=' . $member_id_type . '&page_size=' . $page_size;
        $header = [
            'Authorization: Bearer ' . $this->token,
        ];
        $res = Tool::feishuGet($header, $baseUrl, array(), false);
        $result = $this->resData($res);
        if ($result['code'] != 0) {
            throw new Exception('获取群成员失败：' . $result['msg']);
        }
        $data = $result['data']['items'];
        if ($result['data']['has_more']) {
            $page_token = $result['data']['page_token'];
            while ($page_token) {
                $url = $baseUrl . '&page_token=' . $page_token;
                $res = Tool::feishuGet($header, $url, array(), false);
                $result = $this->resData($res);
                if ($result['code'] != 0) {
                    throw new Exception('获取群成员失败：' . $result['msg']);
                }
                $data = array_merge($data, $result['data']['items']);
                if ($result['data']['has_more']) {
                    $page_token = $result['data']['page_token'];
                } else {
                    $page_token = null;
                }
            }
        }
        return $data;
    }

    /**
     * @brief 数据处理
     *
     * @param $data
     * @return bool
     */
    private function resData($data)
    {
        if ($data['code'] == 0) {
            return $data;
        } else {
            $this->error_msg = $data['msg'];
            return false;
        }
    }

    /**
     * @brief 获取到毫秒时间
     *
     * @return float
     */
    private function getMillisecond()
    {
        list($s1, $s2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
    }

    /**
     * 飞书创建审批流程
     *
     * @param $feishu_userid
     * @param $form_component_values
     * @param $process_code
     * @return mixed
     * @throws \Exception
     */
    public function processCreate($feishu_userid, $form_component_values, $approval_code)
    {
        $url = self::url_server . 'open-apis/approval/v4/instances';
        $header = [
            'Authorization: Bearer ' . $this->token,
            'Content-Type: application/json; charset=utf-8',
        ];
        $data = [
            'approval_code' => $approval_code,
            'user_id' => $feishu_userid,
            'form' => json_encode($form_component_values),
        ];

        return Tool::feishuPost($header, $url, $data);
    }

    /**
     * 审批单定义的详情
     */
    public function processInfo($approval_code)
    {
        $url = self::url_server . 'open-apis/approval/v4/approvals/' . $approval_code;
        $header = array(
            'Authorization: Bearer ' . $this->token,
        );
        $data = array();
        return Tool::feishuGet($header, $url, $data);
    }

    /**
     * 审批订阅
     */
    public function approvalsSubscribe($approval_code)
    {
        $url = self::url_server . "open-apis/approval/v4/approvals/{$approval_code}/subscribe";
        $header = array(
            'Authorization: Bearer ' . $this->token,
        );
        return Tool::feishuPost($header, $url);
    }

    /**
     * 审批取消订阅
     */
    public function approvalsUnSubscribe($approval_code)
    {
        $url = self::url_server . "open-apis/approval/v4/approvals/{$approval_code}/unsubscribe";
        $header = array(
            'Authorization: Bearer ' . $this->token,
        );
        return Tool::feishuPost($header, $url);
    }

    /**
     * 云文档事件订阅
     * file_type  文档类型:
     *                   doc : 文档
     *                   docx : 新版文档
     *                   sheet : 表格
     *                   bitable : 多维表格 
     */
    public function fileSubscribe($file_token, $file_type = 'bitable')
    {
        $url = self::url_server . "open-apis/drive/v1/files/{$file_token}/subscribe?file_type={$file_type}";
        $header = array(
            'Authorization: Bearer ' . $this->token,
        );
        return Tool::feishuPost($header, $url);
    }

    /**
     * 云文档事件取消订阅----飞书
     * file_type  文档类型:
     *                   doc : 文档
     *                   docx : 新版文档
     *                   sheet : 表格
     *                   bitable : 多维表格 
     */
    public function fileUnSubscribe($file_token, $file_type = 'bitable')
    {
        $url = self::url_server . "open-apis/drive/v1/files/{$file_token}/delete_subscribe?file_type={$file_type}";
        $header = array(
            'Authorization: Bearer ' . $this->token,
        );
        return Tool::feishuDelete($header, $url);
    }

    /**
     * 批量获取审批实例 ID
     *
     * @param $approval_code
     * @param $start_time
     * @param $end_time
     * @return mixed
     * @throws \Exception
     */
    public function getApproval($approval_code, $start_time = '', $end_time = '')
    {
        $date = DateHelper::today();
        $start_time = $start_time ?: $date['start'];
        $end_time = $end_time ?: $date['end'];

        $url = self::url_server . 'open-apis/approval/v4/instances';
        $header = array(
            'Authorization: Bearer ' . $this->token,
        );
        $data = array(
            'approval_code' => $approval_code,
            'start_time' => $start_time . '000',
            'end_time' => $end_time . '000',
        );
        return Tool::feishuGet($header, $url, $data);
    }

    /**
     * 获取单个审批实例详情
     *
     * @param $instance_id
     * @param $entity_id
     * @return mixed
     * @throws \Exception
     */
    public function getSingleApproval($instance_id)
    {
        $url = self::url_server . 'open-apis/approval/v4/instances/' . $instance_id;
        $header = array(
            'Authorization: Bearer ' . $this->token,
        );
        $data = array();
        return Tool::feishuGet($header, $url, $data);
    }

    /**
     * 上传审批流文件
     *
     * @param $file_name 文件名称
     * @param $file_path 文件地址
     * @param $type      类型：文件 => attachment 图片 => image
     * @return mixed
     * @throws \Exception
     */
    public function uploadApprovalFiles($file_name, $file_path, $type = 'image')
    {
        $url = self::url_server . 'approval/openapi/v2/file/upload';

        $header = [
            'Authorization: Bearer ' . $this->token,
            'Content-Type: multipart/form-data',
        ];

        $data = [
            'name' => $file_name,
            'type' => $type,
            'content' => new \CURLFile($file_path),
        ];
        return Tool::feishuPost($header, $url, $data, true, false);
    }

    /**
     * 根据部门获得用户userId
     *
     * @param int $dingtalkDepartmentId
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getDepartmentUserId($dingtalkDepartmentId = 1)
    {
        $url = self::url_server . 'topapi/user/listid';
        $params = [
            'query' => [
                "cursor" => "0",
                "contain_access_limit" => "false",
                "size" => "10",
                "order_field" => "modify_desc",
                "language" => "zh_CN",
                "access_token" => $this->token,
                "dept_id" => $dingtalkDepartmentId,
            ],
        ];
        return Tool::guzzleHttp('POST', $url, $params);
    }

    /**
     * 更新用户数据
     *
     * @param $data
     * @return bool
     * @throws \Exception
     */
    public function updateUser($data)
    {
        $url = self::url_server . 'topapi/v2/user/update?access_token=' . $this->token;
        $res = Tool::curlRequest($url, $data, true);
        return $this->resData($res);
    }

    /**
     * @brief 查询多维表格数据-单条
     *
     * @param string $app_token 多维表格的唯一标识符
     * @param string $table_id  多维表格数据表的唯一标识符
     * @param string $record_id 多维表格数据表中：一条记录的唯一标识
     * @param array $changeData 
     * @return array
     */
    public function getMoreTableSingleData($app_token, $table_id, $record_id)
    {
        $url = self::url_server . 'open-apis/bitable/v1/apps/' . $app_token . '/tables' . '/' . $table_id . '/records' . '/' . $record_id;

        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->getTenantAccessToken()
            ],
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * 获取多维表格数据-获取所有数据
     */
    public function getMoreTableDataAll($app_token, $table_id, $filterQuery = '', $page_token = '', $page_size = 100, $data = [])
    {
        $result = $this->getMoreTableData($app_token, $table_id, $filterQuery, $page_token, $page_size);

        if ($result['code'] != 0) {
            $this->error_msg = $result['msg'];
            return false;
        }

        if ($result['data']['total'] == 0) {
            return $data;
        }

        $data = array_merge($data, $result['data']['items']);
        if ($result['data']['has_more']) {
            return $this->getMoreTableDataAll($app_token, $table_id, $filterQuery, $result['data']['page_token'], $page_size, $data);
        }

        return $data;
    }

    /**
     * @brief 获取多维表格数据-按条件获取
     *
     * @param string $app_token 多维表格的唯一标识符
     * @param string $table_id 多维表格数据表的唯一标识符
     * @param number $page_size 分页，默认一页是20条，最大值500条
     * @param string $page_token 分页标记
     * @return array
     */
    public function getMoreTableData($app_token, $table_id, $filterQuery = '', $page_token = '', $page_size = 100)
    {
        $url = self::url_server . 'open-apis/bitable/v1/apps/' . $app_token . '/tables' . '/' . $table_id . '/records?page_size=' . $page_size;

        if ($filterQuery) {
            $url .= '&filter=' . urlencode($filterQuery);
        }
        if ($page_token) {
            $url .= '&page_token=' . $page_token;
        }

        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->getTenantAccessToken()
            ],
        ]);
        return json_decode($response->getBody()->getContents(), true);
    }

    public function getMoreTableDataSearch($app_token, $table_id, $parmas, $page_token = '', $page_size = 100)
    {
        $url = self::url_server . 'open-apis/bitable/v1/apps/' . $app_token . '/tables' . '/' . $table_id . '/records/search?page_size=' . $page_size;

        if ($page_token) {
            $url .= '&page_token=' . $page_token;
        }

        $body = json_encode($parmas);

        $response = (new \yii\httpclient\Client())->post($url, $body, [
            'Authorization' => 'Bearer ' . $this->getTenantAccessToken(),
            'Content-Type' => 'application/json'
        ])->send();
        return $response->getData();
    }

    /**
     * @brief 新增多维表格数据-单条记录
     *
     * @param string $app_token 多维表格的唯一标识符
     * @param string $table_id  多维表格数据表的唯一标识符
     * @param array $addData
     * @param string $parameters 参数
     */
    public function createMoreTableData($app_token, $table_id, $addData, $parameters = '')
    {
        $url = self::url_server . 'open-apis/bitable/v1/apps/' . $app_token . '/tables' . '/' . $table_id . '/records' . $parameters;

        $params = [
            'fields' => $addData
        ];

        $response = (new \yii\httpclient\Client())->post($url, json_encode($params), [
            'Authorization' => 'Bearer ' . $this->getTenantAccessToken(),
            'Content-Type' => 'application/json'
        ])->send();
        return $response->getData();
    }

    /**
     * @brief 修改多维表格数据-单条
     *
     * @param string $app_token 多维表格的唯一标识符
     * @param string $table_id  多维表格数据表的唯一标识符
     * @param string $record_id 多维表格数据表中：一条记录的唯一标识
     * @param array $changeData 
     * @param array $parameters 参数
     * @return array
     */
    public function updateMoreTableData($app_token, $table_id, $record_id, $changeData, $parameters = '')
    {
        $url = self::url_server . 'open-apis/bitable/v1/apps/' . $app_token . '/tables' . '/' . $table_id . '/records' . '/' . $record_id . $parameters;

        $params = [
            'fields' => $changeData
        ];

        $response = (new \yii\httpclient\Client())->put($url, json_encode($params), [
            'Authorization' => 'Bearer ' . $this->getTenantAccessToken(),
            'Content-Type' => 'application/json'
        ])->send();
        return $response->getData();
    }

    /**
     * @brief 删除多维表格数据-单条
     *
     * @param string $app_token 多维表格的唯一标识符
     * @param string $table_id  多维表格数据表的唯一标识符
     * @param string $record_id 多维表格数据表中：一条记录的唯一标识
     * @return array
     */
    public function deleteMoreTableData($app_token, $table_id, $record_id)
    {
        $url = self::url_server . 'open-apis/bitable/v1/apps/' . $app_token . '/tables' . '/' . $table_id . '/records' . '/' . $record_id;

        $response = (new \yii\httpclient\Client())->DELETE($url, null, [
            'Authorization' => 'Bearer ' . $this->getTenantAccessToken(),
            'Content-Type' => 'application/json'
        ])->send();
        return $response->getData();
    }

    /**
     * @brief 删除多维表格数据-多条
     *
     * @param string $app_token 多维表格的唯一标识符
     * @param string $table_id  多维表格数据表的唯一标识符
     * @param string $record_ids 多维表格数据表中：多条记录的唯一标识
     * @return array
     */
    public function multipleDeleteMoreTableData($app_token, $table_id, $record_ids)
    {
        $url = self::url_server . 'open-apis/bitable/v1/apps/' . $app_token . '/tables' . '/' . $table_id . '/records/batch_delete';

        $body = [
            'records' => $record_ids
        ];

        $response = (new \yii\httpclient\Client())->post($url, json_encode($body), [
            'Authorization' => 'Bearer ' . $this->getTenantAccessToken(),
            'Content-Type' => 'application/json'
        ])->send();
        return $response->getData();
    }

    /**
     * 获取图片数组
     */
    public function getImageUrl($file_token)
    {
        $res = $this->getTmpDownloadUrl($file_token);
        if ($res['code'] != 0) {
            return [];
        }

        $imgUrl = [];
        foreach ($res['data']['tmp_download_urls'] as $item) {
            $imgUrl[] = $item['tmp_download_url'];
        }

        return $imgUrl;
    }

    /**
     * 获取飞书下载图片
     */
    public function getTmpDownloadUrl($file_tokens)
    {
        $url = self::url_server . 'open-apis/drive/v1/medias/batch_get_tmp_download_url?file_tokens=' . $file_tokens;

        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->getTenantAccessToken()
            ],
        ]);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * 上传飞书素材
     * 文档：https://open.feishu.cn/document/server-docs/docs/drive-v1/media/upload_all
     * @param string $file_path 本地文件路径
     * @param string $parent_type 上传点的类型，如 docx_image、docx_file 等
     * @param string $parent_node 上传点的token，即要上传的云文档的token
     * @param string $file_name 文件名（可选，默认取文件路径名）
     * @return array|bool
     */
    public function uploadMedia($file_path, $parent_type = 'bitable_image', $parent_node = '', $file_name = '')
    {
        $url = self::url_server . 'open-apis/drive/v1/medias/upload_all';

        if (!file_exists($file_path)) {
            $this->error_msg = '文件不存在';
            return false;
        }

        if (!$file_name) {
            $file_name = basename($file_path);
        }

        // 获取文件大小
        $file_size = filesize($file_path);
        
        // 准备multipart表单数据
        $multipart = [
            [
                'name' => 'file_name',
                'contents' => $file_name
            ],
            [
                'name' => 'parent_type',
                'contents' => $parent_type
            ],
            [
                'name' => 'parent_node',
                'contents' => $parent_node
            ],
            [
                'name' => 'size',
                'contents' => $file_size
            ],
            [
                'name' => 'file',
                'contents' => fopen($file_path, 'r'),
                'filename' => $file_name
            ]
        ];

        try {
            $client = new Client();
            $response = $client->request('POST', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token
                ],
                'multipart' => $multipart
            ]);
            
            $result = json_decode($response->getBody()->getContents(), true);
            return $this->resData($result);
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
            return false;
        }
    }

    /**
     * 上传图片
     * 文档：https://open.feishu.cn/document/server-docs/im-v1/image/create
     * @param string $type 图片类型，可选值：message 用于发送消息 | avatar 用于设置头像
     * @param string $path 图片文件路径
     * @param string $source 图片来源，默认 'local'，可选 'local' 或 'url'
     * @return array|bool
     * @throws \Exception
     */
    public function uploadImage($type, $path, $source = 'local')
    {
        $url = self::url_server . 'open-apis/im/v1/images';

        if ($source == 'local' && !file_exists($path)) {
            $this->error_msg = '图片不存在';
            return false;
        }

        // 准备multipart表单数据
        $contents = $source == 'local' ? fopen($path, 'r') : file_get_contents($path);
        $multipart = [
            [
                'name' => 'image_type',
                'contents' => $type
            ],
            [
                'name' => 'image',
                'contents' => $contents,
                'filename' => basename($path)
            ]
        ];

        try {
            $client = new Client();
            $response = $client->request('POST', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token
                ],
                'multipart' => $multipart
            ]);
            
            $result = json_decode($response->getBody()->getContents(), true);
            return $this->resData($result);
        } catch (\Exception $e) {
            $this->error_msg = $e->getMessage();
            return false;
        }
    }
}
