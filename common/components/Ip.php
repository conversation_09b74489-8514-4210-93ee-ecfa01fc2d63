<?php

namespace common\components;

use common\helpers\Tool;
use console\services\DealDataService;
use Yii;

/**
 * Class Ip
 * 
 * @package common\components
 */
class Ip
{
    public static function getIp138Status()
    {
        $token = Yii::$app->params['ip138']['token'];
        if (empty($token)) {
            Yii::error('ip138的token不存在');
            return false;
        }
        $url = "https://api.ip138.com/status/?token={$token}";
        $res = Tool::curlRequest($url, [], false);
        return $res;
    }

    /**
     * 接口文档：https://user.ip138.com/ip/doc/
     * 获取ip138的ip信息
     * @param string $ip
     * 
     * @return array
     */
    public static function getIpInfoByIp138($ip)
    {
        $token = Yii::$app->params['ip138']['token'];
        if (empty($token)) {
            Yii::error('ip138的token不存在');
            return false;
        }
        $url = "https://api.ip138.com/ipdata/?ip={$ip}&datatype=jsonp&token={$token}";
        $res = Tool::curlRequest($url);

        return $res;
    }

    /**
     * 百度地图开放平台-获取ip信息
     * @param string $ip
     * 
     * @return array
     */
    public static function getIpInfoByBaidu($ip)
    {
        $mapAk = DealDataService::getValidAk();
        // 选择剩余次数最多的AK
        $ak = $akKey = '';
        $maxCount = 0;
        $selectedAk = '';
        $redis = Yii::$app->redis;
        foreach ($mapAk as $currentAk) {
            $currentAkKey = $currentAk . date("Y-m-d");
            $currentCount = (int)$redis->get($currentAkKey);

            if ($currentCount > $maxCount) {
                $maxCount = $currentCount;
                $selectedAk = $currentAk;
                $akKey = $currentAkKey;
            }
        }

        if ($maxCount > 0) {
            $ak = $selectedAk;
        }

        if (empty($ak)) {
            Yii::error('百度地图ak值不存在');
            return false;
        }

        $ret = Tool::curlRequest("https://api.map.baidu.com/location/ip?ak={$ak}&ip={$ip}&coor=bd09ll");
        $redis->decr($akKey);
        if (empty($ret)) {
            Yii::$app->feishuNotice->text("百度地图ak值: {$akKey} 已失效");
        }

        return $ret;
    }
}
