<?php

namespace common\services\feishu;

use common\components\Feishu;
use common\models\backend\Member;
use common\services\BaseService;
use common\models\feishu\User;
use Exception;
use Yii;

class UserService extends BaseService
{
    /**
     * @var User
     */
    public static $modelClass = User::class;

    /**
     * 获取客服union_id
     */
    public static function getCusServiceUnionIdByID($user_id)
    {
        $chat_id = 'oc_bd2fc434ea037f42db3b14f9f26e7a24'; //【客服-门店信息对接群】的群id
        return static::getUnionIdByID($user_id, $chat_id);
    }

    /**
     * 获取推广union_id
     */
    public static function getPromoterUnionIdByID($user_id)
    {
        $chat_id = 'oc_c8665b7da4023c5266df8034a27a7d31'; //【广告运营群】的群id
        return static::getUnionIdByID($user_id, $chat_id);
    }

    public static function getUnionIdByID($user_id, $chat_id)
    {
        $info = Member::find()->select('username,current_feishu_app_id,feishu_unionid')->where(['id' => $user_id])->one();
        if ($info->current_feishu_app_id == 1) {
            return $info->feishu_unionid;
        }

        $key = $chat_id . '_001';
        $feishuClass = new Feishu();
        $chatMember = Yii::$app->cache->get($key);
        if (empty($chatMember)) {
            $chatMember = $feishuClass->getChatListOne($chat_id, 'union_id');
            if ($chatMember['code'] != 0) {
                throw new Exception('获取飞书群成员失败，原因：' . $chatMember['msg'] . ',请联系信息部门');
            }

            Yii::$app->cache->set($key, $chatMember, 86400);
        }

        foreach ($chatMember as $item) {
            if ($item['name'] == $info->username) {
                return $item['member_id'];
            }
        }

        $chatMember = $feishuClass->getChatListOne($chat_id, 'union_id');
        if ($chatMember['code'] != 0) {
            throw new Exception('获取飞书群成员失败，原因：' . $chatMember['msg'] . ',请联系信息部门');
        }

        Yii::$app->cache->set($key, $chatMember, 86400);

        foreach ($chatMember as $item) {
            if ($item['name'] == $info->username) {
                return $item['member_id'];
            }
        }

        throw new Exception('没有获取到该成员的union_id');
    }
}
