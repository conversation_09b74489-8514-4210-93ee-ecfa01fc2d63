<?php

namespace common\services\member;

use auth\models\feishu\App;
use backendapi\forms\MemberForm;
use common\cache\DepartmentCache;
use common\components\Feishu;
use common\enums\dingtalk\EmployeeStatusEnum;
use common\enums\MemberTypeEnum;
use common\enums\StatusEnum;
use common\models\backend\Member;
use common\models\common\Department;
use common\models\common\DepartmentAssignment;
use common\services\BaseService;
use common\models\feishu\User as FeishuUser;
use common\helpers\ArrayHelper;
use common\models\feishu\App as FeishuApp;
use Exception;
use Yii;
use common\models\member\FeishuUserAbnormalLog;

class FeishuUserService extends BaseService
{
    /**
     * @var FeishuUser
     */
    public static $modelClass = FeishuUser::class;

    /**
     * 获取飞书应用信息
     */
    public static function getFeishuAppInfo($appCode)
    {
        $info = App::find()->where(['code' => $appCode])->one();
        if (empty($info)) {
            throw new Exception("飞书应用code：{$appCode} 对应的飞书不存在,请联系管理员");
        }
        if (empty($info->entity_id)) {
            throw new Exception("飞书应用code：{$appCode} 对应的的entity_id不存在");
        }

        return $info;
    }
    /**
     * 通讯录用户增加
     */
    public static function userAddOrg($message)
    {
        $feishuAppInfo = static::getFeishuAppInfo($message['ComCode']);
        $userInfo = $message['event']['object'];

        static::userAddSave($userInfo, $feishuAppInfo, true);
    }

    public static function userAddSave($userInfo, $feishuAppInfo, $isAuto = false)
    {
        $feishuAppCode = $feishuAppInfo->code;
        $feishu_app_id = $feishuAppInfo->id;
        $entity_id = $feishuAppInfo->entity_id;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $logAbnorma = [
                'user_id' => $userInfo['user_id'],
                'department' => $userInfo['department_ids'],
                'position' => $userInfo['job_title']
            ];

            $feishuUser = FeishuUser::find()->where(['feishu_user_id' => $userInfo['user_id'], 'feishu_app_id' => $feishu_app_id])->one();
            if ($feishuUser) {
                $member = Member::find()->where(['id' => $feishuUser->member_id])->one();
                if ($member && $member->status == StatusEnum::DISABLED) {
                    $member->status = StatusEnum::ENABLED;
                    $member->save();
                }

                $feishuUser->status = StatusEnum::ENABLED;
                $feishuUser->employee_status = EmployeeStatusEnum::STATUS_THREE;
                $feishuUser->save();

                //记录异动记录
                if ($isAuto) {
                    self::logAbnormaSave('再次入职', $logAbnorma);
                }

                $transaction->commit();
                return true;
            }

            $feishu = new Feishu($feishuAppCode);
            $result = $feishu->getFeishuUserMsg($userInfo['user_id']);
            $result = $result['data'] ?? [];
            $result['head_portrait'] = $userInfo['avatar']['avatar_240'];
            $result['union_id'] = $userInfo['union_id'];
            $data = self::rosterData($result);

            self::setUserMsg($data, $feishu_app_id, $entity_id);
            self::logAbnormaSave('新人入职', $logAbnorma);

            if (!static::isExisteSameName($userInfo['name'], $entity_id)) {
                $feishuUser = FeishuUser::find()->where(['feishu_user_id' => $userInfo['user_id'], 'feishu_app_id' => $feishu_app_id])->one();
                //保存用户到backend_member表
                static::setMember($feishuUser);
            }

            $transaction->commit();
            Yii::$app->feishuNotice->barkMsg('飞书：' . $feishuAppInfo->name . ',' . $userInfo['name'] . ',小伙伴入职了');
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw new Exception($e->getMessage());
        }
    }

    public static function setMember(&$feishuUser)
    {
        if (empty($feishuUser)) {
            return false;
        }

        $member = Member::find()->where(['id' => $feishuUser->member_id])->one();
        if (empty($member)) {
            $member = new MemberForm();

            $member->realname = $member->username = $feishuUser->name;
            $member->mobile = $feishuUser->mobile;
            $member->email = $feishuUser->email;
            $member->jobnumber = $feishuUser->job_number;
            $member->head_portrait = $feishuUser->head_portrait;
            $member->feishu_unionid = $feishuUser->feishu_unionid;
            $member->feishu_userid = $feishuUser->feishu_user_id;
            $member->status = StatusEnum::ENABLED;
            $member->type = MemberTypeEnum::GENERAL_ADMIN;
            $member->current_entity_id = $feishuUser->entity_id;
            $member->current_feishu_app_id = $feishuUser->feishu_app_id;
            $member->gender = $feishuUser->gender;
            $member->position = $feishuUser->position;

            if (!$member->save()) throw new Exception('新增用户失败，请联系管理员' . current($member->getFirstErrors()));
            //默认部门分配
            $dept_ids = Department::find()->select('id')->where(['entity_id' => $feishuUser->entity_id, 'pid' => 0, 'deleted_at' => 0])->asArray()->one();
            if (!$dept_ids) throw new Exception('分配的部门不存在，请重试');

            $departmentAssignment = new DepartmentAssignment();
            $deptResult = $departmentAssignment->assign($dept_ids, $member->id);
        } else {
            $departmentAssignment = null;
        }

        if (empty($feishuUser->member_id)) {
            $feishuUser->member_id = $member->id;
            if (!$feishuUser->save()) throw new Exception('花名册保存用户ID失败,请重试');
        }

        if ($departmentAssignment) {
            $departmentAssignment->eventUserAssignDept($deptResult);
        }
        return true;
    }

    /**
     * 通讯录用户更改
     * @param $message
     */
    public static function userModifyOrg($message)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $feishuAppInfo = static::getFeishuAppInfo($message['ComCode']);
            $userInfo = $message['event']['object'];

            static::userModifyOrgSave($userInfo, $feishuAppInfo);
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw new Exception($e->getMessage());
        }

        return true;
    }

    public static function userModifyOrgSave($userInfo, $feishuAppInfo)
    {
        $feishu_app_id = $feishuAppInfo->id;
        $entity_id = $feishuAppInfo->entity_id;

        $feishuUser = FeishuUser::find()->where(['feishu_user_id' => $userInfo['user_id'], 'feishu_app_id' => $feishu_app_id, 'entity_id' => $entity_id])->one();
        if (empty($feishuUser)) return true;

        $feishuUser->name = ArrayHelper::getValue($userInfo, 'name', '');
        $feishuUser->mobile = static::formattingMobile($userInfo['mobile']);
        $feishuUser->email = ArrayHelper::getValue($userInfo, 'email', '');
        $feishuUser->job_number = ArrayHelper::getValue($userInfo, 'employee_no', '');
        $feishuUser->head_portrait = ArrayHelper::getValue($userInfo, 'avatar.avatar_240', '');
        $feishuUser->position = ArrayHelper::getValue($userInfo, 'job_title', '');
        $feishuUser->employee_type = ArrayHelper::getValue($userInfo, 'employee_type', 0);
        if (!$feishuUser->save()) {
            throw new Exception('用户修改信息,feishu表修改失败:' . current($feishuUser->getFirstErrors()));
        }

        if (empty($feishuUser->member_id)) return true;

        $memberCount = FeishuUser::find()->where(['member_id' => $feishuUser->member_id])->count();
        if ($memberCount > 1) {
            return true;
        }

        $member = Member::find()->where(['id' => $feishuUser->member_id])->one();
        if (empty($member)) {
            $error = $userInfo['name'] . '小伙伴修改信息,但系统backend_member表没有该用户信息';
            throw new Exception($error);
        }

        $member->username = ArrayHelper::getValue($userInfo, 'name', '');
        $member->realname = ArrayHelper::getValue($userInfo, 'name', '');
        $member->mobile = ArrayHelper::getValue($userInfo, 'mobile', '');
        $member->email = ArrayHelper::getValue($userInfo, 'email', '');
        $member->jobnumber = ArrayHelper::getValue($userInfo, 'employee_no', '');
        $member->head_portrait = ArrayHelper::getValue($userInfo, 'avatar.avatar_240', '');
        $member->position = ArrayHelper::getValue($userInfo, 'job_title', '');
        $member->save();
        if (!$member->save()) {
            throw new Exception('用户修改信息,backend_member保存失败:' . current($member->getFirstErrors()));
        }
    }

    public static function logAbnormaSave($remark, $userInfo)
    {
        $abnormaModel = new FeishuUserAbnormalLog();
        $abnormaModel->feishu_user_id = $userInfo['user_id'];
        $abnormaModel->dept_id = $userInfo['department'] ? implode(',', $userInfo['department']) : '';
        $abnormaModel->position = $userInfo['position'] ?? '';
        $abnormaModel->remark = $remark;
        $abnormaModel->save();
        if (!$abnormaModel->save()) throw new Exception('花名册异动表保存失败:' . current($abnormaModel->getFirstErrors()));
        return true;
    }

    /**
     * 员工离职
     */
    public static function userLeaveOrg($message)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $feishuAppInfo = static::getFeishuAppInfo($message['ComCode']);
            $userInfo = $message['event']['object'];
            $department_ids = $message['event']['old_object']['department_ids'];

            static::userLeaveOrgSave($userInfo, $feishuAppInfo, $department_ids);

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw new Exception($e->getMessage());
        }
        return true;
    }

    public static function userLeaveOrgSave($userInfo, $feishuAppInfo, $department_ids)
    {
        $feishu_app_id = $feishuAppInfo->id;
        $entity_id = $feishuAppInfo->entity_id;

        $feishuUser = FeishuUser::find()->where(['feishu_user_id' => $userInfo['user_id'], 'feishu_app_id' => $feishu_app_id, 'entity_id' => $entity_id])->one();
        if (empty($feishuUser)) return true;

        $feishuUser->status = StatusEnum::DISABLED;
        $feishuUser->employee_status = EmployeeStatusEnum::STATUS_FOUR;
        $feishuUser->save();

        $logAbnorma = [
            'user_id' => $userInfo['user_id'],
            'department' => $department_ids,
            'position' => $userInfo['job_title']
        ];

        //记录异动记录
        self::logAbnormaSave('员工离职', $logAbnorma);
        Yii::$app->feishuNotice->barkMsg('飞书：' . $feishuAppInfo->name . ',' . $feishuUser->name . ',小伙伴离职了');

        $memberCount = FeishuUser::find()->where(['member_id' => $feishuUser->member_id, 'status' => StatusEnum::ENABLED])->count();
        if ($memberCount > 0) {
            return true;
        }

        $member = Member::find()->where(['id' => $feishuUser->member_id])->one();
        if (empty($member)) {
            $error = $userInfo['name'] . '小伙伴离职修改状态,但系统backend_member表没有该用户信息';
            throw new Exception($error);
        }

        $member->status = StatusEnum::DISABLED;
        $member->save();
        // 存在负责部门
        $userDept = Department::find()
            ->where(['user_id' => $member->id])
            ->one();
        if ($userDept) {
            $userDept->user_id = 0;
            $userDept->save();
            DepartmentCache::init($userDept->id)->createOrUpdate();
        }

        MemberForm::setDeptPersonNum($member->id);
    }

    /**
     * 花名册数据拼接
     *
     * @param $data
     * @return array
     */
    public static function rosterData($data)
    {
        $insertData = [];
        foreach ($data['items'] as $val) {
            $system_fields = $val['system_fields'] ?? [];
            $custom_fields = $val['custom_fields'] ?? [];
            $insert = [];
            $insert['gender'] = $system_fields['gender'];
            $insert['real_name'] = $system_fields['name'];
            $insert['name'] = $system_fields['name'];
            $insert['dept_name'] = $system_fields['department_id'];
            $insert['mobile'] = static::formattingMobile($system_fields['mobile']);
            $insert['confirm_join_time'] = $system_fields['hire_date'];
            $insert['urgent_contacts_phone'] = $system_fields['primary_emergency_contact']['mobile'];
            $insert['urgent_contacts_name'] = $system_fields['primary_emergency_contact']['name'];
            $insert['employee_status'] = $system_fields['status'];
            $insert['employee_type'] = $$system_fields['type'];
            $insert['political_status'] = $system_fields['political_status'];
            $insert['feishu_dept_id'] = $system_fields['department_id'];
            $insert['cert_no'] = $system_fields['id_number'];
            $insert['cert_address'] = $system_fields['hukou_location'];
            $insert['cert_end_time'] = '';
            $insert['position'] = $system_fields['job']['name'];
            $insert['marriage'] = $system_fields['marital_status'];
            $insert['nation_type'] = $system_fields['ethnicity'];
            $insert['job_number'] = $system_fields['employee_no'];
            $insert['head_portrait'] = $data['head_portrait'] ?: '';
            $insert['email'] = $system_fields['email'];
            $insert['birthday'] = $system_fields['birthday'];

            if (!empty($insert)) {
                $insert['feishu_user_id'] = $val['user_id'] ?? 0;
                $insert['feishu_union_id'] = $data['union_id'] ?? '';
                $insertData[] = $insert;
            }
        }

        return $insertData;
    }

    /**
     * 公用写入飞书人员信息
     *
     * @param $insertUser
     * @return bool
     * @throws Exception
     */
    public static function setUserMsg($insertUser, $feishu_app_id, $entityId)
    {
        if (empty($insertUser)) {
            return true;
        }

        $feishuUser = new FeishuUser();
        foreach ($insertUser as $v) {
            $feishuUserId = $v['feishu_user_id'] ?? '';
            $confirmJoinTime = empty($v['confirm_join_time']) ? 0 : strtotime($v['confirm_join_time']);
            $political_status = static::getPoliticalStatusText($v['political_status']);
            $marriage = static::getMarriageText($v['marriage']);
            $feishuUser->attributes = [
                'dept_name' => $v['dept_name'] ?? '',
                'urgent_contacts_name' => $v['urgent_contacts_name'] ?? '',
                'urgent_contacts_phone' => $v['urgent_contacts_phone'] ?? '',
                'political_status' => $political_status,
                'cert_no' => $v['cert_no'] ?? '',
                'feishu_dept_id' => $v['feishu_dept_id'] ?? '',
                'cert_address' => $v['cert_address'] ?? '',
                'position' => $v['position'] ?? '',
                'name' => $v['name'] ?? '',
                'gender' => $v['gender'] ?? 0,
                'real_name' => $v['name'] ?? '', // 名称和真实姓名保持一致
                'email' => $v['email'],
                'marriage' => $marriage ?? '',
                'employee_type' => $v['employee_type'] ?? 0,
                'employee_status' => $v['employee_status'] ?? 0,
                'other_dept_ids' => $v['dept_ids'] ?? '',
                'cert_end_time' => $v['cert_end_time'] ?? '',
                'nation_type' => static::getNationTypeText($v['nation_type']),
                'job_number' => $v['job_number'] ?? '',
                'feishu_user_id' => $feishuUserId,
                'feishu_unionid' => $v['feishu_union_id'],
                'confirm_join_time' => $confirmJoinTime,
                'mobile' => $v['mobile'] ?? '',
                'head_portrait' => $v['head_portrait'] ?? '',
                'birthday' => $v['birthday'] ?? '',
                'entity_id' => $entityId,
                'feishu_app_id' => $feishu_app_id,
            ];
            if (!$feishuUser->save()) {
                throw new Exception($feishuUser->getFirstErrMsg());
            }
        }
        return true;
    }

    /**
     * 格式化手机号
     */
    public static function formattingMobile($mobile)
    {
        $mobile = str_ireplace('+86', '', $mobile);
        return $mobile;
    }

    /**
     * 获取政治面貌
     */
    public static function getPoliticalStatusText($political_status)
    {
        switch ($political_status) {
            case 1:
                $political_status_text = '中共党员';
                break;
            case 2:
                $political_status_text = '中国农工民主党';
                break;
            case 3:
                $political_status_text = '中国国民党革命委员会';
                break;
            case 4:
                $political_status_text = '中国民主促进会会员';
                break;
            case 5:
                $political_status_text = '中国民主同盟成员';
                break;
            case 6:
                $political_status_text = '中国民主建国会';
                break;
            case 7:
                $political_status_text = '中国致公党党员';
                break;
            case 8:
                $political_status_text = '九三学社社员';
                break;
            case 9:
                $political_status_text = '共青团员';
                break;
            case 10:
                $political_status_text = '其它党派成员';
                break;
            case 11:
                $political_status_text = '民主人士';
                break;
            case 12:
                $political_status_text = '群众';
                break;
            case 13:
                $political_status_text = '台湾民主自治同盟盟员';
                break;
            default:
                $political_status_text = '';
        }

        return $political_status_text;
    }

    /**
     * 获取婚姻状况
     */
    public static function getMarriageText($marriage)
    {
        switch ($marriage) {
            case 1:
                $text = '未婚';
                break;
            case 2:
                $text = '已婚';
                break;
            case 3:
                $text = '离异';
                break;
            case 4:
                $text = '其他';
                break;
            default:
                $text = '';
        }

        return $text;
    }

    public static function isExisteSameName($username, $entity_id)
    {
        $member = Member::find()->select('id')
            ->where(['username' => $username])
            ->andWhere(['current_entity_id' => $entity_id])
            ->limit(1)->one();
        return $member ? true : false;
    }

    public static function getNationTypeText($nation_type)
    {
        $ethnic_groups = [
            1 => '汉族',
            2 => '蒙古族',
            3 => '回族',
            4 => '藏族',
            5 => '维吾尔族',
            6 => '苗族',
            7 => '彝族',
            8 => '壮族',
            9 => '布依族',
            10 => '朝鲜族',
            11 => '满族',
            12 => '侗族',
            13 => '瑶族',
            14 => '白族',
            15 => '土家族',
            16 => '哈尼族',
            17 => '哈萨克族',
            18 => '傣族',
            19 => '黎族',
            20 => '傈僳族',
            21 => '佤族',
            22 => '畲族',
            23 => '高山族',
            24 => '拉祜族',
            25 => '水族',
            26 => '东乡族',
            27 => '纳西族',
            28 => '景颇族',
            29 => '阿昌族',
            30 => '柯尔克孜族',
            31 => '土族',
            32 => '达斡尔族',
            33 => '仫佬族',
            34 => '羌族',
            35 => '布朗族',
            36 => '撒拉族',
            37 => '毛南族',
            38 => '仡佬族',
            39 => '锡伯族',
            40 => '普米族',
            41 => '塔吉克族',
            42 => '怒族',
            43 => '乌孜别克族',
            44 => '俄罗斯族',
            45 => '鄂温克族',
            46 => '德昂族',
            47 => '保安族',
            48 => '裕固族',
            49 => '京族',
            50 => '塔塔尔族',
            51 => '独龙族',
            52 => '鄂伦春族',
            53 => '赫哲族',
            54 => '门巴族',
            55 => '珞巴族',
            56 => '基诺族',
            57 => '其他'
        ];

        return $ethnic_groups[$nation_type] ?: '';
    }

    public static function getMemberId($feishuUserId, $comCode = 'chz')
    {
        return self::$modelClass::find()
            ->select('member_id')
            ->leftJoin(['fa' => FeishuApp::tableName()], 'fa.id = feishu_app_id')
            ->where(['feishu_user_id' => $feishuUserId])
            ->andWhere(['fa.code' => $comCode])
            ->scalar();
    }
}
