<?php

namespace common\services\wxcom;

use common\components\Ip;
use common\helpers\ArrayHelper;
use common\models\wxcom\CusCustomerUser;
use Exception;
use Yii;

class CusCustomerUserService extends CusCustomerUser
{
    /**
     * 处理用户的ip
     * 
     * @param int $id
     * @return bool
     */
    public function dealIp()
    {
        if (empty($this->ip)) {
            return false;
        }

        if ($this->province_id || $this->city_id) {
            return false;
        }

        try {
            $ip = $this->ip;
            $ipInfo = Ip::getIpInfoByIp138($ip);
            if (empty($ipInfo)) {
                throw new Exception('获取ip信息失败:返回数据为空,表wxcom_cus_customer_user,id=' . $this->id);
            }

            if ($ipInfo['ret'] == 'err') {
                throw new Exception('获取ip信息失败:' . $ipInfo['msg'] . ',表wxcom_cus_customer_user,id=' . $this->id);
            }

            $data = ArrayHelper::getValue($ipInfo, 'data', []);
            if (empty($data)) {
                throw new Exception('获取ip信息失败:.' . json_encode($ipInfo, 256) . '.,表wxcom_cus_customer_user,id=' . $this->id);
            }

            $province = $data[1];
            $city = $data[2];
            if (empty($province)) {
                $province = '其他';
            }

            $provinceId = $this->getDistrictId($province);
            if (!$provinceId) {
                $provinceId = $this->getDistrictId('其他');
                $this->province_id = $provinceId;
                if (!$this->save(false)) {
                    throw new Exception('获取ip信息失败，表wxcom_cus_customer_user,id=' . $this->id . ',保存省份：' . $provinceId . '失败');
                }
            }

            $this->province_id = $provinceId;
            if (empty($city)) {
                if (!$this->save(false)) {
                    throw new Exception('获取ip信息失败，表wxcom_cus_customer_user,id=' . $this->id . ',保存省份：' . $provinceId . '失败');
                }
            }

            $cityId = $this->getDistrictId($city, $provinceId);
            $this->city_id = $cityId;
            if (!$this->save(false)) {
                throw new Exception('获取ip信息失败，表wxcom_cus_customer_user,id=' . $this->id . ',保存城市：' . $cityId . '失败');
            }

            $key = 'getIp138Status_' . date('Ymd');
            $count = Yii::$app->cache->get($key);
            if (empty($count)) {
                $ipStatus = Ip::getIp138Status();
                if ($ipStatus['ret'] == 'err') {
                    throw new Exception('获取ip138状态失败:' . $ipStatus['msg']);
                }
                $count = $ipStatus['data']['package'];

                if ($count < 30000) {
                    Yii::$app->feishuNotice->warning('ip138获取IP次数剩余不足30000次，当前剩余：' . $count);
                }
            }
            $count -= 1;
            Yii::$app->cache->set($key, $count, 86400);
        } catch (Exception $e) {
            Yii::error('处理用户的ip失败：' . $e->getMessage());
            Yii::$app->feishuNotice->error($e->getMessage());
            return false;
        }
    }
}
