<?php
/**
 * 测试退款数据合计行GROUP BY问题
 */

require_once __DIR__ . '/backendapi/config/bootstrap.php';
require_once __DIR__ . '/vendor/autoload.php';

$app = new yii\console\Application(require(__DIR__ . '/console/config/main.php'));

// 创建测试参数
$params = [
    'start_time' => strtotime('2024-01-01'),
    'end_time' => strtotime('2024-12-31 23:59:59'),
    'teacher_name' => '', // 不筛选老师
];

echo "=== 测试退款数据GROUP BY问题 ===\n\n";

// 获取原始的dataQuery
$query = \auth\services\data\TeacherAnalysisService::dataQuery($params);

echo "1. 执行原始查询（按teacher_id分组）:\n";
$originalData = $query->all();
foreach ($originalData as $row) {
    echo "老师ID: {$row['teacher_id']}, 老师姓名: {$row['teacher_name']}, 退款数据: {$row['refund_data']}\n";
}

echo "\n2. 执行groupBy('')合计查询:\n";
$sumQuery = clone $query;
$sumData = $sumQuery->groupBy('')->orderBy('')->one();
echo "合计退款数据: {$sumData['refund_data']}\n";

echo "\n3. 处理合计退款数据:\n";
$totalRefundAmount = '0';
if (!empty($sumData['refund_data'])) {
    $refundItems = explode(',', $sumData['refund_data']);
    echo "退款项目数量: " . count($refundItems) . "\n";
    foreach ($refundItems as $i => $refundItem) {
        echo "项目 {$i}: {$refundItem}\n";
        $parts = explode(':', $refundItem);
        if (count($parts) == 2) {
            $amount = $parts[1];
            $totalRefundAmount = \common\helpers\BcHelper::add($totalRefundAmount, $amount);
            echo "  订单ID: {$parts[0]}, 金额: {$amount}, 累计金额: {$totalRefundAmount}\n";
        } else {
            echo "  格式错误的退款项目: {$refundItem}\n";
        }
    }
}

echo "\n最终合计退款金额: {$totalRefundAmount}\n";

echo "\n4. 直接计算所有原始数据的退款金额（预期结果）:\n";
$expectedTotal = '0';
foreach ($originalData as $row) {
    if (!empty($row['refund_data'])) {
        $refundItems = explode(',', $row['refund_data']);
        foreach ($refundItems as $refundItem) {
            $parts = explode(':', $refundItem);
            if (count($parts) == 2) {
                $expectedTotal = \common\helpers\BcHelper::add($expectedTotal, $parts[1]);
            }
        }
    }
}
echo "预期合计退款金额: {$expectedTotal}\n";

echo "\n=== 测试完成 ===\n";