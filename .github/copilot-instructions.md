# GitHub Copilot Instructions

## Project Overview

This is a comprehensive ERP SaaS platform for Chinese education/training businesses built on **Yii2 framework (backend)** and **Vue.js multi-frontend architecture**. The system manages customer lifecycle, multi-platform advertising, order processing, and deep WeChat ecosystem integration.

## Essential Architecture Patterns

### Multi-Application Structure
- **`backendapi/`** - Main REST API using Yii2 controllers extending `BaseController`
- **`manageSystem/`** - Vue 2 + Ant Design Vue management interface (main UI)
- **`client/`** - Vue 3 + Vant UI customer-facing mobile app
- **`common/`** - Shared models, services, components using `BaseModel` with soft delete
- **`services/`** - Business logic layer with centralized DI container (`services/Application.php`)

### Critical Backend Patterns

**Service Layer Architecture:**
```
Controllers (backendapi/) → Services (services/) → Models (common/models/)
```

- Controllers extend `BaseController` with standardized CRUD (`actionIndex`, `actionCreate`)
- All models extend `BaseModel` with automatic `id`, `status`, `created_at`, `updated_at`, `deleted_at`
- Services accessed via `Yii::$app->services->serviceName` (centralized DI)

**Database Conventions:**
- Tables: snake_case with `erp_` prefix (`erp_user`, `erp_order_header`)
- Indexes: `pk_*` (primary), `uk_*` (unique), `idx_*` (regular)
- Always implement both `up()` and `down()` in migrations

**Queue System (Critical for Operations):**
```php
// Two-tier priority system
Yii::$app->queueHigh->push(new SomeJob(['param' => $value]));
Yii::$app->queue->push(new RegularJob(['data' => $data]));
```
- Jobs extend `BaseJob` with rate limiting and retry logic
- Essential for: data sync, notifications, reports, API calls

### Frontend Patterns (Vue 2 Management System)

**JeecgListMixin Requirements (Critical):**
```javascript
// ✅ Required - queryParam MUST be defined in data() with string values only
data() {
  return {
    queryParam: {
      name: '',        // ✅ String values only
      status: '1'      // ✅ Not arrays or objects
    },
    materialIds: []    // ✅ Handle arrays separately
  }
}
```

**JavaScript Compatibility (Vue 2 Build Limitations):**
```javascript
// ❌ Avoid - Not supported in this build environment
const value = data.bank_account?.account_no || ''  // Optional chaining
const result = someValue ?? defaultValue           // Nullish coalescing

// ✅ Use instead
const bankAccount = data.bank_account || {}
const value = bankAccount.account_no || ''
const result = (someValue !== null && someValue !== undefined) ? someValue : defaultValue
```

## API Response Format (Project-Specific)

**Critical: This project uses `code` not `status`:**
```php
// ✅ Correct API response format
return ['code' => 200, 'message' => 'success', 'data' => $result];
return ['code' => 400, 'message' => 'error message'];

// Frontend expects
if (res.code === 200) { /* success */ }
```

## Development Commands

```bash
# Backend (PHP/Yii2)
php init                           # Initialize environment (choose 0)
php composer install              # Install dependencies
php yii migrate                    # Run migrations
php yii queue/listen               # Start main queue worker (REQUIRED)
php yii que/listen                 # Alternative queue worker

# Frontend (manageSystem - Vue 2)
npm run mac-serve                  # Development (macOS)
npm run serve                     # Development (Windows)
npm run mac-build                 # Production (macOS)

# Frontend (client - Vue 3)
cd client && npm run serve        # Customer-facing app
```

## Parameter Type Safety (Common Issue)

When changing parameter types (string ↔ array), check ALL usage:
```php
// ❌ Will break when parameter becomes array
trim($params['material_id'])
strlen($params['material_id'])

// ✅ Safe conversion pattern
$materialId = isset($params['material_id']) ? $params['material_id'] : null;
if (is_string($materialId)) {
    $materialId = trim($materialId);
}
```

## Integration Points

- **WeChat Ecosystem**: Public accounts, mini-programs, enterprise WeChat
- **Collaboration**: Feishu (Lark), DingTalk with card-based interactions  
- **Payments**: Alipay, WeChat Pay, UnionPay
- **Ad Platforms**: TikTok/Bytedance, Tencent Ads, Kuaishou
- **Queue Workers**: Must be running for notifications, data sync, reports

## Testing & Quality

```bash
vendor/bin/codecept run                    # Run all tests
vendor/bin/codecept run frontend          # Frontend tests only
vendor/bin/codecept run backendapi        # API tests only
```

## Important Notes

- **Language**: Code comments and commit messages in Chinese
- **Queue Dependency**: System requires queue workers running for proper operation
- **Performance**: Heavy operations (reports, imports) must use queue system
- **Branch**: `erp_prod` is main branch for production deployments
