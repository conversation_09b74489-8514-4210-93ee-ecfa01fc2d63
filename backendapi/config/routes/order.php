<?php
return [
    // 订单类型管理
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order-type',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET select' => 'select',
            'POST create' => 'create',
            'POST set-status' => 'status',
            'POST update' => 'update-data',
        ],
    ],
    // 商品管理-视图
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'view/goods-union',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
        ],
    ],
    // 客户项目管理
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'customer-product',
        'pluralize' => false,
        'extraPatterns' => [
            'GET enable-list' => 'enable-list',
        ],
    ],
    // 团购平台管理
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'group-platform',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET select' => 'select',
            'POST create' => 'create',
            'POST set-status' => 'status',
            'POST update' => 'update-data',
        ],

    ],
    [   //赠品
        'class' => 'yii\rest\UrlRule',
        'controller' => ['gift'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'POST create' => 'create',
            'POST set-status' => 'status',
            'GET goods-list' => 'goods-list',
            'GET export' => 'export',
        ],
    ],
    [   //储值卡销售
        'class' => 'yii\rest\UrlRule',
        'controller' => ['customer_recharge/customer-recharge-card'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET card-list' => 'card-list',
            'GET index' => 'index',
            'GET view' => 'view',
            'GET view-for-print' => 'view-for-print',
            'POST set-order' => 'set-order',
            'POST extend-expire' => 'extend-expire',
            'POST pay-confirm' => 'pay-confirm',
            'GET  store-pay-record-list' => 'store-pay-record-list',
            'POST  store-pay-record-associated' => 'store-pay-record-associated',
            'POST useable-list' => 'useable-list',
        ],
    ],
    [   //老师
        'class' => 'yii\rest\UrlRule',
        'controller' => ['teacher-job'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET export' => 'export',
            'GET store-list' => 'store-list',
            'GET teacher-type' => 'teacher-type',
            'GET virtual-teacher-list' => 'virtual-teacher-list',
            'POST virtual-teacher-create' => 'virtual-teacher-create',
            'POST virtual-teacher-delete' => 'virtual-teacher-delete',
            'POST update' => 'update-data',
            'POST update-schedule' => 'update-schedule',
            'DELETE delete' => 'delete',
        ],
    ],
    [   //老师等级
        'class' => 'yii\rest\UrlRule',
        'controller' => ['teacher-level'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET select' => 'select',
            'POST create' => 'create',
            'POST update' => 'update-data',
            'POST set-status' => 'status',
        ],
    ],
    [   //老师类型
        'class' => 'yii\rest\UrlRule',
        'controller' => ['teacher-type'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET select' => 'select',
            'POST create' => 'create',
            'POST update' => 'update-data',
            'POST set-status' => 'status',
        ],
    ],
    [   //老师锁定档期
        'class' => 'yii\rest\UrlRule',
        'controller' => ['teacher-schedule-lock'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET schedule-list' => 'schedule-list',
            'POST update-schedule' => 'update-schedule',
        ],
    ],
    [   //订单管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order/order-header',
        'pluralize' => false,
        'extraPatterns' => [
            'GET store-order-list' => 'store-order-list',
            'GET reach-store' => 'reach-store',
            'POST other-create' => 'other-create',
            'GET cus-service-performance' => 'cus-service-performance',
            'POST store-retract' => 'store-retract',
        ],
    ],
    [   //订单管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order/pay',
        'pluralize' => false,
        'extraPatterns' => [
            'GET get-data-by-order-id' => 'get-data-by-order-id',
            'POST update-data' => 'update-data',                    //修改流水金额
            'DELETE delete' => 'soft-delete',                       //删除门店支付流水
        ],
    ],
    [   // 订单到店未做备注
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order/customer-churn-remark',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET export' => 'export',
            'POST create' => 'create',
            'POST cancel' => 'cancel',
            'POST store-real-remark' => 'store-real-remark',
        ],
    ],
    [   // 订单到店未做原因
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order/customer-churn-reason',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET select' => 'select',
            'POST create' => 'create',
            'POST update' => 'update-data',
            'POST set-status' => 'status',
        ],
    ],
    [   // 退款申请管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order/refund-application',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'POST create' => 'create',
            'POST update' => 'update-data',
            'GET refundable-orders' => 'refundable-orders',
            'GET status-select-list' => 'status-select-list',
            'GET export' => 'export',
        ],
    ]
];
