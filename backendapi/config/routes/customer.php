<?php

return [
    [
        // 基础例子
        'class' => 'yii\rest\UrlRule',
        'controller' => ['customer-introduced'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'GET view' => 'view',
                'GET export' => 'export',
                'POST store-bind' => 'store-bind',
            ],
    ],
    [
        // 客户手机号查看日志
        'class' => 'yii\rest\UrlRule',
        'controller' => ['customer/mobile-view-log'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'GET export' => 'export',
            ],
    ],
    [
        // 客资反馈
        'class' => 'yii\rest\UrlRule',
        'controller' => ['customer/feedback'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'GET export' => 'export',
            ],
    ],
];
