<?php

namespace backendapi\tests\unit\promote\transfermoneyv2;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\TransferMoneyBatchService;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use common\queues\TransferMoneyJobV2;
use common\queues\TransferMoneyJob;
use common\enums\reportEnum;
use Codeception\Test\Unit;
use Yii;
use Exception;

/**
 * 兼容性测试
 * 
 * 测试新架构与现有系统的兼容性，包括：
 * 1. 与现有 TransferMoneyBatchService 的接口兼容性
 * 2. 与现有 TransferMoneyJob 的队列兼容性
 * 3. 错误码体系兼容性测试
 * 4. 缓存键命名兼容性测试
 * 5. 数据格式兼容性测试
 * 6. API响应格式兼容性测试
 */
class CompatibilityTest extends Unit
{
    /**
     * @var TransferMoneyServiceV2 新服务
     */
    private $newService;

    /**
     * @var TransferMoneyBatchService 旧服务
     */
    private $oldService;

    /**
     * @var array 测试数据
     */
    private $testData;

    /**
     * 设置测试环境
     */
    protected function _before()
    {
        parent::_before();
        
        // 创建Mock缓存组件
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->method('get')->willReturn(false);
        $mockCache->method('set')->willReturn(true);
        $mockCache->method('delete')->willReturn(true);
        
        // 初始化新服务
        $platformFactory = new PlatformFactory();
        $cacheManager = new TransferCacheManager($mockCache);
        $this->newService = new TransferMoneyServiceV2($platformFactory, $cacheManager);
        
        // 初始化旧服务
        $this->oldService = new TransferMoneyBatchService();
        
        // 准备测试数据
        $this->prepareTestData();
    }

    /**
     * 清理测试环境
     */
    protected function _after()
    {
        parent::_after();
    }

    /**
     * 准备测试数据
     */
    private function prepareTestData()
    {
        $this->testData = [
            'valid_params' => [
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => "账户ID：1234567890\n转账金额：100"
            ],
            'batch_params' => [
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => "账户ID：1234567890、0987654321\n转账金额：100"
            ],
            'timed_params' => [
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => "账户ID：1234567890\n转账金额：100\n定时充值：" . date('Y-m-d H:i:s', strtotime('+1 hour'))
            ],
            'queue_data' => [
                'target_advertiser_ids' => ['1234567890'],
                'amount' => 100,
                'user_name' => '测试用户',
                'execute_time' => time(),
                'isTimeRecharge' => false
            ]
        ];
    }

    /**
     * 测试错误码体系兼容性
     */
    public function testErrorCodeCompatibility()
    {
        // 1. 验证成功码一致性
        $this->assertEquals(200, $this->newService->getSuccessCode());
        $this->assertEquals(200, $this->oldService->success_code);

        // 2. 验证定时充值码一致性
        $this->assertEquals(100, $this->newService->getTimeCode());
        // 注意：旧服务的time_code是私有属性，这里通过反射访问
        $reflection = new \ReflectionClass($this->oldService);
        $timeCodeProperty = $reflection->getProperty('time_code');
        $timeCodeProperty->setAccessible(true);
        $this->assertEquals(100, $timeCodeProperty->getValue($this->oldService));

        // 3. 验证余额不足成功码一致性
        $this->assertEquals(201, $this->newService->getSuccessInsufficientBalanceCode());
        $this->assertEquals(201, $this->oldService->success_insufficient_balance_code);

        // 4. 验证技术错误码一致性
        $this->assertEquals(422, $this->newService->getErrorCodeIt());
        $errorCodeItProperty = $reflection->getProperty('error_code_it');
        $errorCodeItProperty->setAccessible(true);
        $this->assertEquals(422, $errorCodeItProperty->getValue($this->oldService));

        // 5. 验证推广错误码一致性
        $this->assertEquals(423, $this->newService->getErrorCodePromote());
        $errorCodePromoteProperty = $reflection->getProperty('error_code_promote');
        $errorCodePromoteProperty->setAccessible(true);
        $this->assertEquals(423, $errorCodePromoteProperty->getValue($this->oldService));

        // 6. 验证余额不足错误码一致性
        $this->assertEquals(424, $this->newService->getErrorCodeInsufficientBalance());
        $errorCodeInsufficientBalanceProperty = $reflection->getProperty('error_code_insufficient_balance');
        $errorCodeInsufficientBalanceProperty->setAccessible(true);
        $this->assertEquals(424, $errorCodeInsufficientBalanceProperty->getValue($this->oldService));
    }

    /**
     * 测试核心方法接口兼容性
     */
    public function testCoreMethodCompatibility()
    {
        // 1. 验证run方法存在
        $this->assertTrue(method_exists($this->newService, 'run'));
        $this->assertTrue(method_exists($this->oldService, 'run'));

        // 2. 验证execute方法存在
        $this->assertTrue(method_exists($this->newService, 'execute'));
        $this->assertTrue(method_exists($this->oldService, 'execute'));

        // 3. 验证transferMoney方法存在
        $this->assertTrue(method_exists($this->newService, 'transferMoney'));
        $this->assertTrue(method_exists($this->oldService, 'transferMoney'));

        // 4. 验证getBalance方法存在
        $this->assertTrue(method_exists($this->newService, 'getBalance'));
        $this->assertTrue(method_exists($this->oldService, 'getBalance'));

        // 5. 验证getAccountBalance方法存在
        $this->assertTrue(method_exists($this->newService, 'getAccountBalance'));
        $this->assertTrue(method_exists($this->oldService, 'getAccountBalance'));

        // 6. 验证initialize方法存在
        $this->assertTrue(method_exists($this->newService, 'initialize'));
        $this->assertTrue(method_exists($this->oldService, 'initialize'));
    }

    /**
     * 测试参数处理兼容性
     */
    public function testParameterProcessingCompatibility()
    {
        $params = $this->testData['valid_params'];

        try {
            // 新服务参数处理
            $newResult = $this->newService->run($params);
            $newServiceWorked = true;
        } catch (Exception $e) {
            $newServiceWorked = false;
            $newError = $e->getMessage();
        }

        try {
            // 旧服务参数处理
            $oldResult = $this->oldService->run($params);
            $oldServiceWorked = true;
        } catch (Exception $e) {
            $oldServiceWorked = false;
            $oldError = $e->getMessage();
        }

        // 验证两个服务的行为一致性
        $this->assertEquals($oldServiceWorked, $newServiceWorked, 
            '新旧服务处理相同参数的结果应该一致');

        if (!$newServiceWorked && !$oldServiceWorked) {
            // 如果都失败，验证错误信息类似
            $this->assertNotEmpty($newError);
            $this->assertNotEmpty($oldError);
        }
    }

    /**
     * 测试队列任务兼容性
     */
    public function testQueueJobCompatibility()
    {
        $queueData = $this->testData['queue_data'];

        // 1. 创建新旧队列任务
        $newJob = new TransferMoneyJobV2(['data' => $queueData]);
        $oldJob = new TransferMoneyJob(['data' => $queueData]);

        // 2. 验证基本属性兼容性
        $this->assertEquals($oldJob->delay, $newJob->delay);
        $this->assertEquals($oldJob->retryTimes, $newJob->retryTimes);
        $this->assertEquals($oldJob->data, $newJob->data);
        $this->assertEquals($oldJob->isSendMessage, $newJob->isSendMessage);

        // 3. 验证核心方法存在
        $this->assertTrue(method_exists($newJob, 'run'));
        $this->assertTrue(method_exists($oldJob, 'run'));
        $this->assertTrue(method_exists($newJob, 'sendMsg'));
        $this->assertTrue(method_exists($oldJob, 'sendMsg'));
        $this->assertTrue(method_exists($newJob, 'getSendUserId'));
        $this->assertTrue(method_exists($oldJob, 'getSendUserId'));

        // 4. 验证静态方法兼容性
        $this->assertTrue(method_exists(TransferMoneyJobV2::class, 'addJob'));
        $this->assertTrue(method_exists(TransferMoneyJob::class, 'addJob'));
        $this->assertTrue(method_exists(TransferMoneyJobV2::class, 'addFansJob'));
        $this->assertTrue(method_exists(TransferMoneyJob::class, 'addFansJob'));
        $this->assertTrue(method_exists(TransferMoneyJobV2::class, 'checkTransferMoneyCount'));
        $this->assertTrue(method_exists(TransferMoneyJob::class, 'checkTransferMoneyCount'));
    }

    /**
     * 测试缓存键命名兼容性
     */
    public function testCacheKeyCompatibility()
    {
        $targetId = '1234567890';
        $advertiserId = '0987654321';

        // 1. 验证充值记录缓存键格式
        $expectedTransferKey = 'transferMoneyData:' . $targetId;
        $newTransferKey = 'transferMoneyData:' . $targetId;
        $this->assertEquals($expectedTransferKey, $newTransferKey);

        // 2. 验证余额缓存键格式
        $expectedBalanceKey = 'transferMoneyBalance:' . $advertiserId;
        $newBalanceKey = 'transferMoneyBalance:' . $advertiserId;
        $this->assertEquals($expectedBalanceKey, $newBalanceKey);

        // 3. 验证加粉充值计数键格式
        $expectedCountKey = 'AddFansTransferMoneyCount:' . $targetId;
        $newCountKey = 'AddFansTransferMoneyCount:' . $targetId;
        $this->assertEquals($expectedCountKey, $newCountKey);

        // 4. 验证加粉充值日限制键格式
        $today = date('Y-m-d');
        $expectedDayKey = 'AddFansTransferMoneyCount:' . $today;
        $newDayKey = 'AddFansTransferMoneyCount:' . $today;
        $this->assertEquals($expectedDayKey, $newDayKey);
    }

    /**
     * 测试数据格式兼容性
     */
    public function testDataFormatCompatibility()
    {
        // 1. 测试参数解析格式兼容性
        $testData = "账户ID：1234567890、0987654321\n转账金额：100\n定时充值：2024-01-01 10:00:00";
        
        try {
            // 新服务解析
            $newParsed = $this->newService->dealParams([
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => $testData
            ]);
            $newParseWorked = true;
        } catch (Exception $e) {
            $newParseWorked = false;
        }

        try {
            // 旧服务解析
            $oldParsed = $this->oldService->dealParams([
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => $testData
            ]);
            $oldParseWorked = true;
        } catch (Exception $e) {
            $oldParseWorked = false;
        }

        // 验证解析行为一致性
        $this->assertEquals($oldParseWorked, $newParseWorked);

        if ($newParseWorked && $oldParseWorked) {
            // 验证解析结果格式一致
            $this->assertArrayHasKey('账户ID', $newParsed);
            $this->assertArrayHasKey('转账金额', $newParsed);
            $this->assertArrayHasKey('账户ID', $oldParsed);
            $this->assertArrayHasKey('转账金额', $oldParsed);
        }
    }

    /**
     * 测试平台限额配置兼容性
     */
    public function testPlatformLimitCompatibility()
    {
        // 1. 验证抖音平台限额
        $tiktokSingleLimit = $this->newService->getPlatformFactory()
            ->create('tiktok')->getSingleLimit();
        
        // 获取旧服务的抖音限额
        $reflection = new \ReflectionClass($this->oldService);
        $tiktokSingleProperty = $reflection->getProperty('tiktok_single_recharge_amount');
        $tiktokSingleProperty->setAccessible(true);
        $oldTiktokSingle = $tiktokSingleProperty->getValue($this->oldService);
        
        $this->assertEquals($oldTiktokSingle, $tiktokSingleLimit);

        // 2. 验证ADQ平台限额
        $adqSingleLimit = $this->newService->getPlatformFactory()
            ->create('adq')->getSingleLimit();
            
        $adqSingleProperty = $reflection->getProperty('adq_single_recharge_amount');
        $adqSingleProperty->setAccessible(true);
        $oldAdqSingle = $adqSingleProperty->getValue($this->oldService);
        
        $this->assertEquals($oldAdqSingle, $adqSingleLimit);

        // 3. 验证抖音小时限额
        $tiktokHourlyLimit = $this->newService->getPlatformFactory()
            ->create('tiktok')->getHourlyLimit();
            
        $tiktokHourlyProperty = $reflection->getProperty('tiktok_one_hour_max_recharge_amount');
        $tiktokHourlyProperty->setAccessible(true);
        $oldTiktokHourly = $tiktokHourlyProperty->getValue($this->oldService);
        
        $this->assertEquals($oldTiktokHourly, $tiktokHourlyLimit);

        // 4. 验证ADQ小时限额
        $adqHourlyLimit = $this->newService->getPlatformFactory()
            ->create('adq')->getHourlyLimit();
            
        $adqHourlyProperty = $reflection->getProperty('adq_one_hour_max_recharge_amount');
        $adqHourlyProperty->setAccessible(true);
        $oldAdqHourly = $adqHourlyProperty->getValue($this->oldService);
        
        $this->assertEquals($oldAdqHourly, $adqHourlyLimit);
    }

    /**
     * 测试业务逻辑兼容性
     */
    public function testBusinessLogicCompatibility()
    {
        // 1. 测试时间限制逻辑兼容性
        $currentHour = (int)date('H');
        
        // 如果当前时间在限制范围内（2:00-6:30）
        if ($currentHour >= 2 && $currentHour <= 6) {
            try {
                $this->newService->timeLimit();
                $newTimeLimitPassed = true;
            } catch (Exception $e) {
                $newTimeLimitPassed = false;
                $newTimeError = $e->getMessage();
            }

            try {
                $this->oldService->timeLimit();
                $oldTimeLimitPassed = true;
            } catch (Exception $e) {
                $oldTimeLimitPassed = false;
                $oldTimeError = $e->getMessage();
            }

            // 验证时间限制行为一致
            $this->assertEquals($oldTimeLimitPassed, $newTimeLimitPassed);
            
            if (!$newTimeLimitPassed && !$oldTimeLimitPassed) {
                $this->assertContains('时间段不可充值', $newTimeError);
                $this->assertContains('时间段不可充值', $oldTimeError);
            }
        }

        // 2. 测试批量限制逻辑兼容性
        $largeBatchData = [
            'target_advertiser_ids' => array_fill(0, 60, '1234567890'), // 超过50个限制
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        try {
            $this->newService->execute($largeBatchData);
            $newBatchPassed = true;
        } catch (Exception $e) {
            $newBatchPassed = false;
            $newBatchError = $e->getMessage();
        }

        try {
            $this->oldService->execute($largeBatchData);
            $oldBatchPassed = true;
        } catch (Exception $e) {
            $oldBatchPassed = false;
            $oldBatchError = $e->getMessage();
        }

        // 验证批量限制行为一致
        $this->assertEquals($oldBatchPassed, $newBatchPassed);
        
        if (!$newBatchPassed && !$oldBatchPassed) {
            $this->assertContains('50个户', $newBatchError);
            $this->assertContains('50个户', $oldBatchError);
        }
    }

    /**
     * 测试响应格式兼容性
     */
    public function testResponseFormatCompatibility()
    {
        // 1. 测试成功响应格式
        $mockSuccessResult = [
            200 => [
                [
                    'msg' => '充值成功',
                    'target_advertiser_id' => '1234567890',
                    'target_advertiser_name' => '测试账户',
                    'main_body' => '测试主体',
                    'advertiser_id' => '0987654321',
                    'insufficientNalance' => 4900
                ]
            ]
        ];

        // 新服务格式化结果
        $newFormatted = $this->newService->resRealData($mockSuccessResult);
        
        // 旧服务格式化结果
        $oldFormatted = $this->oldService->resRealData($mockSuccessResult);

        // 验证格式化结果结构一致
        $this->assertArrayHasKey(200, $newFormatted);
        $this->assertArrayHasKey(200, $oldFormatted);
        $this->assertEquals($oldFormatted[200]['code'], $newFormatted[200]['code']);

        // 2. 测试错误响应格式
        $mockErrorResult = [
            422 => [
                [
                    'msg' => '充值失败',
                    'target_advertiser_id' => '1234567890',
                    'target_advertiser_name' => '测试账户'
                ]
            ]
        ];

        $newErrorFormatted = $this->newService->resRealData($mockErrorResult);
        $oldErrorFormatted = $this->oldService->resRealData($mockErrorResult);

        $this->assertArrayHasKey(422, $newErrorFormatted);
        $this->assertArrayHasKey(422, $oldErrorFormatted);
        $this->assertEquals($oldErrorFormatted[422]['code'], $newErrorFormatted[422]['code']);
    }

    /**
     * 测试加粉充值兼容性
     */
    public function testAddFansRechargeCompatibility()
    {
        $subAdvertiserId = '1234567890';

        // 1. 测试加粉充值任务添加
        try {
            $newAddFansResult = TransferMoneyJobV2::addFansJob($subAdvertiserId);
            $newAddFansWorked = true;
        } catch (Exception $e) {
            $newAddFansWorked = false;
        }

        try {
            $oldAddFansResult = TransferMoneyJob::addFansJob($subAdvertiserId);
            $oldAddFansWorked = true;
        } catch (Exception $e) {
            $oldAddFansWorked = false;
        }

        // 验证加粉充值行为一致性
        $this->assertEquals($oldAddFansWorked, $newAddFansWorked);

        // 2. 测试充值频次检查
        try {
            $newCountResult = TransferMoneyJobV2::checkTransferMoneyCount($subAdvertiserId);
            $newCountWorked = true;
        } catch (Exception $e) {
            $newCountWorked = false;
        }

        try {
            $oldCountResult = TransferMoneyJob::checkTransferMoneyCount($subAdvertiserId);
            $oldCountWorked = true;
        } catch (Exception $e) {
            $oldCountWorked = false;
        }

        // 验证频次检查行为一致性
        $this->assertEquals($oldCountWorked, $newCountWorked);
        
        if ($newCountWorked && $oldCountWorked) {
            $this->assertEquals($oldCountResult, $newCountResult);
        }
    }

    /**
     * 测试配置兼容性
     */
    public function testConfigurationCompatibility()
    {
        // 1. 验证默认充值金额兼容性
        $newDefaultAmount = 50; // 新系统默认值
        $oldDefaultAmount = 50; // 旧系统默认值
        $this->assertEquals($oldDefaultAmount, $newDefaultAmount);

        // 2. 验证频次限制兼容性
        $newMaxAttempts = 5; // 新系统默认值
        $oldMaxAttempts = 5; // 旧系统默认值
        $this->assertEquals($oldMaxAttempts, $newMaxAttempts);

        // 3. 验证缓存过期时间兼容性
        $newCacheTime = 300; // 5分钟
        $oldCacheTime = 5 * 60; // 5分钟
        $this->assertEquals($oldCacheTime, $newCacheTime);

        // 4. 验证余额缓存时间兼容性
        $newBalanceCacheTime = 500; // 500秒
        $oldBalanceCacheTime = 500; // 500秒
        $this->assertEquals($oldBalanceCacheTime, $newBalanceCacheTime);
    }

    /**
     * 测试数据库操作兼容性
     */
    public function testDatabaseOperationCompatibility()
    {
        // 由于涉及数据库操作，这里主要验证SQL查询的兼容性
        // 实际测试中需要Mock数据库连接

        // 1. 验证账户查询兼容性
        $this->assertTrue(method_exists($this->newService, 'setTargetAdvertiserIds'));
        $this->assertTrue(method_exists($this->oldService, 'setTargetAdvertiserIds'));

        // 2. 验证平台查询兼容性
        $this->assertTrue(method_exists($this->newService, 'setPlatform'));
        $this->assertTrue(method_exists($this->oldService, 'setPlatform'));

        // 3. 验证Token设置兼容性
        $this->assertTrue(method_exists($this->newService, 'setToken'));
        $this->assertTrue(method_exists($this->oldService, 'setToken'));
    }

    /**
     * 测试向后兼容性保证
     */
    public function testBackwardCompatibilityGuarantee()
    {
        // 1. 验证新服务可以处理旧格式的参数
        $oldFormatParams = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：1234567890\n转账金额：100"
        ];

        try {
            $result = $this->newService->run($oldFormatParams);
            $this->assertTrue(true); // 如果没有异常，说明兼容
        } catch (Exception $e) {
            // 验证异常信息是业务逻辑异常，而不是格式不兼容异常
            $this->assertNotContains('格式不兼容', $e->getMessage());
            $this->assertNotContains('参数格式错误', $e->getMessage());
        }

        // 2. 验证新队列任务可以处理旧格式的数据
        $oldQueueData = [
            'target_advertiser_ids' => ['1234567890'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        $newJob = new TransferMoneyJobV2(['data' => $oldQueueData]);
        $this->assertEquals($oldQueueData, $newJob->data);

        // 3. 验证错误码保持不变
        $this->assertEquals(422, $this->newService->getCode()); // 默认错误码
        $this->assertEquals(422, $this->oldService->code); // 默认错误码
    }
}