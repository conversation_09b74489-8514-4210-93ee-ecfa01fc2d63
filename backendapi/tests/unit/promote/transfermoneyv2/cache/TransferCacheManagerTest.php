<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\cache;

use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * 充值缓存管理器测试用例
 * 
 * 测试充值缓存管理器的各种功能和边界情况
 * 基于现有TransferMoneyBatchService::amountlimit()和success()方法逻辑
 */
class TransferCacheManagerTest extends TestCase
{
    /**
     * @var TransferCacheManager
     */
    private $cacheManager;

    /**
     * @var MockObject 模拟缓存组件
     */
    private $mockCache;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建模拟缓存组件
        $this->mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        
        // 创建缓存管理器实例
        $this->cacheManager = new TransferCacheManager($this->mockCache);
    }

    /**
     * 测试小时限额检查 - 无缓存数据
     */
    public function testCheckHourlyLimitWithNoCache()
    {
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn(false);

        $result = $this->cacheManager->checkHourlyLimit('*********', 500, 1000);
        $this->assertTrue($result);
    }

    /**
     * 测试小时限额检查 - 通过检查
     */
    public function testCheckHourlyLimitPass()
    {
        $currentTime = time();
        $cacheData = [
            [
                'user_name' => 'test_user',
                'amount' => 300,
                'time' => $currentTime - 1800 // 30分钟前
            ]
        ];

        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn($cacheData);

        $result = $this->cacheManager->checkHourlyLimit('*********', 500, 1000);
        $this->assertTrue($result);
    }

    /**
     * 测试小时限额检查 - 超过限额
     */
    public function testCheckHourlyLimitExceed()
    {
        $currentTime = time();
        $cacheData = [
            [
                'user_name' => 'test_user',
                'amount' => 800,
                'time' => $currentTime - 1800 // 30分钟前
            ]
        ];

        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn($cacheData);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('1小时内限制充值金额不能超过1000，账户*********已经充值了800');

        $this->cacheManager->checkHourlyLimit('*********', 500, 1000);
    }

    /**
     * 测试小时限额检查 - 过期数据清理
     */
    public function testCheckHourlyLimitWithExpiredData()
    {
        $currentTime = time();
        $cacheData = [
            [
                'user_name' => 'old_user',
                'amount' => 500,
                'time' => $currentTime - 7200 // 2小时前，应该被清理
            ],
            [
                'user_name' => 'recent_user',
                'amount' => 300,
                'time' => $currentTime - 1800 // 30分钟前
            ]
        ];

        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn($cacheData);

        // 期望更新缓存，移除过期数据
        $this->mockCache->expects($this->once())
                       ->method('set')
                       ->with(
                           'transferMoneyData:*********',
                           $this->callback(function($data) {
                               return count($data) === 1 && $data[0]['user_name'] === 'recent_user';
                           }),
                           3600
                       );

        $result = $this->cacheManager->checkHourlyLimit('*********', 500, 1000);
        $this->assertTrue($result);
    }

    /**
     * 测试记录成功充值
     */
    public function testRecordSuccessfulTransfer()
    {
        $existingData = [
            [
                'user_name' => 'existing_user',
                'amount' => 300,
                'time' => time() - 1800
            ]
        ];

        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn($existingData);

        $this->mockCache->expects($this->once())
                       ->method('set')
                       ->with(
                           'transferMoneyData:*********',
                           $this->callback(function($data) {
                               return count($data) === 2 && 
                                      $data[1]['user_name'] === 'test_user' &&
                                      $data[1]['amount'] === 500;
                           }),
                           3600
                       )
                       ->willReturn(true);

        $result = $this->cacheManager->recordSuccessfulTransfer('*********', 500, 'test_user');
        $this->assertTrue($result);
    }

    /**
     * 测试记录成功充值 - 无现有数据
     */
    public function testRecordSuccessfulTransferWithNoExistingData()
    {
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn(false);

        $this->mockCache->expects($this->once())
                       ->method('set')
                       ->with(
                           'transferMoneyData:*********',
                           $this->callback(function($data) {
                               return count($data) === 1 && 
                                      $data[0]['user_name'] === 'test_user' &&
                                      $data[0]['amount'] === 500;
                           }),
                           3600
                       )
                       ->willReturn(true);

        $result = $this->cacheManager->recordSuccessfulTransfer('*********', 500, 'test_user');
        $this->assertTrue($result);
    }

    /**
     * 测试获取账户余额
     */
    public function testGetBalance()
    {
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyBalance:*********')
                       ->willReturn(1500.50);

        $balance = $this->cacheManager->getBalance('*********');
        $this->assertEquals(1500.50, $balance);
    }

    /**
     * 测试获取账户余额 - 缓存不存在
     */
    public function testGetBalanceNotExists()
    {
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyBalance:*********')
                       ->willReturn(false);

        $balance = $this->cacheManager->getBalance('*********');
        $this->assertNull($balance);
    }

    /**
     * 测试设置账户余额
     */
    public function testSetBalance()
    {
        $this->mockCache->expects($this->once())
                       ->method('set')
                       ->with('transferMoneyBalance:*********', 1500.50, 500)
                       ->willReturn(true);

        $result = $this->cacheManager->setBalance('*********', 1500.50);
        $this->assertTrue($result);
    }

    /**
     * 测试删除账户余额
     */
    public function testDeleteBalance()
    {
        $this->mockCache->expects($this->once())
                       ->method('delete')
                       ->with('transferMoneyBalance:*********')
                       ->willReturn(true);

        $result = $this->cacheManager->deleteBalance('*********');
        $this->assertTrue($result);
    }

    /**
     * 测试获取加粉充值计数
     */
    public function testGetAddFansTransferCount()
    {
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:*********')
                       ->willReturn(5);

        $count = $this->cacheManager->getAddFansTransferCount('*********');
        $this->assertEquals(5, $count);
    }

    /**
     * 测试获取加粉充值计数 - 缓存不存在
     */
    public function testGetAddFansTransferCountNotExists()
    {
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:*********')
                       ->willReturn(false);

        $count = $this->cacheManager->getAddFansTransferCount('*********');
        $this->assertEquals(0, $count);
    }

    /**
     * 测试增加加粉充值计数
     */
    public function testIncrementAddFansTransferCount()
    {
        // 先获取当前计数
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:*********')
                       ->willReturn(3);

        // 然后设置新计数
        $this->mockCache->expects($this->once())
                       ->method('set')
                       ->with('AddFansTransferMoneyCount:*********', 5, 300)
                       ->willReturn(true);

        $newCount = $this->cacheManager->incrementAddFansTransferCount('*********', 2);
        $this->assertEquals(5, $newCount);
    }

    /**
     * 测试获取当天已限制的加粉充值账户列表
     */
    public function testGetAddFansRestrictedAccounts()
    {
        $accounts = ['*********', '*********'];
        
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:2025-01-23')
                       ->willReturn($accounts);

        $result = $this->cacheManager->getAddFansRestrictedAccounts('2025-01-23');
        $this->assertEquals($accounts, $result);
    }

    /**
     * 测试添加账户到当天加粉充值限制列表
     */
    public function testAddAddFansRestrictedAccount()
    {
        $existingAccounts = ['*********'];
        
        // 先获取现有列表
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:2025-01-23')
                       ->willReturn($existingAccounts);

        // 然后设置新列表
        $this->mockCache->expects($this->once())
                       ->method('set')
                       ->with(
                           'AddFansTransferMoneyCount:2025-01-23',
                           ['*********', '*********'],
                           86400
                       )
                       ->willReturn(true);

        $result = $this->cacheManager->addAddFansRestrictedAccount('2025-01-23', '*********');
        $this->assertTrue($result);
    }

    /**
     * 测试添加已存在的账户到限制列表
     */
    public function testAddAddFansRestrictedAccountAlreadyExists()
    {
        $existingAccounts = ['*********', '*********'];
        
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:2025-01-23')
                       ->willReturn($existingAccounts);

        // 不应该调用set方法
        $this->mockCache->expects($this->never())
                       ->method('set');

        $result = $this->cacheManager->addAddFansRestrictedAccount('2025-01-23', '*********');
        $this->assertTrue($result);
    }

    /**
     * 测试检查账户是否在当天加粉充值限制列表中
     */
    public function testIsAddFansRestricted()
    {
        $accounts = ['*********', '*********'];
        
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:2025-01-23')
                       ->willReturn($accounts);

        $result = $this->cacheManager->isAddFansRestricted('2025-01-23', '*********');
        $this->assertTrue($result);

        // 测试不在列表中的账户
        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('AddFansTransferMoneyCount:2025-01-23')
                       ->willReturn($accounts);

        $result = $this->cacheManager->isAddFansRestricted('2025-01-23', '*********');
        $this->assertFalse($result);
    }

    /**
     * 测试获取充值历史记录
     */
    public function testGetTransferHistory()
    {
        $currentTime = time();
        $cacheData = [
            [
                'user_name' => 'old_user',
                'amount' => 500,
                'time' => $currentTime - 7200 // 2小时前
            ],
            [
                'user_name' => 'recent_user',
                'amount' => 300,
                'time' => $currentTime - 1800 // 30分钟前
            ]
        ];

        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn($cacheData);

        $history = $this->cacheManager->getTransferHistory('*********', 1);
        
        $this->assertCount(1, $history);
        $this->assertEquals('recent_user', $history[0]['user_name']);
        $this->assertEquals(300, $history[0]['amount']);
        $this->assertArrayHasKey('formatted_time', $history[0]);
    }

    /**
     * 测试获取充值总额
     */
    public function testGetTotalTransferAmount()
    {
        $currentTime = time();
        $cacheData = [
            [
                'user_name' => 'user1',
                'amount' => 300,
                'time' => $currentTime - 1800 // 30分钟前
            ],
            [
                'user_name' => 'user2',
                'amount' => 200,
                'time' => $currentTime - 900 // 15分钟前
            ]
        ];

        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn($cacheData);

        $total = $this->cacheManager->getTotalTransferAmount('*********', 1);
        $this->assertEquals(500, $total);
    }

    /**
     * 测试清理过期充值记录
     */
    public function testCleanExpiredTransferRecords()
    {
        $currentTime = time();
        $cacheData = [
            [
                'user_name' => 'old_user',
                'amount' => 500,
                'time' => $currentTime - 7200 // 2小时前，应该被清理
            ],
            [
                'user_name' => 'recent_user',
                'amount' => 300,
                'time' => $currentTime - 1800 // 30分钟前
            ]
        ];

        $this->mockCache->expects($this->once())
                       ->method('get')
                       ->with('transferMoneyData:*********')
                       ->willReturn($cacheData);

        $this->mockCache->expects($this->once())
                       ->method('set')
                       ->with(
                           'transferMoneyData:*********',
                           $this->callback(function($data) {
                               return count($data) === 1 && $data[0]['user_name'] === 'recent_user';
                           }),
                           3600
                       )
                       ->willReturn(true);

        $result = $this->cacheManager->cleanExpiredTransferRecords('*********');
        $this->assertTrue($result);
    }

    /**
     * 测试批量清理过期记录
     */
    public function testBatchCleanExpiredRecords()
    {
        $accountIds = ['*********', '*********'];

        // 模拟两次get调用
        $this->mockCache->expects($this->exactly(2))
                       ->method('get')
                       ->willReturn([]);

        $results = $this->cacheManager->batchCleanExpiredRecords($accountIds);
        
        $this->assertCount(2, $results);
        $this->assertArrayHasKey('*********', $results);
        $this->assertArrayHasKey('*********', $results);
    }

    /**
     * 测试获取缓存统计信息
     */
    public function testGetCacheStats()
    {
        $stats = $this->cacheManager->getCacheStats();
        
        $this->assertArrayHasKey('cache_component', $stats);
        $this->assertArrayHasKey('key_prefixes', $stats);
        $this->assertArrayHasKey('expire_times', $stats);
        
        $this->assertArrayHasKey('transfer_data', $stats['key_prefixes']);
        $this->assertArrayHasKey('balance', $stats['key_prefixes']);
        $this->assertArrayHasKey('add_fans_count', $stats['key_prefixes']);
        $this->assertArrayHasKey('add_fans_date', $stats['key_prefixes']);
    }
}