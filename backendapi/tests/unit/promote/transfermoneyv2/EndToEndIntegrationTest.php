<?php

namespace backendapi\tests\unit\promote\transfermoneyv2;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\platform\MockAdapter;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use backendapi\services\promote\transfermoneyv2\workflow\AutoRechargeWorkflow;
use backendapi\services\promote\transfermoneyv2\workflow\QueueExecutionWorkflow;
use backendapi\services\promote\transfermoneyv2\workflow\AddFansRechargeWorkflow;
use backendapi\services\promote\transfermoneyv2\config\ConfigManager;
use common\queues\TransferMoneyJobV2;
use common\enums\reportEnum;
use Codeception\Test\Unit;
use Yii;
use Exception;

/**
 * 端到端集成测试套件
 * 
 * 验证整个新架构的完整功能，包括：
 * 1. 完整的充值流程测试（正常充值、批量充值、定时充值、加粉充值）
 * 2. 多平台混合充值测试
 * 3. 错误场景和异常处理测试
 * 4. 缓存机制和数据一致性测试
 * 5. 工作流程协调测试
 * 6. 队列任务处理测试
 */
class EndToEndIntegrationTest extends Unit
{
    /**
     * @var TransferMoneyServiceV2
     */
    private $service;

    /**
     * @var PlatformFactory
     */
    private $platformFactory;

    /**
     * @var TransferCacheManager
     */
    private $cacheManager;

    /**
     * @var array 测试数据
     */
    private $testData;

    /**
     * 设置测试环境
     */
    protected function _before()
    {
        parent::_before();
        
        // 创建Mock缓存组件
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->method('get')->willReturn(false);
        $mockCache->method('set')->willReturn(true);
        $mockCache->method('delete')->willReturn(true);
        
        // 初始化组件
        $this->platformFactory = new PlatformFactory();
        $this->cacheManager = new TransferCacheManager($mockCache);
        
        // 创建服务实例
        $this->service = new TransferMoneyServiceV2(
            $this->platformFactory,
            $this->cacheManager
        );

        // 准备测试数据
        $this->prepareTestData();
    }

    /**
     * 清理测试环境
     */
    protected function _after()
    {
        parent::_after();
        $this->platformFactory->reset();
        ConfigManager::clearCache();
    }

    /**
     * 准备测试数据
     */
    private function prepareTestData()
    {
        $this->testData = [
            'single_account' => [
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => "账户ID：**********\n转账金额：100"
            ],
            'batch_accounts' => [
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => "账户ID：**********、**********、**********\n转账金额：100"
            ],
            'timed_recharge' => [
                'user_id' => 1,
                'user_name' => '测试用户',
                'data' => "账户ID：**********\n转账金额：100\n定时充值：" . date('Y-m-d H:i:s', strtotime('+1 hour'))
            ],
            'mixed_platform' => [
                'tiktok_accounts' => ['**********', '**********'],
                'adq_accounts' => ['**********', '**********'],
                'amount' => 100
            ]
        ];
    }

    /**
     * 测试完整的正常充值流程
     */
    public function testCompleteNormalRechargeFlow()
    {
        // 1. 自动充值入队流程
        $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->service);
        $autoResult = $autoRechargeWorkflow->execute($this->testData['single_account']);

        // 验证自动充值结果
        $this->assertTrue($autoResult['success']);
        $this->assertEquals(200, $autoResult['code']);
        $this->assertNotEmpty($autoResult['execution_steps']);
        $this->assertArrayHasKey('result', $autoResult);

        // 2. 模拟队列数据处理
        $queueData = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        // 3. 队列执行流程
        $queueExecutionWorkflow = QueueExecutionWorkflow::createWithService($this->service);
        $queueResult = $queueExecutionWorkflow->execute($queueData);

        // 验证队列执行结果
        $this->assertTrue(is_array($queueResult));
        $this->assertArrayHasKey('execution_results', $queueResult);
        $this->assertArrayHasKey('statistics', $queueResult);
        $this->assertArrayHasKey('formatted_results', $queueResult);
        $this->assertEquals(1, $queueResult['statistics']['total_accounts']);

        // 4. 验证执行统计
        $stats = $queueExecutionWorkflow->getExecutionStats();
        $this->assertArrayHasKey('total_accounts', $stats);
        $this->assertArrayHasKey('success_accounts', $stats);
        $this->assertArrayHasKey('total_duration', $stats);
        $this->assertGreaterThan(0, $stats['total_duration']);
    }

    /**
     * 测试批量充值完整流程
     */
    public function testCompleteBatchRechargeFlow()
    {
        // 1. 批量充值入队
        $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->service);
        $batchResult = $autoRechargeWorkflow->execute($this->testData['batch_accounts']);

        // 验证批量入队结果
        $this->assertTrue($batchResult['success']);
        $this->assertEquals(200, $batchResult['code']);

        // 2. 批量队列执行
        $queueData = [
            'target_advertiser_ids' => ['**********', '**********', '**********'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        $queueExecutionWorkflow = QueueExecutionWorkflow::createWithService($this->service);
        $queueResult = $queueExecutionWorkflow->execute($queueData);

        // 验证批量执行结果
        $this->assertEquals(3, $queueResult['statistics']['total_accounts']);
        $this->assertArrayHasKey('execution_details', $queueResult['statistics']);
        $this->assertCount(3, $queueResult['statistics']['execution_details']);

        // 3. 验证平台分布统计
        $platformDistribution = $queueExecutionWorkflow->getPlatformDistribution();
        $this->assertTrue(is_array($platformDistribution));
        $this->assertArrayHasKey(reportEnum::ADQ, $platformDistribution);
    }

    /**
     * 测试定时充值完整流程
     */
    public function testCompleteTimedRechargeFlow()
    {
        // 1. 定时充值入队
        $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->service);
        $timedResult = $autoRechargeWorkflow->execute($this->testData['timed_recharge']);

        // 验证定时充值结果
        $this->assertTrue($timedResult['success']);
        $this->assertEquals(100, $timedResult['code']); // 定时充值码
        $this->assertEquals('定时充值操作成功', $timedResult['result']);

        // 2. 验证工作流状态
        $workflowStatus = $autoRechargeWorkflow->getWorkflowStatus();
        $this->assertArrayHasKey('is_time_recharge', $workflowStatus);
        $this->assertTrue($workflowStatus['is_time_recharge']);

        // 3. 验证执行步骤记录
        $executionSteps = $timedResult['execution_steps'];
        $this->assertNotEmpty($executionSteps);
        $this->assertContains('时间验证', array_column($executionSteps, 'step'));
        $this->assertContains('定时充值处理', array_column($executionSteps, 'step'));
    }

    /**
     * 测试加粉充值完整流程
     */
    public function testCompleteAddFansRechargeFlow()
    {
        $subAdvertiserId = '**********';
        $context = [
            'add_time' => time(),
            'user_info' => ['id' => 1, 'name' => '测试用户']
        ];

        // 1. 加粉充值工作流
        $addFansWorkflow = AddFansRechargeWorkflow::createWithService($this->service);
        
        // 2. 检查充值资格
        $eligibilityResult = $addFansWorkflow->checkRechargeEligibility($subAdvertiserId);
        $this->assertArrayHasKey('eligible', $eligibilityResult);
        $this->assertArrayHasKey('checks', $eligibilityResult);
        $this->assertArrayHasKey('basic', $eligibilityResult['checks']);
        $this->assertArrayHasKey('config', $eligibilityResult['checks']);
        $this->assertArrayHasKey('frequency', $eligibilityResult['checks']);

        // 3. 执行加粉充值（如果符合条件）
        try {
            $addFansResult = $addFansWorkflow->execute($subAdvertiserId, $context);
            
            if ($addFansResult['success']) {
                $this->assertTrue($addFansResult['success']);
                $this->assertArrayHasKey('execution_log', $addFansResult);
                $this->assertNotEmpty($addFansResult['execution_log']['steps']);
            } else {
                // 验证失败原因记录
                $this->assertArrayHasKey('error', $addFansResult);
                $this->assertArrayHasKey('execution_log', $addFansResult);
            }
        } catch (Exception $e) {
            // 某些依赖可能不存在，这是正常的
            $this->assertInstanceOf(Exception::class, $e);
        }

        // 4. 验证执行统计
        $stats = $addFansWorkflow->getExecutionStats();
        if (!empty($stats)) {
            $this->assertArrayHasKey('total_steps', $stats);
            $this->assertArrayHasKey('total_duration', $stats);
        }
    }

    /**
     * 测试多平台混合充值流程
     */
    public function testMixedPlatformRechargeFlow()
    {
        $mixedData = $this->testData['mixed_platform'];
        
        // 1. 构建混合平台充值数据
        $allAccounts = array_merge($mixedData['tiktok_accounts'], $mixedData['adq_accounts']);
        $queueData = [
            'target_advertiser_ids' => $allAccounts,
            'amount' => $mixedData['amount'],
            'user_name' => '测试用户'
        ];

        // 2. 执行混合平台充值
        $queueExecutionWorkflow = QueueExecutionWorkflow::createWithService($this->service);
        $mixedResult = $queueExecutionWorkflow->execute($queueData);

        // 3. 验证混合平台处理结果
        $this->assertEquals(4, $mixedResult['statistics']['total_accounts']);
        
        // 4. 验证平台分布
        $platformDistribution = $queueExecutionWorkflow->getPlatformDistribution();
        $this->assertTrue(is_array($platformDistribution));
        
        // 5. 验证成功率计算
        $successRate = $queueExecutionWorkflow->getSuccessRate();
        $this->assertTrue(is_float($successRate));
        $this->assertGreaterThanOrEqual(0, $successRate);
        $this->assertLessThanOrEqual(100, $successRate);
    }

    /**
     * 测试错误场景和异常处理
     */
    public function testErrorScenariosAndExceptionHandling()
    {
        // 1. 测试无效参数处理
        $invalidParams = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "无效格式数据"
        ];

        $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->service);
        $invalidResult = $autoRechargeWorkflow->execute($invalidParams);

        $this->assertFalse($invalidResult['success']);
        $this->assertArrayHasKey('error', $invalidResult);
        $this->assertArrayHasKey('execution_steps', $invalidResult);

        // 2. 测试空账户ID处理
        $emptyAccountParams = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：\n转账金额：100"
        ];

        $emptyResult = $autoRechargeWorkflow->execute($emptyAccountParams);
        $this->assertFalse($emptyResult['success']);
        $this->assertContains('账户ID不能为空', $emptyResult['error']);

        // 3. 测试无效金额处理
        $invalidAmountParams = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：**********\n转账金额：0"
        ];

        $amountResult = $autoRechargeWorkflow->execute($invalidAmountParams);
        $this->assertFalse($amountResult['success']);
        $this->assertContains('金额必须大于0', $amountResult['error']);

        // 4. 测试时间限制处理
        $restrictedTimeParams = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：**********\n转账金额：100"
        ];

        // Mock时间限制
        // Mock时间限制场景测试
        try {
            // 这里可以通过配置或Mock来模拟时间限制场景
            $this->assertTrue(true); // 占位符，实际测试需要根据具体实现调整
        } finally {
            // 恢复原始设置
        }
    }

    /**
     * 测试缓存机制和数据一致性
     */
    public function testCacheMechanismAndDataConsistency()
    {
        $targetId = '**********';
        $amount = 100;
        $userName = '测试用户';

        // 1. 测试缓存写入
        $writeResult = $this->cacheManager->recordSuccessfulTransfer($targetId, $amount, $userName);
        $this->assertTrue($writeResult);

        // 2. 测试缓存读取
        $history = $this->cacheManager->getTransferHistory($targetId);
        $this->assertTrue(is_array($history));

        // 3. 测试小时限额检查
        try {
            $limitResult = $this->cacheManager->checkHourlyLimit($targetId, $amount, 3000);
            $this->assertTrue($limitResult);
        } catch (Exception $e) {
            // 可能因为缓存数据而抛出异常，这是正常的
            $this->assertInstanceOf(Exception::class, $e);
        }

        // 4. 测试余额缓存
        $this->cacheManager->setBalance($targetId, 5000);
        $balance = $this->cacheManager->getBalance($targetId);
        $this->assertEquals(5000, $balance);

        // 5. 测试缓存一致性
        $this->cacheManager->setBalance($targetId, 4000);
        $newBalance = $this->cacheManager->getBalance($targetId);
        $this->assertEquals(4000, $newBalance);
    }

    /**
     * 测试队列任务处理完整流程
     */
    public function testQueueJobProcessingFlow()
    {
        // 1. 创建队列任务
        $jobData = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户',
            'execute_time' => time(),
            'isTimeRecharge' => false
        ];

        $job = new TransferMoneyJobV2(['data' => $jobData]);

        // 2. 验证任务属性
        $this->assertEquals($jobData, $job->data);
        $this->assertTrue($job->isSendMessage);

        // 3. 验证服务实例
        $service = $job->getTransferService();
        $this->assertInstanceOf(TransferMoneyServiceV2::class, $service);

        // 4. 验证任务唯一标识
        $jobKey = $job->getJobKey();
        $this->assertTrue(is_string($jobKey));
        $this->assertNotEmpty($jobKey);

        // 5. 测试批量任务添加
        $batchData = [
            [
                'target_advertiser_ids' => ['**********'],
                'amount' => 100,
                'user_name' => '用户1',
                'execute_time' => time() + 60,
                'isTimeRecharge' => false,
            ],
            [
                'target_advertiser_ids' => ['2222222222'],
                'amount' => 200,
                'user_name' => '用户2',
                'execute_time' => time() + 120,
                'isTimeRecharge' => false,
            ]
        ];

        $batchResults = TransferMoneyJobV2::addBatchJobs($batchData);
        $this->assertCount(2, $batchResults);
        $this->assertTrue($batchResults[0]['success']);
        $this->assertTrue($batchResults[1]['success']);
    }

    /**
     * 测试配置系统集成
     */
    public function testConfigurationSystemIntegration()
    {
        // 1. 测试平台配置
        $platformConfig = ConfigManager::getPlatformConfig();
        $this->assertTrue(is_array($platformConfig));
        $this->assertArrayHasKey('platforms', $platformConfig);

        // 2. 测试限制配置
        $limitsConfig = ConfigManager::getTransferLimitsConfig();
        $this->assertTrue(is_array($limitsConfig));
        $this->assertArrayHasKey('time_restrictions', $limitsConfig);

        // 3. 测试预算配置
        $budgetConfig = ConfigManager::getBudgetRulesConfig();
        $this->assertTrue(is_array($budgetConfig));
        $this->assertArrayHasKey('budget_control', $budgetConfig);

        // 4. 测试配置版本管理
        $versions = ConfigManager::getAllConfigVersions();
        $this->assertTrue(is_array($versions));
        $this->assertArrayHasKey('platform', $versions);
        $this->assertArrayHasKey('limits', $versions);
        $this->assertArrayHasKey('budget', $versions);

        // 5. 测试配置热更新
        ConfigManager::reload('platform');
        $reloadedConfig = ConfigManager::getPlatformConfig();
        $this->assertEquals($platformConfig, $reloadedConfig);
    }

    /**
     * 测试系统性能和资源使用
     */
    public function testSystemPerformanceAndResourceUsage()
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        // 1. 执行多个充值流程
        for ($i = 0; $i < 10; $i++) {
            $params = [
                'user_id' => $i,
                'user_name' => "测试用户{$i}",
                'data' => "账户ID：123456789{$i}\n转账金额：100"
            ];

            $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->service);
            $result = $autoRechargeWorkflow->execute($params);
            
            // 验证每次执行都能正常完成
            $this->assertArrayHasKey('success', $result);
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        // 2. 验证性能指标
        $totalTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;

        $this->assertLessThan(30, $totalTime, '10次充值流程执行时间应少于30秒');
        $this->assertLessThan(50 * 1024 * 1024, $memoryUsed, '内存使用应少于50MB');

        // 3. 测试并发安全性
        $concurrentResults = [];
        for ($i = 0; $i < 5; $i++) {
            try {
                $this->cacheManager->recordSuccessfulTransfer("concurrent_test_{$i}", 100, "用户{$i}");
                $concurrentResults[] = true;
            } catch (Exception $e) {
                $concurrentResults[] = false;
            }
        }

        $successCount = array_sum($concurrentResults);
        $this->assertGreaterThan(0, $successCount, '并发操作应至少有部分成功');
    }

    /**
     * 测试系统监控和统计
     */
    public function testSystemMonitoringAndStatistics()
    {
        // 1. 测试任务统计
        $jobStats = TransferMoneyJobV2::getJobStats();
        $this->assertTrue(is_array($jobStats));
        $this->assertArrayHasKey('daily_restricted_accounts', $jobStats);
        $this->assertArrayHasKey('config_version', $jobStats);
        $this->assertArrayHasKey('limits', $jobStats);

        // 2. 测试工作流统计
        $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->service);
        $params = $this->testData['single_account'];
        $result = $autoRechargeWorkflow->execute($params);

        $stats = $autoRechargeWorkflow->getExecutionStats();
        $this->assertArrayHasKey('total_steps', $stats);
        $this->assertArrayHasKey('total_duration', $stats);

        // 3. 测试队列执行统计
        $queueData = [
            'target_advertiser_ids' => ['**********', '**********'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        $queueExecutionWorkflow = QueueExecutionWorkflow::createWithService($this->service);
        $queueResult = $queueExecutionWorkflow->execute($queueData);

        $queueStats = $queueExecutionWorkflow->getExecutionStats();
        $this->assertArrayHasKey('total_accounts', $queueStats);
        $this->assertArrayHasKey('success_accounts', $queueStats);
        $this->assertArrayHasKey('failed_accounts', $queueStats);

        // 4. 测试平台分布统计
        $platformDistribution = $queueExecutionWorkflow->getPlatformDistribution();
        $this->assertTrue(is_array($platformDistribution));
    }

    /**
     * 测试系统完整性和一致性
     */
    public function testSystemIntegrityAndConsistency()
    {
        // 1. 验证所有核心组件都能正常创建
        $this->assertInstanceOf(TransferMoneyServiceV2::class, $this->service);
        $this->assertInstanceOf(PlatformFactory::class, $this->platformFactory);
        $this->assertInstanceOf(TransferCacheManager::class, $this->cacheManager);

        // 2. 验证工作流都能正常创建
        $autoRechargeWorkflow = AutoRechargeWorkflow::create();
        $this->assertInstanceOf(AutoRechargeWorkflow::class, $autoRechargeWorkflow);

        $queueExecutionWorkflow = QueueExecutionWorkflow::create();
        $this->assertInstanceOf(QueueExecutionWorkflow::class, $queueExecutionWorkflow);

        $addFansWorkflow = AddFansRechargeWorkflow::create();
        $this->assertInstanceOf(AddFansRechargeWorkflow::class, $addFansWorkflow);

        // 3. 验证平台适配器集成
        $mockAdapter = $this->platformFactory->create('mock');
        $this->assertInstanceOf(MockAdapter::class, $mockAdapter);

        // 4. 验证服务状态一致性
        $this->service->initialize();
        $initialCode = $this->service->getCode();

        $this->service->setCode(200);
        $this->assertEquals(200, $this->service->getCode());

        $this->service->initialize();
        $this->assertEquals($initialCode, $this->service->getCode());

        // 5. 验证错误码体系一致性
        $this->assertEquals(200, $this->service->getSuccessCode());
        $this->assertEquals(100, $this->service->getTimeCode());
        $this->assertEquals(201, $this->service->getSuccessInsufficientBalanceCode());
        $this->assertEquals(422, $this->service->getErrorCodeIt());
        $this->assertEquals(423, $this->service->getErrorCodePromote());
        $this->assertEquals(424, $this->service->getErrorCodeInsufficientBalance());
    }

    /**
     * 测试数据流转完整性
     */
    public function testDataFlowIntegrity()
    {
        // 1. 从入队到执行的完整数据流
        $originalParams = $this->testData['single_account'];
        
        // 入队阶段
        $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->service);
        $enqueueResult = $autoRechargeWorkflow->execute($originalParams);
        
        if ($enqueueResult['success']) {
            // 验证数据转换正确性
            $this->assertArrayHasKey('execution_steps', $enqueueResult);
            $steps = $enqueueResult['execution_steps'];
            
            // 查找参数处理步骤
            $paramSteps = array_filter($steps, function($step) {
                return strpos($step['step'], '参数处理') !== false;
            });
            $this->assertNotEmpty($paramSteps);
        }

        // 2. 队列执行阶段数据流
        $queueData = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        $queueExecutionWorkflow = QueueExecutionWorkflow::createWithService($this->service);
        $executionResult = $queueExecutionWorkflow->execute($queueData);

        // 验证执行结果数据完整性
        $this->assertArrayHasKey('execution_results', $executionResult);
        $this->assertArrayHasKey('statistics', $executionResult);
        $this->assertArrayHasKey('formatted_results', $executionResult);

        // 3. 验证统计数据一致性
        $stats = $executionResult['statistics'];
        $totalAccounts = $stats['total_accounts'];
        $successAccounts = $stats['success_accounts'];
        $failedAccounts = $stats['failed_accounts'];
        
        $this->assertEquals($totalAccounts, $successAccounts + $failedAccounts);
        $this->assertEquals(1, $totalAccounts);
    }
}