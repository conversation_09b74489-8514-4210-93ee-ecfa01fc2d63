<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\integration;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\config\ConfigManager;
use common\queues\TransferMoneyJobV2;
use Codeception\Test\Unit;
use Exception;
use Yii;

/**
 * 转账充值系统集成测试
 * 
 * 测试整个转账充值系统的集成功能：
 * - 队列任务与服务层的集成
 * - 配置系统与业务逻辑的集成
 * - 各组件之间的协作
 * - 端到端的业务流程
 */
class TransferMoneyIntegrationTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    protected function _before()
    {
        // 清理配置缓存
        ConfigManager::clearCache();
    }

    protected function _after()
    {
        // 清理配置缓存
        ConfigManager::clearCache();
    }

    /**
     * 测试完整的充值流程
     */
    public function testCompleteTransferFlow()
    {
        // 准备测试数据
        $transferData = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户',
            'user_id' => 1,
            'execute_time' => time(),
            'isTimeRecharge' => false,
            'create_time' => time(),
        ];

        // 创建队列任务
        $job = new TransferMoneyJobV2(['data' => $transferData]);

        // 验证任务属性
        $this->assertEquals($transferData, $job->data);
        $this->assertTrue($job->isSendMessage);

        // 验证服务实例
        $service = $job->getTransferService();
        $this->assertInstanceOf(TransferMoneyServiceV2::class, $service);

        // 验证任务唯一标识生成
        $jobKey = $job->getJobKey();
        $this->assertIsString($jobKey);
        $this->assertNotEmpty($jobKey);
    }

    /**
     * 测试配置系统与业务逻辑集成
     */
    public function testConfigIntegrationWithBusinessLogic()
    {
        // 测试平台配置集成
        $tiktokAdapterClass = ConfigManager::getPlatformAdapterClass('tiktok');
        $this->assertEquals('backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter', $tiktokAdapterClass);

        $adqAdapterClass = ConfigManager::getPlatformAdapterClass('adq');
        $this->assertEquals('backendapi\services\promote\transfermoneyv2\platform\AdqAdapter', $adqAdapterClass);

        // 测试限制配置集成
        $maxBatchSize = ConfigManager::getBatchLimit('max_accounts_per_batch');
        $this->assertEquals(50, $maxBatchSize);

        $sleepDuration = ConfigManager::getTransferLimitsConfig('frequency_control.sleep_duration');
        $this->assertEquals(500000, $sleepDuration);

        // 测试时间限制集成
        $this->assertTrue(ConfigManager::isTimeRestricted('03:00'));
        $this->assertFalse(ConfigManager::isTimeRestricted('10:00'));

        // 测试预算配置集成
        $warningThreshold = ConfigManager::getBalanceWarningThreshold();
        $this->assertEquals(3000, $warningThreshold);

        $budgetEnabled = ConfigManager::isBudgetControlEnabled();
        $this->assertTrue($budgetEnabled);
    }

    /**
     * 测试加粉充值集成流程
     */
    public function testFansRechargeIntegration()
    {
        $subAdvertiserId = '**********';

        // Mock 配置获取
        $mockConfig = $this->createMock(\common\models\Config::class);
        $mockConfig::staticExpects($this->once())
            ->method('getByName')
            ->with('transferAccount')
            ->willReturn("**********\n0987654321");

        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->expects($this->exactly(2))
            ->method('get')
            ->willReturnOnConsecutiveCalls(null, false); // 当天限制列表为空，计数为false

        $mockCache->expects($this->once())
            ->method('set')
            ->with(
                $this->stringContains('AddFansTransferMoneyCount:' . $subAdvertiserId),
                1,
                300
            );

        Yii::$app->set('cache', $mockCache);

        // Mock 队列
        $mockQueue = $this->createMock(\yii\queue\Queue::class);
        $mockQueue->expects($this->once())
            ->method('has')
            ->willReturn(false);

        $mockQueue->expects($this->once())
            ->method('push')
            ->with($this->callback(function ($job) {
                return $job instanceof TransferMoneyJobV2 
                    && $job->data['amount'] == 50
                    && $job->data['user_name'] == '系统自动充值'
                    && !$job->isSendMessage;
            }));

        Yii::$app->set('que', $mockQueue);

        // 执行加粉充值
        $result = TransferMoneyJobV2::addFansJob($subAdvertiserId);
        $this->assertTrue($result);
    }

    /**
     * 测试批量任务处理集成
     */
    public function testBatchJobProcessingIntegration()
    {
        $batchData = [
            [
                'target_advertiser_ids' => ['**********'],
                'amount' => 100,
                'user_name' => '用户1',
                'execute_time' => time() + 60,
                'isTimeRecharge' => false,
            ],
            [
                'target_advertiser_ids' => ['**********'],
                'amount' => 200,
                'user_name' => '用户2',
                'execute_time' => time() + 120,
                'isTimeRecharge' => false,
            ],
        ];

        // Mock 队列
        $mockQueue = $this->createMock(\yii\queue\Queue::class);
        $mockQueue->expects($this->exactly(2))
            ->method('has')
            ->willReturn(false);

        $mockQueue->expects($this->exactly(2))
            ->method('setImportant')
            ->willReturnSelf();

        $mockQueue->expects($this->exactly(2))
            ->method('delay')
            ->willReturnSelf();

        $mockQueue->expects($this->exactly(2))
            ->method('push');

        Yii::$app->set('que', $mockQueue);

        // 执行批量添加任务
        $results = TransferMoneyJobV2::addBatchJobs($batchData);

        $this->assertCount(2, $results);
        $this->assertTrue($results[0]['success']);
        $this->assertTrue($results[1]['success']);
    }

    /**
     * 测试配置热更新集成
     */
    public function testConfigHotReloadIntegration()
    {
        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);

        // 第一次获取配置
        $mockCache->expects($this->once())
            ->method('get')
            ->with('config_platform')
            ->willReturn(false);

        $mockCache->expects($this->once())
            ->method('set')
            ->with('config_platform', $this->isType('array'), 3600);

        Yii::$app->set('cache', $mockCache);

        // 获取配置
        $config1 = ConfigManager::getPlatformConfig();
        $this->assertIsArray($config1);

        // 清除缓存并重新加载
        $mockCache->expects($this->once())
            ->method('delete')
            ->with('config_platform');

        $mockCache->expects($this->once())
            ->method('get')
            ->with('config_platform')
            ->willReturn(false);

        $mockCache->expects($this->once())
            ->method('set')
            ->with('config_platform', $this->isType('array'), 3600);

        ConfigManager::reload('platform');

        // 验证配置重新加载
        $config2 = ConfigManager::getPlatformConfig();
        $this->assertEquals($config1, $config2);
    }

    /**
     * 测试错误处理集成
     */
    public function testErrorHandlingIntegration()
    {
        // 测试无效平台配置
        try {
            ConfigManager::getPlatformAdapterClass('invalid_platform');
            $this->fail('应该抛出异常');
        } catch (Exception $e) {
            $this->assertStringContains('未知的平台', $e->getMessage());
        }

        // 测试批量任务超限
        $oversizedBatch = [
            [
                'target_advertiser_ids' => array_fill(0, 60, '**********'), // 超过50个限制
                'amount' => 100,
                'user_name' => '测试用户',
                'execute_time' => time(),
                'isTimeRecharge' => false,
            ]
        ];

        $results = TransferMoneyJobV2::addBatchJobs($oversizedBatch);
        $this->assertFalse($results[0]['success']);
        $this->assertStringContains('批量充值账户数量不能超过', $results[0]['message']);
    }

    /**
     * 测试任务统计信息集成
     */
    public function testJobStatsIntegration()
    {
        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->expects($this->once())
            ->method('get')
            ->with('AddFansTransferMoneyCount:' . date('Y-m-d'))
            ->willReturn(['**********', '**********']);

        Yii::$app->set('cache', $mockCache);

        // 获取任务统计
        $stats = TransferMoneyJobV2::getJobStats();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('daily_restricted_accounts', $stats);
        $this->assertArrayHasKey('config_version', $stats);
        $this->assertArrayHasKey('limits', $stats);

        $this->assertEquals(2, $stats['daily_restricted_accounts']);
        $this->assertIsArray($stats['config_version']);
        $this->assertIsArray($stats['limits']);
    }

    /**
     * 测试清理过期限制记录集成
     */
    public function testCleanupExpiredRestrictionsIntegration()
    {
        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        
        // 模拟删除7天的记录
        $mockCache->expects($this->exactly(7))
            ->method('delete')
            ->willReturn(true);

        Yii::$app->set('cache', $mockCache);

        // 执行清理
        $cleanupCount = TransferMoneyJobV2::cleanupExpiredRestrictions(7);
        $this->assertEquals(7, $cleanupCount);
    }

    /**
     * 测试消息发送集成
     */
    public function testMessageSendingIntegration()
    {
        $transferData = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户',
            'isTimeRecharge' => false,
        ];

        $job = new TransferMoneyJobV2([
            'data' => $transferData,
            'isSendMessage' => true
        ]);

        $arrError = [
            200 => ['msg' => '充值成功']
        ];

        // Mock Member模型
        $mockMember = $this->createMock(\common\models\backend\Member::class);
        $mockMember::staticExpects($this->once())
            ->method('find')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('select')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('where')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('scalar')
            ->willReturn('test_feishu_id');

        // Mock 飞书通知
        $mockFeishuNotice = $this->createMock(\stdClass::class);
        $mockFeishuNotice->expects($this->once())
            ->method('text')
            ->with(
                $this->stringContains('充值成功'),
                'test_feishu_id',
                'user_id'
            );

        Yii::$app->set('feishuNotice', $mockFeishuNotice);

        $result = $job->sendMsg($arrError);
        $this->assertTrue($result);
    }

    /**
     * 测试配置验证集成
     */
    public function testConfigValidationIntegration()
    {
        // 验证所有配置文件的完整性
        $platformConfig = ConfigManager::getPlatformConfig();
        $this->assertArrayHasKey('platforms', $platformConfig);
        $this->assertArrayHasKey('platform_limits', $platformConfig);
        $this->assertArrayHasKey('default', $platformConfig);

        $limitsConfig = ConfigManager::getTransferLimitsConfig();
        $this->assertArrayHasKey('time_restrictions', $limitsConfig);
        $this->assertArrayHasKey('batch_limits', $limitsConfig);
        $this->assertArrayHasKey('frequency_control', $limitsConfig);

        $budgetConfig = ConfigManager::getBudgetRulesConfig();
        $this->assertArrayHasKey('budget_control', $budgetConfig);
        $this->assertArrayHasKey('user_daily_limits', $budgetConfig);
        $this->assertArrayHasKey('balance_warnings', $budgetConfig);

        // 验证配置版本信息
        $versions = ConfigManager::getAllConfigVersions();
        $this->assertArrayHasKey('platform', $versions);
        $this->assertArrayHasKey('limits', $versions);
        $this->assertArrayHasKey('budget', $versions);

        foreach ($versions as $type => $version) {
            $this->assertArrayHasKey('version', $version);
            $this->assertArrayHasKey('updated_at', $version);
        }
    }

    /**
     * 测试系统兼容性
     */
    public function testSystemCompatibility()
    {
        // 验证新队列任务与现有系统的兼容性
        $job = new TransferMoneyJobV2();
        
        // 验证继承关系
        $this->assertInstanceOf(\common\queues\BaseJob::class, $job);
        
        // 验证必要属性
        $this->assertObjectHasAttribute('delay', $job);
        $this->assertObjectHasAttribute('retryTimes', $job);
        $this->assertObjectHasAttribute('data', $job);
        $this->assertObjectHasAttribute('isSendMessage', $job);

        // 验证必要方法
        $this->assertTrue(method_exists($job, 'run'));
        $this->assertTrue(method_exists($job, 'getJobKey'));
        $this->assertTrue(method_exists($job, 'sendMsg'));
        $this->assertTrue(method_exists($job, 'getSendUserId'));

        // 验证静态方法
        $this->assertTrue(method_exists(TransferMoneyJobV2::class, 'addJob'));
        $this->assertTrue(method_exists(TransferMoneyJobV2::class, 'addFansJob'));
        $this->assertTrue(method_exists(TransferMoneyJobV2::class, 'checkTransferMoneyCount'));
    }

    /**
     * 测试性能和资源使用
     */
    public function testPerformanceAndResourceUsage()
    {
        // 测试配置缓存性能
        $startTime = microtime(true);
        
        // 多次获取配置，验证缓存效果
        for ($i = 0; $i < 10; $i++) {
            ConfigManager::getPlatformConfig();
            ConfigManager::getTransferLimitsConfig();
            ConfigManager::getBudgetRulesConfig();
        }
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // 验证执行时间合理（应该很快，因为有缓存）
        $this->assertLessThan(1.0, $executionTime, '配置获取性能测试失败');

        // 测试内存使用
        $memoryBefore = memory_get_usage();
        
        // 创建多个任务实例
        $jobs = [];
        for ($i = 0; $i < 100; $i++) {
            $jobs[] = new TransferMoneyJobV2([
                'data' => [
                    'target_advertiser_ids' => ['test_' . $i],
                    'amount' => 100,
                    'user_name' => '测试用户',
                ]
            ]);
        }
        
        $memoryAfter = memory_get_usage();
        $memoryUsed = $memoryAfter - $memoryBefore;
        
        // 验证内存使用合理（每个任务实例不应该占用太多内存）
        $this->assertLessThan(10 * 1024 * 1024, $memoryUsed, '内存使用测试失败'); // 10MB限制
        
        // 清理
        unset($jobs);
    }
}