<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\platform;

use backendapi\services\promote\transfermoneyv2\platform\MockAdapter;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use Codeception\Test\Unit;
use Yii;

/**
 * MockAdapter 测试用例
 * 
 * 测试Mock适配器的功能正确性和安全性
 * 确保Mock适配器绝对不会触发真实充值操作
 */
class MockAdapterTest extends Unit
{
    /**
     * @var MockAdapter
     */
    private $mockAdapter;

    protected function _before()
    {
        $this->mockAdapter = new MockAdapter();
    }

    protected function _after()
    {
        // 清理测试日志
        if (file_exists(Yii::getAlias('@runtime/logs/mock_adapter_test.log'))) {
            unlink(Yii::getAlias('@runtime/logs/mock_adapter_test.log'));
        }
    }

    /**
     * 测试Mock适配器实现了平台接口
     */
    public function testImplementsPlatformInterface()
    {
        $this->assertInstanceOf(PlatformAdapterInterface::class, $this->mockAdapter);
    }

    /**
     * 测试获取平台名称
     */
    public function testGetName()
    {
        $this->assertEquals('mock', $this->mockAdapter->getName());
    }

    /**
     * 测试获取单次充值限额
     */
    public function testGetSingleLimit()
    {
        $this->assertEquals(10000, $this->mockAdapter->getSingleLimit());
    }

    /**
     * 测试获取小时充值限额
     */
    public function testGetHourlyLimit()
    {
        $this->assertEquals(50000, $this->mockAdapter->getHourlyLimit());
    }

    /**
     * 测试成功充值场景
     */
    public function testTransferMoneySuccess()
    {
        $result = $this->mockAdapter->transferMoney(
            'mock_access_token',
            'mock_org_id',
            'mock_from_account',
            'mock_to_account',
            100
        );

        $this->assertTrue(is_array($result));
        $this->assertEquals(0, $result['code']);
        $this->assertEquals('Mock充值成功', $result['message']);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('transaction_id', $result['data']);
        $this->assertEquals('mock_to_account', $result['data']['target_account_id']);
        $this->assertEquals(100, $result['data']['amount']);
    }

    /**
     * 测试余额不足场景
     */
    public function testTransferMoneyInsufficientBalance()
    {
        // 设置模拟余额不足的账户
        $this->mockAdapter->setMockBalance('insufficient_balance_account', 50);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Mock模拟：账户余额不足');

        $this->mockAdapter->transferMoney(
            'mock_access_token',
            'mock_org_id',
            'insufficient_balance_account',
            'mock_to_account',
            100
        );
    }

    /**
     * 测试接口异常场景
     */
    public function testTransferMoneyApiError()
    {
        // 设置模拟API错误的账户
        $this->mockAdapter->setMockError('error_account', 'Mock模拟：接口调用失败');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Mock模拟：接口调用失败');

        $this->mockAdapter->transferMoney(
            'mock_access_token',
            'mock_org_id',
            'error_account',
            'mock_to_account',
            100
        );
    }

    /**
     * 测试网络超时场景
     */
    public function testTransferMoneyTimeout()
    {
        // 设置模拟超时的账户
        $this->mockAdapter->setMockTimeout('timeout_account');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Mock模拟：网络请求超时');

        $this->mockAdapter->transferMoney(
            'mock_access_token',
            'mock_org_id',
            'timeout_account',
            'mock_to_account',
            100
        );
    }

    /**
     * 测试参数错误场景
     */
    public function testTransferMoneyInvalidParams()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Mock模拟：充值金额必须大于0');

        $this->mockAdapter->transferMoney(
            'mock_access_token',
            'mock_org_id',
            'mock_from_account',
            'mock_to_account',
            0
        );
    }

    /**
     * 测试获取账户余额成功
     */
    public function testGetBalanceSuccess()
    {
        $balance = $this->mockAdapter->getBalance('mock_access_token', 'mock_account');
        $this->assertEquals(10000.0, $balance);
    }

    /**
     * 测试获取自定义余额
     */
    public function testGetBalanceCustom()
    {
        $this->mockAdapter->setMockBalance('custom_account', 5000);
        $balance = $this->mockAdapter->getBalance('mock_access_token', 'custom_account');
        $this->assertEquals(5000.0, $balance);
    }

    /**
     * 测试获取余额失败
     */
    public function testGetBalanceError()
    {
        $this->mockAdapter->setMockError('error_account', 'Mock模拟：查询余额失败');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Mock模拟：查询余额失败');

        $this->mockAdapter->getBalance('mock_access_token', 'error_account');
    }

    /**
     * 测试参数验证成功
     */
    public function testValidateParamsSuccess()
    {
        $params = [
            'access_token' => 'mock_token',
            'from_account' => 'mock_from',
            'to_account' => 'mock_to',
            'amount' => 100
        ];

        $this->assertTrue($this->mockAdapter->validateParams($params));
    }

    /**
     * 测试参数验证失败 - 缺少必要参数
     */
    public function testValidateParamsMissingRequired()
    {
        $params = [
            'access_token' => 'mock_token',
            // 缺少其他必要参数
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Mock模拟：缺少必要参数');

        $this->mockAdapter->validateParams($params);
    }

    /**
     * 测试错误信息格式化
     */
    public function testFormatError()
    {
        $response = [
            'code' => 1001,
            'message' => 'API调用失败',
            'details' => '详细错误信息'
        ];

        $formattedError = $this->mockAdapter->formatError($response);
        $this->assertEquals('Mock错误[1001]: API调用失败 - 详细错误信息', $formattedError);
    }

    /**
     * 测试调用日志记录
     */
    public function testCallLogging()
    {
        // 执行一次充值操作
        $this->mockAdapter->transferMoney(
            'test_token',
            'test_org',
            'test_from',
            'test_to',
            200
        );

        // 获取调用日志
        $logs = $this->mockAdapter->getCallLogs();
        $this->assertNotEmpty($logs);
        
        $lastLog = end($logs);
        $this->assertEquals('transferMoney', $lastLog['method']);
        $this->assertEquals('test_from', $lastLog['params']['fromAccountId']);
        $this->assertEquals('test_to', $lastLog['params']['toAccountId']);
        $this->assertEquals(200, $lastLog['params']['amount']);
        $this->assertEquals('success', $lastLog['result']['status']);
    }

    /**
     * 测试重置Mock状态
     */
    public function testResetMockState()
    {
        // 设置一些Mock状态
        $this->mockAdapter->setMockBalance('test_account', 1000);
        $this->mockAdapter->setMockError('error_account', 'test error');
        
        // 执行一些操作产生日志
        $this->mockAdapter->getBalance('test_token', 'test_account');
        
        // 重置状态
        $this->mockAdapter->resetMockState();
        
        // 验证状态已重置
        $balance = $this->mockAdapter->getBalance('test_token', 'test_account');
        $this->assertEquals(10000.0, $balance); // 默认余额
        
        $logs = $this->mockAdapter->getCallLogs();
        $this->assertEmpty($logs); // 日志已清空
    }

    /**
     * 测试Mock适配器的安全性 - 确保不会调用真实接口
     */
    public function testMockSafety()
    {
        // 使用真实的账户ID和参数进行测试
        $result = $this->mockAdapter->transferMoney(
            'real_access_token',
            'real_organization_id', 
            'real_from_account_id',
            'real_to_account_id',
            1000
        );

        // 验证返回的是Mock结果，而不是真实结果
        $this->assertEquals(0, $result['code']);
        $this->assertEquals('Mock充值成功', $result['message']);
        $this->assertContains('mock_transaction_', $result['data']['transaction_id']);
        
        // 验证调用被记录但没有真实执行
        $logs = $this->mockAdapter->getCallLogs();
        $this->assertNotEmpty($logs);
        $lastLog = end($logs);
        $this->assertEquals('mock', $lastLog['platform']);
        $this->assertEquals('transferMoney', $lastLog['method']);
    }

    /**
     * 测试并发调用的安全性
     */
    public function testConcurrentCallsSafety()
    {
        $results = [];
        
        // 模拟并发调用
        for ($i = 0; $i < 10; $i++) {
            $results[] = $this->mockAdapter->transferMoney(
                'concurrent_token_' . $i,
                'concurrent_org_' . $i,
                'concurrent_from_' . $i,
                'concurrent_to_' . $i,
                100 + $i
            );
        }

        // 验证所有调用都返回Mock结果
        foreach ($results as $i => $result) {
            $this->assertEquals(0, $result['code']);
            $this->assertEquals('Mock充值成功', $result['message']);
            $this->assertEquals('concurrent_to_' . $i, $result['data']['target_account_id']);
            $this->assertEquals(100 + $i, $result['data']['amount']);
        }

        // 验证所有调用都被正确记录
        $logs = $this->mockAdapter->getCallLogs();
        $this->assertCount(10, $logs);
    }
}