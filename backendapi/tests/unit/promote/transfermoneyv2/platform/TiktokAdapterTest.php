<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\platform;

use backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use common\components\promoteData\Oceanengine;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * TiktokAdapter测试用例
 * 
 * 测试抖音平台适配器的各种功能和边界情况
 * 基于现有Oceanengine类的接口设计
 */
class TiktokAdapterTest extends TestCase
{
    /**
     * @var TiktokAdapter
     */
    private $adapter;

    /**
     * @var MockObject|Oceanengine
     */
    private $mockOceanengine;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建Oceanengine的Mock对象
        $this->mockOceanengine = $this->createMock(Oceanengine::class);
        
        // 创建适配器实例
        $this->adapter = new TiktokAdapter();
        
        // 注入Mock对象（需要在适配器中添加setter方法）
        if (method_exists($this->adapter, 'setOceanengineInstance')) {
            $this->adapter->setOceanengineInstance($this->mockOceanengine);
        }
    }

    /**
     * 测试适配器实现了正确的接口
     */
    public function testImplementsCorrectInterface()
    {
        $this->assertInstanceOf(PlatformAdapterInterface::class, $this->adapter);
    }

    /**
     * 测试获取平台名称
     */
    public function testGetName()
    {
        $this->assertEquals('tiktok', $this->adapter->getName());
    }

    /**
     * 测试获取单次充值限额
     */
    public function testGetSingleLimit()
    {
        $this->assertEquals(1000, $this->adapter->getSingleLimit());
    }

    /**
     * 测试获取小时充值限额
     */
    public function testGetHourlyLimit()
    {
        $this->assertEquals(3000, $this->adapter->getHourlyLimit());
    }

    /**
     * 测试成功的转账充值
     */
    public function testTransferMoneySuccess()
    {
        $accessToken = 'test_access_token';
        $organizationId = '*********';
        $fromAccountId = '*********';
        $toAccountId = '*********';
        $amount = 500;

        // 模拟Oceanengine::transferCreate的成功响应
        $mockResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'transfer_serial' => '********************',
                'biz_request_no' => '20250123*********0',
                'transfer_status' => 'TRANSFER_SUCCESS'
            ]
        ];

        $this->mockOceanengine
            ->expects($this->once())
            ->method('transferCreate')
            ->with(
                $accessToken,
                $organizationId,
                $fromAccountId,
                $toAccountId,
                $amount
            )
            ->willReturn($mockResponse);

        $result = $this->adapter->transferMoney(
            $accessToken,
            $organizationId,
            $fromAccountId,
            $toAccountId,
            $amount
        );

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['code']);
        $this->assertEquals('success', $result['message']);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('transfer_serial', $result['data']);
    }

    /**
     * 测试转账充值失败
     */
    public function testTransferMoneyFailure()
    {
        $accessToken = 'test_access_token';
        $organizationId = '*********';
        $fromAccountId = '*********';
        $toAccountId = '*********';
        $amount = 500;

        // 模拟Oceanengine::transferCreate的失败响应
        $mockResponse = [
            'code' => 40001,
            'message' => '账户余额不足',
            'data' => null
        ];

        $this->mockOceanengine
            ->expects($this->once())
            ->method('transferCreate')
            ->with(
                $accessToken,
                $organizationId,
                $fromAccountId,
                $toAccountId,
                $amount
            )
            ->willReturn($mockResponse);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('抖音转账失败[40001]: 账户余额不足');

        $this->adapter->transferMoney(
            $accessToken,
            $organizationId,
            $fromAccountId,
            $toAccountId,
            $amount
        );
    }

    /**
     * 测试转账金额验证
     */
    public function testTransferMoneyAmountValidation()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('充值金额必须大于0');

        $this->adapter->transferMoney(
            'test_token',
            '*********',
            '*********',
            '*********',
            0
        );
    }

    /**
     * 测试转账金额超过单次限额
     */
    public function testTransferMoneyExceedsSingleLimit()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('充值金额超过单次限额1000元');

        $this->adapter->transferMoney(
            'test_token',
            '*********',
            '*********',
            '*********',
            1500
        );
    }

    /**
     * 测试成功获取账户余额
     */
    public function testGetBalanceSuccess()
    {
        $accessToken = 'test_access_token';
        $accountId = '*********';

        // 模拟Oceanengine::getFund的成功响应
        $mockResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'balance' => 500000 // 5000.00元，以分为单位
            ]
        ];

        $this->mockOceanengine
            ->expects($this->once())
            ->method('getFund')
            ->with($accessToken, $accountId)
            ->willReturn($mockResponse);

        $balance = $this->adapter->getBalance($accessToken, $accountId);

        $this->assertEquals(5000.0, $balance);
    }

    /**
     * 测试获取账户余额失败
     */
    public function testGetBalanceFailure()
    {
        $accessToken = 'test_access_token';
        $accountId = '*********';

        // 模拟Oceanengine::getFund的失败响应
        $mockResponse = [
            'code' => 40003,
            'message' => '账户不存在',
            'data' => null
        ];

        $this->mockOceanengine
            ->expects($this->once())
            ->method('getFund')
            ->with($accessToken, $accountId)
            ->willReturn($mockResponse);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('查询抖音账户余额失败[40003]: 账户不存在');

        $this->adapter->getBalance($accessToken, $accountId);
    }

    /**
     * 测试参数验证成功
     */
    public function testValidateParamsSuccess()
    {
        $params = [
            'access_token' => 'test_token',
            'organization_id' => '*********',
            'from_account' => '*********',
            'to_account' => '*********',
            'amount' => 500
        ];

        $result = $this->adapter->validateParams($params);
        $this->assertTrue($result);
    }

    /**
     * 测试参数验证失败 - 缺少必要参数
     */
    public function testValidateParamsMissingRequired()
    {
        $params = [
            'access_token' => 'test_token',
            // 缺少organization_id
            'from_account' => '*********',
            'to_account' => '*********',
            'amount' => 500
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('缺少必要参数: organization_id');

        $this->adapter->validateParams($params);
    }

    /**
     * 测试参数验证失败 - 参数为空
     */
    public function testValidateParamsEmptyValue()
    {
        $params = [
            'access_token' => '',
            'organization_id' => '*********',
            'from_account' => '*********',
            'to_account' => '*********',
            'amount' => 500
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('缺少必要参数: access_token');

        $this->adapter->validateParams($params);
    }

    /**
     * 测试错误信息格式化
     */
    public function testFormatError()
    {
        $response = [
            'code' => 40001,
            'message' => '账户余额不足',
            'details' => '当前余额: 100元'
        ];

        $formattedError = $this->adapter->formatError($response);
        $this->assertEquals('抖音错误[40001]: 账户余额不足 - 当前余额: 100元', $formattedError);
    }

    /**
     * 测试错误信息格式化 - 无详细信息
     */
    public function testFormatErrorWithoutDetails()
    {
        $response = [
            'code' => 40001,
            'message' => '账户余额不足'
        ];

        $formattedError = $this->adapter->formatError($response);
        $this->assertEquals('抖音错误[40001]: 账户余额不足', $formattedError);
    }

    /**
     * 测试错误信息格式化 - 未知错误
     */
    public function testFormatErrorUnknown()
    {
        $response = [];

        $formattedError = $this->adapter->formatError($response);
        $this->assertEquals('抖音错误[unknown]: 未知错误', $formattedError);
    }
}