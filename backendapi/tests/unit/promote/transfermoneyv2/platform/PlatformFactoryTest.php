<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\platform;

use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use backendapi\services\promote\transfermoneyv2\platform\MockAdapter;
use Codeception\Test\Unit;

/**
 * PlatformFactory 测试用例
 * 
 * 测试平台工厂类的功能正确性
 * 验证工厂能够正确创建和管理平台适配器
 */
class PlatformFactoryTest extends Unit
{
    /**
     * @var PlatformFactory
     */
    private $factory;

    protected function _before()
    {
        $this->factory = new PlatformFactory();
    }

    protected function _after()
    {
        // 重置工厂状态
        $this->factory->reset();
    }

    /**
     * 测试创建Mock适配器
     */
    public function testCreateMockAdapter()
    {
        $adapter = $this->factory->create('mock');
        
        $this->assertInstanceOf(PlatformAdapterInterface::class, $adapter);
        $this->assertInstanceOf(MockAdapter::class, $adapter);
        $this->assertEquals('mock', $adapter->getName());
    }

    /**
     * 测试创建不存在的平台适配器
     */
    public function testCreateNonExistentAdapter()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('不支持的平台类型: nonexistent');

        $this->factory->create('nonexistent');
    }

    /**
     * 测试注册自定义适配器
     */
    public function testRegisterCustomAdapter()
    {
        // 创建一个自定义适配器类
        $customAdapter = new class implements PlatformAdapterInterface {
            public function getName(): string { return 'custom'; }
            public function transferMoney(string $accessToken, string $organizationId, string $fromAccountId, string $toAccountId, int $amount): array { return []; }
            public function getBalance(string $accessToken, string $accountId): float { return 0.0; }
            public function getSingleLimit(): int { return 1000; }
            public function getHourlyLimit(): int { return 5000; }
            public function validateParams(array $params): bool { return true; }
            public function formatError(array $response): string { return ''; }
        };

        // 注册自定义适配器
        $this->factory->register('custom', get_class($customAdapter));

        // 测试创建自定义适配器
        $adapter = $this->factory->create('custom');
        $this->assertInstanceOf(PlatformAdapterInterface::class, $adapter);
        $this->assertEquals('custom', $adapter->getName());
    }

    /**
     * 测试获取支持的平台列表
     */
    public function testGetSupportedPlatforms()
    {
        $platforms = $this->factory->getSupportedPlatforms();
        
        $this->assertTrue(is_array($platforms));
        $this->assertContains('mock', $platforms);
    }

    /**
     * 测试检查平台是否支持
     */
    public function testIsSupported()
    {
        $this->assertTrue($this->factory->isSupported('mock'));
        $this->assertFalse($this->factory->isSupported('nonexistent'));
    }

    /**
     * 测试单例模式 - 相同平台返回相同实例
     */
    public function testSingletonPattern()
    {
        $adapter1 = $this->factory->create('mock');
        $adapter2 = $this->factory->create('mock');
        
        $this->assertSame($adapter1, $adapter2);
    }

    /**
     * 测试强制创建新实例
     */
    public function testForceNewInstance()
    {
        $adapter1 = $this->factory->create('mock');
        $adapter2 = $this->factory->create('mock', true); // 强制创建新实例
        
        $this->assertNotSame($adapter1, $adapter2);
        $this->assertInstanceOf(MockAdapter::class, $adapter1);
        $this->assertInstanceOf(MockAdapter::class, $adapter2);
    }

    /**
     * 测试工厂重置
     */
    public function testFactoryReset()
    {
        // 创建一个适配器实例
        $adapter1 = $this->factory->create('mock');
        
        // 重置工厂
        $this->factory->reset();
        
        // 再次创建应该是新实例
        $adapter2 = $this->factory->create('mock');
        
        $this->assertNotSame($adapter1, $adapter2);
    }

    /**
     * 测试批量创建适配器
     */
    public function testBatchCreate()
    {
        $platforms = ['mock'];
        $adapters = $this->factory->batchCreate($platforms);
        
        $this->assertTrue(is_array($adapters));
        $this->assertCount(1, $adapters);
        $this->assertArrayHasKey('mock', $adapters);
        $this->assertInstanceOf(MockAdapter::class, $adapters['mock']);
    }

    /**
     * 测试批量创建包含不支持的平台
     */
    public function testBatchCreateWithUnsupportedPlatform()
    {
        $platforms = ['mock', 'nonexistent'];
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('不支持的平台类型: nonexistent');
        
        $this->factory->batchCreate($platforms);
    }

    /**
     * 测试获取适配器配置
     */
    public function testGetAdapterConfig()
    {
        $config = $this->factory->getAdapterConfig('mock');
        
        $this->assertTrue(is_array($config));
        $this->assertArrayHasKey('class', $config);
        $this->assertArrayHasKey('name', $config);
        $this->assertEquals('Mock平台适配器', $config['name']);
    }

    /**
     * 测试获取不存在平台的配置
     */
    public function testGetNonExistentAdapterConfig()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('不支持的平台类型: nonexistent');
        
        $this->factory->getAdapterConfig('nonexistent');
    }

    /**
     * 测试验证适配器接口实现
     */
    public function testValidateAdapterInterface()
    {
        // 测试正确的适配器类
        $this->assertTrue($this->factory->validateAdapterClass(MockAdapter::class));
        
        // 测试不正确的适配器类
        $this->assertFalse($this->factory->validateAdapterClass(\stdClass::class));
    }

    /**
     * 测试注册无效的适配器类
     */
    public function testRegisterInvalidAdapterClass()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('适配器类必须实现 PlatformAdapterInterface 接口');
        
        $this->factory->register('invalid', \stdClass::class);
    }

    /**
     * 测试工厂统计信息
     */
    public function testFactoryStatistics()
    {
        // 创建一些适配器实例
        $this->factory->create('mock');
        $this->factory->create('mock'); // 应该返回相同实例
        
        $stats = $this->factory->getStatistics();
        
        $this->assertTrue(is_array($stats));
        $this->assertArrayHasKey('total_platforms', $stats);
        $this->assertArrayHasKey('created_instances', $stats);
        $this->assertArrayHasKey('cache_hits', $stats);
        
        $this->assertEquals(1, $stats['total_platforms']);
        $this->assertEquals(1, $stats['created_instances']);
        $this->assertEquals(1, $stats['cache_hits']);
    }

    /**
     * 测试工厂调试信息
     */
    public function testFactoryDebugInfo()
    {
        $this->factory->create('mock');
        
        $debugInfo = $this->factory->getDebugInfo();
        
        $this->assertTrue(is_array($debugInfo));
        $this->assertArrayHasKey('registered_platforms', $debugInfo);
        $this->assertArrayHasKey('cached_instances', $debugInfo);
        $this->assertArrayHasKey('creation_history', $debugInfo);
    }

    /**
     * 测试平台适配器的基本功能
     */
    public function testAdapterBasicFunctionality()
    {
        $adapter = $this->factory->create('mock');
        
        // 测试基本方法
        $this->assertEquals('mock', $adapter->getName());
        $this->assertGreaterThan(0, $adapter->getSingleLimit());
        $this->assertGreaterThan(0, $adapter->getHourlyLimit());
        
        // 测试参数验证
        $validParams = [
            'access_token' => 'test_token',
            'from_account' => 'test_from',
            'to_account' => 'test_to',
            'amount' => 100
        ];
        $this->assertTrue($adapter->validateParams($validParams));
    }
}