<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\platform;

use backendapi\services\promote\transfermoneyv2\platform\AdqAdapter;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use common\components\promoteData\Adq;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * AdqAdapter测试用例
 * 
 * 测试ADQ平台适配器的各种功能和边界情况
 * 基于现有Adq类的接口设计
 */
class AdqAdapterTest extends TestCase
{
    /**
     * @var AdqAdapter
     */
    private $adapter;

    /**
     * @var MockObject|Adq
     */
    private $mockAdq;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建Adq的Mock对象
        $this->mockAdq = $this->createMock(Adq::class);
        
        // 创建适配器实例
        $this->adapter = new AdqAdapter();
        
        // 注入Mock对象（需要在适配器中添加setter方法）
        if (method_exists($this->adapter, 'setAdqInstance')) {
            $this->adapter->setAdqInstance($this->mockAdq);
        }
    }

    /**
     * 测试适配器实现了正确的接口
     */
    public function testImplementsCorrectInterface()
    {
        $this->assertInstanceOf(PlatformAdapterInterface::class, $this->adapter);
    }

    /**
     * 测试获取平台名称
     */
    public function testGetName()
    {
        $this->assertEquals('adq', $this->adapter->getName());
    }

    /**
     * 测试获取单次充值限额
     */
    public function testGetSingleLimit()
    {
        $this->assertEquals(2000, $this->adapter->getSingleLimit());
    }

    /**
     * 测试获取小时充值限额
     */
    public function testGetHourlyLimit()
    {
        $this->assertEquals(20000, $this->adapter->getHourlyLimit());
    }

    /**
     * 测试成功的转账充值
     */
    public function testTransferMoneySuccess()
    {
        $accessToken = 'test_access_token';
        $organizationId = ''; // ADQ不需要organizationId
        $fromAccountId = '*********';
        $toAccountId = '*********';
        $amount = 1000;

        // 模拟Adq::subcustomerTransfer的成功响应
        $mockResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'transfer_serial' => 'ADQ20250123*********0',
                'external_bill_no' => '20250123*********0'
            ]
        ];

        $this->mockAdq
            ->expects($this->once())
            ->method('subcustomerTransfer')
            ->with(
                $accessToken,
                $fromAccountId,
                $toAccountId,
                $amount
            )
            ->willReturn($mockResponse);

        $result = $this->adapter->transferMoney(
            $accessToken,
            $organizationId,
            $fromAccountId,
            $toAccountId,
            $amount
        );

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['code']);
        $this->assertEquals('success', $result['message']);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('transfer_serial', $result['data']);
    }

    /**
     * 测试转账充值失败
     */
    public function testTransferMoneyFailure()
    {
        $accessToken = 'test_access_token';
        $organizationId = '';
        $fromAccountId = '*********';
        $toAccountId = '*********';
        $amount = 1000;

        // 模拟Adq::subcustomerTransfer的失败响应
        $mockResponse = [
            'code' => 1001,
            'message' => '账户余额不足',
            'data' => null
        ];

        $this->mockAdq
            ->expects($this->once())
            ->method('subcustomerTransfer')
            ->with(
                $accessToken,
                $fromAccountId,
                $toAccountId,
                $amount
            )
            ->willReturn($mockResponse);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('ADQ转账失败[1001]: 账户余额不足');

        $this->adapter->transferMoney(
            $accessToken,
            $organizationId,
            $fromAccountId,
            $toAccountId,
            $amount
        );
    }

    /**
     * 测试转账金额验证
     */
    public function testTransferMoneyAmountValidation()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('充值金额必须大于0');

        $this->adapter->transferMoney(
            'test_token',
            '',
            '*********',
            '*********',
            0
        );
    }

    /**
     * 测试转账金额超过单次限额
     */
    public function testTransferMoneyExceedsSingleLimit()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('充值金额超过单次限额2000元');

        $this->adapter->transferMoney(
            'test_token',
            '',
            '*********',
            '*********',
            2500
        );
    }

    /**
     * 测试成功获取账户余额
     */
    public function testGetBalanceSuccess()
    {
        $accessToken = 'test_access_token';
        $accountId = '*********';

        // 模拟Adq::getBalance的成功响应
        $mockResponse = [
            [
                'fund_type' => 'FUND_TYPE_CREDIT_ROLL',
                'balance' => 100000, // 1000.00元，以分为单位
                'fund_status' => 'FUND_STATUS_VALID'
            ],
            [
                'fund_type' => 'FUND_TYPE_PREPAY',
                'balance' => 50000, // 500.00元，以分为单位
                'fund_status' => 'FUND_STATUS_VALID'
            ]
        ];

        $this->mockAdq
            ->expects($this->once())
            ->method('getBalance')
            ->with($accessToken, $accountId, '', '')
            ->willReturn($mockResponse);

        $balance = $this->adapter->getBalance($accessToken, $accountId);

        $this->assertEquals(1500.0, $balance); // 总余额1500元
    }

    /**
     * 测试获取账户余额失败
     */
    public function testGetBalanceFailure()
    {
        $accessToken = 'test_access_token';
        $accountId = '*********';

        // 模拟Adq::getBalance抛出异常
        $this->mockAdq
            ->expects($this->once())
            ->method('getBalance')
            ->with($accessToken, $accountId, '', '')
            ->willThrowException(new \Exception('账户不存在'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('查询ADQ账户余额失败: 账户不存在');

        $this->adapter->getBalance($accessToken, $accountId);
    }

    /**
     * 测试参数验证成功
     */
    public function testValidateParamsSuccess()
    {
        $params = [
            'access_token' => 'test_token',
            'from_account' => '*********',
            'to_account' => '*********',
            'amount' => 1000
        ];

        $result = $this->adapter->validateParams($params);
        $this->assertTrue($result);
    }

    /**
     * 测试参数验证成功 - 包含organization_id（ADQ会忽略）
     */
    public function testValidateParamsSuccessWithOrganizationId()
    {
        $params = [
            'access_token' => 'test_token',
            'organization_id' => '*********', // ADQ不需要但不会报错
            'from_account' => '*********',
            'to_account' => '*********',
            'amount' => 1000
        ];

        $result = $this->adapter->validateParams($params);
        $this->assertTrue($result);
    }

    /**
     * 测试参数验证失败 - 缺少必要参数
     */
    public function testValidateParamsMissingRequired()
    {
        $params = [
            'access_token' => 'test_token',
            // 缺少from_account
            'to_account' => '*********',
            'amount' => 1000
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('缺少必要参数: from_account');

        $this->adapter->validateParams($params);
    }

    /**
     * 测试参数验证失败 - 参数为空
     */
    public function testValidateParamsEmptyValue()
    {
        $params = [
            'access_token' => '',
            'from_account' => '*********',
            'to_account' => '*********',
            'amount' => 1000
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('缺少必要参数: access_token');

        $this->adapter->validateParams($params);
    }

    /**
     * 测试错误信息格式化
     */
    public function testFormatError()
    {
        $response = [
            'code' => 1001,
            'message' => '账户余额不足',
            'details' => '当前余额: 100元'
        ];

        $formattedError = $this->adapter->formatError($response);
        $this->assertEquals('ADQ错误[1001]: 账户余额不足 - 当前余额: 100元', $formattedError);
    }

    /**
     * 测试错误信息格式化 - 无详细信息
     */
    public function testFormatErrorWithoutDetails()
    {
        $response = [
            'code' => 1001,
            'message' => '账户余额不足'
        ];

        $formattedError = $this->adapter->formatError($response);
        $this->assertEquals('ADQ错误[1001]: 账户余额不足', $formattedError);
    }

    /**
     * 测试错误信息格式化 - 未知错误
     */
    public function testFormatErrorUnknown()
    {
        $response = [];

        $formattedError = $this->adapter->formatError($response);
        $this->assertEquals('ADQ错误[unknown]: 未知错误', $formattedError);
    }
}