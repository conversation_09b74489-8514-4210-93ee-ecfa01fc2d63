<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\platform;

use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter;
use backendapi\services\promote\transfermoneyv2\platform\AdqAdapter;
use backendapi\services\promote\transfermoneyv2\platform\MockAdapter;
use PHPUnit\Framework\TestCase;

/**
 * 平台适配器集成测试
 * 
 * 测试平台工厂和各个适配器的集成功能
 * 验证整个平台适配器系统的正确性
 */
class PlatformIntegrationTest extends TestCase
{
    /**
     * @var PlatformFactory
     */
    private $factory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new PlatformFactory();
    }

    /**
     * 测试工厂支持所有预期的平台
     */
    public function testFactorySupportedPlatforms()
    {
        $supportedPlatforms = $this->factory->getSupportedPlatforms();
        
        $this->assertContains('mock', $supportedPlatforms);
        $this->assertContains('tiktok', $supportedPlatforms);
        $this->assertContains('adq', $supportedPlatforms);
        $this->assertCount(3, $supportedPlatforms);
    }

    /**
     * 测试创建Mock适配器
     */
    public function testCreateMockAdapter()
    {
        $adapter = $this->factory->create('mock');
        
        $this->assertInstanceOf(MockAdapter::class, $adapter);
        $this->assertInstanceOf(PlatformAdapterInterface::class, $adapter);
        $this->assertEquals('mock', $adapter->getName());
        $this->assertEquals(10000, $adapter->getSingleLimit());
        $this->assertEquals(50000, $adapter->getHourlyLimit());
    }

    /**
     * 测试创建抖音适配器
     */
    public function testCreateTiktokAdapter()
    {
        $adapter = $this->factory->create('tiktok');
        
        $this->assertInstanceOf(TiktokAdapter::class, $adapter);
        $this->assertInstanceOf(PlatformAdapterInterface::class, $adapter);
        $this->assertEquals('tiktok', $adapter->getName());
        $this->assertEquals(1000, $adapter->getSingleLimit());
        $this->assertEquals(3000, $adapter->getHourlyLimit());
    }

    /**
     * 测试创建ADQ适配器
     */
    public function testCreateAdqAdapter()
    {
        $adapter = $this->factory->create('adq');
        
        $this->assertInstanceOf(AdqAdapter::class, $adapter);
        $this->assertInstanceOf(PlatformAdapterInterface::class, $adapter);
        $this->assertEquals('adq', $adapter->getName());
        $this->assertEquals(2000, $adapter->getSingleLimit());
        $this->assertEquals(20000, $adapter->getHourlyLimit());
    }

    /**
     * 测试批量创建适配器
     */
    public function testBatchCreateAdapters()
    {
        $platforms = ['mock', 'tiktok', 'adq'];
        $adapters = $this->factory->batchCreate($platforms);
        
        $this->assertCount(3, $adapters);
        $this->assertInstanceOf(MockAdapter::class, $adapters['mock']);
        $this->assertInstanceOf(TiktokAdapter::class, $adapters['tiktok']);
        $this->assertInstanceOf(AdqAdapter::class, $adapters['adq']);
    }

    /**
     * 测试所有适配器的参数验证功能
     */
    public function testAllAdaptersValidateParams()
    {
        $platforms = ['mock', 'tiktok', 'adq'];
        
        foreach ($platforms as $platform) {
            $adapter = $this->factory->create($platform);
            
            // 测试基本参数验证
            $validParams = [
                'access_token' => 'test_token',
                'organization_id' => '*********',
                'from_account' => '*********',
                'to_account' => '*********',
                'amount' => 500
            ];
            
            $this->assertTrue($adapter->validateParams($validParams), 
                "平台 {$platform} 的参数验证应该通过");
            
            // 测试缺少参数的情况
            $invalidParams = $validParams;
            unset($invalidParams['access_token']);
            
            $this->expectException(\Exception::class);
            $adapter->validateParams($invalidParams);
        }
    }

    /**
     * 测试所有适配器的错误格式化功能
     */
    public function testAllAdaptersFormatError()
    {
        $platforms = ['mock', 'tiktok', 'adq'];
        $errorResponse = [
            'code' => 1001,
            'message' => '测试错误',
            'details' => '详细信息'
        ];
        
        foreach ($platforms as $platform) {
            $adapter = $this->factory->create($platform);
            $formattedError = $adapter->formatError($errorResponse);
            
            $this->assertStringContainsString('1001', $formattedError);
            $this->assertStringContainsString('测试错误', $formattedError);
            $this->assertStringContainsString('详细信息', $formattedError);
        }
    }

    /**
     * 测试适配器限额配置的正确性
     */
    public function testAdapterLimitsConfiguration()
    {
        // 抖音平台限额：单次1000元/小时3000元
        $tiktokAdapter = $this->factory->create('tiktok');
        $this->assertEquals(1000, $tiktokAdapter->getSingleLimit());
        $this->assertEquals(3000, $tiktokAdapter->getHourlyLimit());
        
        // ADQ平台限额：单次2000元/小时20000元
        $adqAdapter = $this->factory->create('adq');
        $this->assertEquals(2000, $adqAdapter->getSingleLimit());
        $this->assertEquals(20000, $adqAdapter->getHourlyLimit());
        
        // Mock平台限额：单次10000元/小时50000元
        $mockAdapter = $this->factory->create('mock');
        $this->assertEquals(10000, $mockAdapter->getSingleLimit());
        $this->assertEquals(50000, $mockAdapter->getHourlyLimit());
    }

    /**
     * 测试工厂缓存功能
     */
    public function testFactoryCaching()
    {
        // 第一次创建
        $adapter1 = $this->factory->create('mock');
        $this->assertTrue($this->factory->isCached('mock'));
        
        // 第二次创建应该返回相同实例
        $adapter2 = $this->factory->create('mock');
        $this->assertSame($adapter1, $adapter2);
        
        // 强制创建新实例
        $adapter3 = $this->factory->create('mock', true);
        $this->assertNotSame($adapter1, $adapter3);
    }

    /**
     * 测试工厂统计功能
     */
    public function testFactoryStatistics()
    {
        $initialStats = $this->factory->getStatistics();
        $this->assertEquals(0, $initialStats['created_instances']);
        
        // 创建几个适配器
        $this->factory->create('mock');
        $this->factory->create('tiktok');
        $this->factory->create('adq');
        
        $stats = $this->factory->getStatistics();
        $this->assertEquals(3, $stats['created_instances']);
        $this->assertEquals(3, $stats['total_platforms']);
        $this->assertEquals(3, $stats['cached_instances']);
    }

    /**
     * 测试不支持的平台
     */
    public function testUnsupportedPlatform()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('不支持的平台类型: unsupported');
        
        $this->factory->create('unsupported');
    }

    /**
     * 测试工厂重置功能
     */
    public function testFactoryReset()
    {
        // 创建一些适配器
        $this->factory->create('mock');
        $this->factory->create('tiktok');
        
        $this->assertTrue($this->factory->isCached('mock'));
        $this->assertTrue($this->factory->isCached('tiktok'));
        
        // 重置工厂
        $this->factory->reset();
        
        $this->assertFalse($this->factory->isCached('mock'));
        $this->assertFalse($this->factory->isCached('tiktok'));
        
        $stats = $this->factory->getStatistics();
        $this->assertEquals(0, $stats['created_instances']);
        $this->assertEquals(0, $stats['cached_instances']);
    }

    /**
     * 测试清除特定平台缓存
     */
    public function testClearSpecificPlatformCache()
    {
        $this->factory->create('mock');
        $this->factory->create('tiktok');
        
        $this->assertTrue($this->factory->isCached('mock'));
        $this->assertTrue($this->factory->isCached('tiktok'));
        
        // 清除mock平台缓存
        $this->factory->clearCache('mock');
        
        $this->assertFalse($this->factory->isCached('mock'));
        $this->assertTrue($this->factory->isCached('tiktok'));
    }

    /**
     * 测试获取适配器配置
     */
    public function testGetAdapterConfig()
    {
        $mockConfig = $this->factory->getAdapterConfig('mock');
        $this->assertEquals(MockAdapter::class, $mockConfig['class']);
        $this->assertEquals('Mock平台适配器', $mockConfig['name']);
        
        $tiktokConfig = $this->factory->getAdapterConfig('tiktok');
        $this->assertEquals(TiktokAdapter::class, $tiktokConfig['class']);
        $this->assertEquals('抖音平台适配器', $tiktokConfig['name']);
        
        $adqConfig = $this->factory->getAdapterConfig('adq');
        $this->assertEquals(AdqAdapter::class, $adqConfig['class']);
        $this->assertEquals('ADQ平台适配器', $adqConfig['name']);
    }

    /**
     * 测试适配器类验证
     */
    public function testValidateAdapterClass()
    {
        $this->assertTrue($this->factory->validateAdapterClass(MockAdapter::class));
        $this->assertTrue($this->factory->validateAdapterClass(TiktokAdapter::class));
        $this->assertTrue($this->factory->validateAdapterClass(AdqAdapter::class));
        
        $this->assertFalse($this->factory->validateAdapterClass('NonExistentClass'));
        $this->assertFalse($this->factory->validateAdapterClass(\stdClass::class));
    }
}