<?php

namespace backendapi\tests\unit\promote\transfermoneyv2;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\TransferMoneyBatchService;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use backendapi\services\promote\transfermoneyv2\workflow\AutoRechargeWorkflow;
use backendapi\services\promote\transfermoneyv2\workflow\QueueExecutionWorkflow;
use common\queues\TransferMoneyJobV2;
use common\queues\TransferMoneyJob;
use Codeception\Test\Unit;
use Yii;
use Exception;

/**
 * 性能测试和基准对比
 * 
 * 测试内容包括：
 * 1. 单次充值性能基准测试
 * 2. 批量充值性能基准测试
 * 3. 并发充值性能测试
 * 4. 内存使用和资源消耗测试
 * 5. 新旧系统性能对比
 * 6. 缓存性能测试
 * 7. 配置加载性能测试
 */
class PerformanceTest extends Unit
{
    /**
     * @var TransferMoneyServiceV2 新服务
     */
    private $newService;

    /**
     * @var TransferMoneyBatchService 旧服务
     */
    private $oldService;

    /**
     * @var array 性能测试结果
     */
    private $performanceResults = [];

    /**
     * @var int 测试迭代次数
     */
    private $iterations = 100;

    /**
     * @var array 测试数据集
     */
    private $testDataSets;

    /**
     * 设置测试环境
     */
    protected function _before()
    {
        parent::_before();
        
        // 创建Mock缓存组件
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->method('get')->willReturn(false);
        $mockCache->method('set')->willReturn(true);
        $mockCache->method('delete')->willReturn(true);
        
        // 初始化新服务
        $platformFactory = new PlatformFactory();
        $cacheManager = new TransferCacheManager($mockCache);
        $this->newService = new TransferMoneyServiceV2($platformFactory, $cacheManager);
        
        // 初始化旧服务
        $this->oldService = new TransferMoneyBatchService();
        
        // 准备测试数据集
        $this->prepareTestDataSets();
        
        // 初始化性能结果记录
        $this->performanceResults = [
            'single_recharge' => [],
            'batch_recharge' => [],
            'concurrent_recharge' => [],
            'memory_usage' => [],
            'cache_performance' => [],
            'config_loading' => []
        ];
    }

    /**
     * 清理测试环境
     */
    protected function _after()
    {
        parent::_after();
        
        // 输出性能测试报告
        $this->outputPerformanceReport();
    }

    /**
     * 准备测试数据集
     */
    private function prepareTestDataSets()
    {
        $this->testDataSets = [
            'single_account' => [
                'user_id' => 1,
                'user_name' => '性能测试用户',
                'data' => "账户ID：**********\n转账金额：100"
            ],
            'small_batch' => [
                'user_id' => 1,
                'user_name' => '性能测试用户',
                'data' => "账户ID：" . implode('、', array_fill(0, 5, '**********')) . "\n转账金额：100"
            ],
            'medium_batch' => [
                'user_id' => 1,
                'user_name' => '性能测试用户',
                'data' => "账户ID：" . implode('、', array_fill(0, 20, '**********')) . "\n转账金额：100"
            ],
            'large_batch' => [
                'user_id' => 1,
                'user_name' => '性能测试用户',
                'data' => "账户ID：" . implode('、', array_fill(0, 50, '**********')) . "\n转账金额：100"
            ],
            'queue_data_single' => [
                'target_advertiser_ids' => ['**********'],
                'amount' => 100,
                'user_name' => '性能测试用户'
            ],
            'queue_data_batch' => [
                'target_advertiser_ids' => array_fill(0, 20, '**********'),
                'amount' => 100,
                'user_name' => '性能测试用户'
            ]
        ];
    }

    /**
     * 测试单次充值性能基准
     */
    public function testSingleRechargePerformance()
    {
        $testData = $this->testDataSets['single_account'];
        
        // 1. 测试新服务性能
        $newServiceTimes = [];
        for ($i = 0; $i < $this->iterations; $i++) {
            $startTime = microtime(true);
            $startMemory = memory_get_usage();
            
            try {
                $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->newService);
                $result = $autoRechargeWorkflow->execute($testData);
            } catch (Exception $e) {
                // 记录异常但继续测试
            }
            
            $endTime = microtime(true);
            $endMemory = memory_get_usage();
            
            $newServiceTimes[] = [
                'duration' => $endTime - $startTime,
                'memory' => $endMemory - $startMemory,
                'peak_memory' => memory_get_peak_usage() - memory_get_peak_usage(true)
            ];
        }

        // 2. 测试旧服务性能（如果可用）
        $oldServiceTimes = [];
        for ($i = 0; $i < $this->iterations; $i++) {
            $startTime = microtime(true);
            $startMemory = memory_get_usage();
            
            try {
                $result = $this->oldService->run($testData);
            } catch (Exception $e) {
                // 记录异常但继续测试
            }
            
            $endTime = microtime(true);
            $endMemory = memory_get_usage();
            
            $oldServiceTimes[] = [
                'duration' => $endTime - $startTime,
                'memory' => $endMemory - $startMemory,
                'peak_memory' => memory_get_peak_usage() - memory_get_peak_usage(true)
            ];
        }

        // 3. 计算统计数据
        $newStats = $this->calculateStatistics($newServiceTimes);
        $oldStats = $this->calculateStatistics($oldServiceTimes);

        // 4. 记录性能结果
        $this->performanceResults['single_recharge'] = [
            'new_service' => $newStats,
            'old_service' => $oldStats,
            'improvement' => [
                'duration' => ($oldStats['avg_duration'] - $newStats['avg_duration']) / $oldStats['avg_duration'] * 100,
                'memory' => ($oldStats['avg_memory'] - $newStats['avg_memory']) / $oldStats['avg_memory'] * 100
            ]
        ];

        // 5. 性能断言
        $this->assertLessThan(1.0, $newStats['avg_duration'], '单次充值平均执行时间应少于1秒');
        $this->assertLessThan(10 * 1024 * 1024, $newStats['avg_memory'], '单次充值平均内存使用应少于10MB');
        
        // 新服务性能不应显著劣于旧服务（允许20%的性能差异）
        if ($oldStats['avg_duration'] > 0) {
            $performanceRatio = $newStats['avg_duration'] / $oldStats['avg_duration'];
            $this->assertLessThan(1.2, $performanceRatio, '新服务性能不应显著劣于旧服务');
        }
    }

    /**
     * 测试批量充值性能基准
     */
    public function testBatchRechargePerformance()
    {
        $batchSizes = ['small_batch', 'medium_batch', 'large_batch'];
        
        foreach ($batchSizes as $batchSize) {
            $testData = $this->testDataSets[$batchSize];
            
            // 测试新服务批量性能
            $newBatchTimes = [];
            for ($i = 0; $i < min(20, $this->iterations); $i++) { // 批量测试减少迭代次数
                $startTime = microtime(true);
                $startMemory = memory_get_usage();
                
                try {
                    $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->newService);
                    $result = $autoRechargeWorkflow->execute($testData);
                } catch (Exception $e) {
                    // 记录异常但继续测试
                }
                
                $endTime = microtime(true);
                $endMemory = memory_get_usage();
                
                $newBatchTimes[] = [
                    'duration' => $endTime - $startTime,
                    'memory' => $endMemory - $startMemory
                ];
            }

            // 计算批量处理统计
            $batchStats = $this->calculateStatistics($newBatchTimes);
            $this->performanceResults['batch_recharge'][$batchSize] = $batchStats;

            // 批量处理性能断言
            $expectedMaxTime = $batchSize === 'large_batch' ? 10.0 : 5.0;
            $this->assertLessThan($expectedMaxTime, $batchStats['avg_duration'], 
                "{$batchSize} 批量充值平均执行时间应少于{$expectedMaxTime}秒");
        }
    }

    /**
     * 测试并发充值性能
     */
    public function testConcurrentRechargePerformance()
    {
        $concurrentCount = 10;
        $testData = $this->testDataSets['single_account'];
        
        // 模拟并发执行
        $concurrentResults = [];
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        for ($i = 0; $i < $concurrentCount; $i++) {
            $iterationStartTime = microtime(true);
            
            try {
                $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->newService);
                $result = $autoRechargeWorkflow->execute($testData);
                $success = true;
            } catch (Exception $e) {
                $success = false;
            }
            
            $iterationEndTime = microtime(true);
            
            $concurrentResults[] = [
                'success' => $success,
                'duration' => $iterationEndTime - $iterationStartTime,
                'iteration' => $i
            ];
        }
        
        $totalTime = microtime(true) - $startTime;
        $totalMemory = memory_get_usage() - $startMemory;
        
        // 计算并发性能统计
        $successCount = array_sum(array_column($concurrentResults, 'success'));
        $avgDuration = array_sum(array_column($concurrentResults, 'duration')) / $concurrentCount;
        
        $this->performanceResults['concurrent_recharge'] = [
            'concurrent_count' => $concurrentCount,
            'success_count' => $successCount,
            'success_rate' => $successCount / $concurrentCount * 100,
            'total_time' => $totalTime,
            'avg_iteration_time' => $avgDuration,
            'total_memory' => $totalMemory,
            'throughput' => $concurrentCount / $totalTime
        ];

        // 并发性能断言
        $this->assertGreaterThan(0.8, $successCount / $concurrentCount, '并发成功率应大于80%');
        $this->assertLessThan(20.0, $totalTime, '并发执行总时间应少于20秒');
        $this->assertGreaterThan(0.5, $concurrentCount / $totalTime, '吞吐量应大于0.5次/秒');
    }

    /**
     * 测试内存使用和资源消耗
     */
    public function testMemoryUsageAndResourceConsumption()
    {
        $testScenarios = [
            'single' => $this->testDataSets['single_account'],
            'batch' => $this->testDataSets['medium_batch']
        ];

        foreach ($testScenarios as $scenario => $testData) {
            $memorySnapshots = [];
            
            // 记录初始内存状态
            $initialMemory = memory_get_usage();
            $initialPeakMemory = memory_get_peak_usage();
            
            // 执行多次操作并记录内存使用
            for ($i = 0; $i < 50; $i++) {
                $beforeMemory = memory_get_usage();
                
                try {
                    $autoRechargeWorkflow = AutoRechargeWorkflow::createWithService($this->newService);
                    $result = $autoRechargeWorkflow->execute($testData);
                } catch (Exception $e) {
                    // 继续测试
                }
                
                $afterMemory = memory_get_usage();
                $peakMemory = memory_get_peak_usage();
                
                $memorySnapshots[] = [
                    'iteration' => $i,
                    'before' => $beforeMemory,
                    'after' => $afterMemory,
                    'peak' => $peakMemory,
                    'used' => $afterMemory - $beforeMemory
                ];
                
                // 每10次迭代强制垃圾回收
                if ($i % 10 === 0) {
                    gc_collect_cycles();
                }
            }
            
            $finalMemory = memory_get_usage();
            $finalPeakMemory = memory_get_peak_usage();
            
            // 计算内存使用统计
            $memoryStats = [
                'initial_memory' => $initialMemory,
                'final_memory' => $finalMemory,
                'memory_growth' => $finalMemory - $initialMemory,
                'peak_memory' => $finalPeakMemory,
                'peak_growth' => $finalPeakMemory - $initialPeakMemory,
                'avg_iteration_memory' => array_sum(array_column($memorySnapshots, 'used')) / count($memorySnapshots),
                'max_iteration_memory' => max(array_column($memorySnapshots, 'used')),
                'memory_leak_indicator' => ($finalMemory - $initialMemory) / 50 // 每次迭代的平均内存增长
            ];
            
            $this->performanceResults['memory_usage'][$scenario] = $memoryStats;
            
            // 内存使用断言
            $this->assertLessThan(100 * 1024 * 1024, $memoryStats['peak_memory'], 
                "{$scenario} 场景峰值内存使用应少于100MB");
            $this->assertLessThan(1024 * 1024, $memoryStats['memory_leak_indicator'], 
                "{$scenario} 场景每次迭代内存增长应少于1MB（内存泄漏检测）");
        }
    }

    /**
     * 测试缓存性能
     */
    public function testCachePerformance()
    {
        $cacheOperations = 1000;
        $targetIds = array_map(function($i) { return "test_account_{$i}"; }, range(1, 100));
        
        // 测试缓存写入性能
        $writeStartTime = microtime(true);
        for ($i = 0; $i < $cacheOperations; $i++) {
            $targetId = $targetIds[$i % count($targetIds)];
            $this->newService->getCacheManager()->recordSuccessfulTransfer($targetId, 100, '测试用户');
        }
        $writeEndTime = microtime(true);
        $writeTime = $writeEndTime - $writeStartTime;
        
        // 测试缓存读取性能
        $readStartTime = microtime(true);
        for ($i = 0; $i < $cacheOperations; $i++) {
            $targetId = $targetIds[$i % count($targetIds)];
            $this->newService->getCacheManager()->getTransferHistory($targetId);
        }
        $readEndTime = microtime(true);
        $readTime = $readEndTime - $readStartTime;
        
        // 测试缓存命中率（模拟）
        $hitCount = 0;
        $missCount = 0;
        for ($i = 0; $i < 100; $i++) {
            $targetId = $targetIds[$i];
            try {
                $history = $this->newService->getCacheManager()->getTransferHistory($targetId);
                if (!empty($history)) {
                    $hitCount++;
                } else {
                    $missCount++;
                }
            } catch (Exception $e) {
                $missCount++;
            }
        }
        
        $this->performanceResults['cache_performance'] = [
            'write_operations' => $cacheOperations,
            'write_time' => $writeTime,
            'write_ops_per_second' => $cacheOperations / $writeTime,
            'read_operations' => $cacheOperations,
            'read_time' => $readTime,
            'read_ops_per_second' => $cacheOperations / $readTime,
            'cache_hit_rate' => $hitCount / ($hitCount + $missCount) * 100,
            'total_cache_operations' => $hitCount + $missCount
        ];
        
        // 缓存性能断言
        $this->assertGreaterThan(100, $cacheOperations / $writeTime, '缓存写入速度应大于100次/秒');
        $this->assertGreaterThan(500, $cacheOperations / $readTime, '缓存读取速度应大于500次/秒');
    }

    /**
     * 测试配置加载性能
     */
    public function testConfigLoadingPerformance()
    {
        $configLoadIterations = 100;
        
        // 测试配置加载性能
        $configLoadTimes = [];
        for ($i = 0; $i < $configLoadIterations; $i++) {
            $startTime = microtime(true);
            
            // 清除配置缓存
            \backendapi\services\promote\transfermoneyv2\config\ConfigManager::clearCache();
            
            // 重新加载配置
            $platformConfig = \backendapi\services\promote\transfermoneyv2\config\ConfigManager::getPlatformConfig();
            $limitsConfig = \backendapi\services\promote\transfermoneyv2\config\ConfigManager::getTransferLimitsConfig();
            $budgetConfig = \backendapi\services\promote\transfermoneyv2\config\ConfigManager::getBudgetRulesConfig();
            
            $endTime = microtime(true);
            $configLoadTimes[] = $endTime - $startTime;
        }
        
        // 测试配置缓存性能
        $cachedLoadTimes = [];
        for ($i = 0; $i < $configLoadIterations; $i++) {
            $startTime = microtime(true);
            
            // 使用缓存的配置
            $platformConfig = \backendapi\services\promote\transfermoneyv2\config\ConfigManager::getPlatformConfig();
            $limitsConfig = \backendapi\services\promote\transfermoneyv2\config\ConfigManager::getTransferLimitsConfig();
            $budgetConfig = \backendapi\services\promote\transfermoneyv2\config\ConfigManager::getBudgetRulesConfig();
            
            $endTime = microtime(true);
            $cachedLoadTimes[] = $endTime - $startTime;
        }
        
        $avgConfigLoadTime = array_sum($configLoadTimes) / count($configLoadTimes);
        $avgCachedLoadTime = array_sum($cachedLoadTimes) / count($cachedLoadTimes);
        
        $this->performanceResults['config_loading'] = [
            'cold_load_time' => $avgConfigLoadTime,
            'cached_load_time' => $avgCachedLoadTime,
            'cache_improvement' => ($avgConfigLoadTime - $avgCachedLoadTime) / $avgConfigLoadTime * 100,
            'cold_load_ops_per_second' => 1 / $avgConfigLoadTime,
            'cached_load_ops_per_second' => 1 / $avgCachedLoadTime
        ];
        
        // 配置加载性能断言
        $this->assertLessThan(0.1, $avgConfigLoadTime, '配置冷加载时间应少于0.1秒');
        $this->assertLessThan(0.01, $avgCachedLoadTime, '配置缓存加载时间应少于0.01秒');
        $this->assertGreaterThan(50, ($avgConfigLoadTime - $avgCachedLoadTime) / $avgConfigLoadTime * 100, 
            '配置缓存应提供至少50%的性能提升');
    }

    /**
     * 测试队列任务性能
     */
    public function testQueueJobPerformance()
    {
        $jobIterations = 50;
        $testData = $this->testDataSets['queue_data_single'];
        
        // 测试新队列任务性能
        $newJobTimes = [];
        for ($i = 0; $i < $jobIterations; $i++) {
            $startTime = microtime(true);
            
            $job = new TransferMoneyJobV2(['data' => $testData]);
            $jobKey = $job->getJobKey();
            $service = $job->getTransferService();
            
            $endTime = microtime(true);
            $newJobTimes[] = $endTime - $startTime;
        }
        
        // 测试旧队列任务性能
        $oldJobTimes = [];
        for ($i = 0; $i < $jobIterations; $i++) {
            $startTime = microtime(true);
            
            $job = new TransferMoneyJob(['data' => $testData]);
            // 执行基本操作
            
            $endTime = microtime(true);
            $oldJobTimes[] = $endTime - $startTime;
        }
        
        $newJobStats = $this->calculateStatistics($newJobTimes);
        $oldJobStats = $this->calculateStatistics($oldJobTimes);
        
        $this->performanceResults['queue_job_performance'] = [
            'new_job' => $newJobStats,
            'old_job' => $oldJobStats,
            'performance_ratio' => $newJobStats['avg_duration'] / $oldJobStats['avg_duration']
        ];
        
        // 队列任务性能断言
        $this->assertLessThan(0.1, $newJobStats['avg_duration'], '新队列任务创建时间应少于0.1秒');
        $this->assertLessThan(2.0, $newJobStats['avg_duration'] / $oldJobStats['avg_duration'], 
            '新队列任务性能不应显著劣于旧任务');
    }

    /**
     * 计算统计数据
     * 
     * @param array $data 时间数据数组
     * @return array 统计结果
     */
    private function calculateStatistics(array $data): array
    {
        if (empty($data)) {
            return [
                'avg_duration' => 0,
                'min_duration' => 0,
                'max_duration' => 0,
                'avg_memory' => 0,
                'total_samples' => 0
            ];
        }

        $durations = array_column($data, 'duration');
        $memories = array_column($data, 'memory');
        
        return [
            'avg_duration' => array_sum($durations) / count($durations),
            'min_duration' => min($durations),
            'max_duration' => max($durations),
            'median_duration' => $this->calculateMedian($durations),
            'std_deviation' => $this->calculateStandardDeviation($durations),
            'avg_memory' => !empty($memories) ? array_sum($memories) / count($memories) : 0,
            'max_memory' => !empty($memories) ? max($memories) : 0,
            'total_samples' => count($data)
        ];
    }

    /**
     * 计算中位数
     * 
     * @param array $values 数值数组
     * @return float 中位数
     */
    private function calculateMedian(array $values): float
    {
        sort($values);
        $count = count($values);
        
        if ($count % 2 === 0) {
            return ($values[$count / 2 - 1] + $values[$count / 2]) / 2;
        } else {
            return $values[intval($count / 2)];
        }
    }

    /**
     * 计算标准差
     * 
     * @param array $values 数值数组
     * @return float 标准差
     */
    private function calculateStandardDeviation(array $values): float
    {
        $mean = array_sum($values) / count($values);
        $squaredDifferences = array_map(function($value) use ($mean) {
            return pow($value - $mean, 2);
        }, $values);
        
        return sqrt(array_sum($squaredDifferences) / count($values));
    }

    /**
     * 输出性能测试报告
     */
    private function outputPerformanceReport(): void
    {
        $report = "\n" . str_repeat("=", 80) . "\n";
        $report .= "性能测试报告\n";
        $report .= str_repeat("=", 80) . "\n";
        
        foreach ($this->performanceResults as $category => $results) {
            $report .= "\n" . ucfirst(str_replace('_', ' ', $category)) . ":\n";
            $report .= str_repeat("-", 40) . "\n";
            
            if (is_array($results)) {
                $report .= $this->formatResults($results, 1);
            }
        }
        
        $report .= "\n" . str_repeat("=", 80) . "\n";
        
        // 输出到日志或控制台
        Yii::info($report, 'performance_test');
        
        // 也可以写入文件
        file_put_contents(
            Yii::getAlias('@runtime/logs/performance_test_' . date('Y-m-d_H-i-s') . '.log'),
            $report
        );
    }

    /**
     * 格式化结果输出
     * 
     * @param array $results 结果数组
     * @param int $indent 缩进级别
     * @return string 格式化的字符串
     */
    private function formatResults(array $results, int $indent = 0): string
    {
        $output = '';
        $indentStr = str_repeat('  ', $indent);
        
        foreach ($results as $key => $value) {
            if (is_array($value)) {
                $output .= $indentStr . $key . ":\n";
                $output .= $this->formatResults($value, $indent + 1);
            } else {
                if (is_float($value)) {
                    $value = number_format($value, 6);
                }
                $output .= $indentStr . $key . ": " . $value . "\n";
            }
        }
        
        return $output;
    }
}