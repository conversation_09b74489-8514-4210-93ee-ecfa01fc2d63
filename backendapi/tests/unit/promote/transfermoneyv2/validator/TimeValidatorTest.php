<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\validator;

use backendapi\services\promote\transfermoneyv2\validator\TimeValidator;
use backendapi\services\promote\transfermoneyv2\validator\ValidatorInterface;
use PHPUnit\Framework\TestCase;

/**
 * 时间验证器测试用例
 * 
 * 测试时间验证器的各种功能和边界情况
 * 基于现有TransferMoneyBatchService::timeLimit()方法逻辑
 */
class TimeValidatorTest extends TestCase
{
    /**
     * @var TimeValidator
     */
    private $validator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->validator = new TimeValidator();
    }

    /**
     * 测试验证器实现了正确的接口
     */
    public function testImplementsCorrectInterface()
    {
        $this->assertInstanceOf(ValidatorInterface::class, $this->validator);
    }

    /**
     * 测试获取验证器名称
     */
    public function testGetName()
    {
        $this->assertEquals('time', $this->validator->getName());
    }

    /**
     * 测试允许的时间段验证通过
     */
    public function testValidateSuccessInAllowedTime()
    {
        // 模拟当前时间为上午10:00（允许充值的时间）
        $validator = new TimeValidator('10:00');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试禁止时间段验证失败 - 凌晨2:00
     */
    public function testValidateFailureAtForbiddenTimeStart()
    {
        // 模拟当前时间为凌晨2:00（禁止充值的开始时间）
        $validator = new TimeValidator('02:00');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('"凌晨2点到6点30分"时间段不可充值');

        $validator->validate($data);
    }

    /**
     * 测试禁止时间段验证失败 - 凌晨6:30
     */
    public function testValidateFailureAtForbiddenTimeEnd()
    {
        // 模拟当前时间为凌晨6:30（禁止充值的结束时间）
        $validator = new TimeValidator('06:30');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('"凌晨2点到6点30分"时间段不可充值');

        $validator->validate($data);
    }

    /**
     * 测试禁止时间段验证失败 - 凌晨4:00
     */
    public function testValidateFailureAtForbiddenTimeMiddle()
    {
        // 模拟当前时间为凌晨4:00（禁止充值的中间时间）
        $validator = new TimeValidator('04:00');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('"凌晨2点到6点30分"时间段不可充值');

        $validator->validate($data);
    }

    /**
     * 测试边界时间验证通过 - 凌晨1:59
     */
    public function testValidateSuccessAtBoundaryTimeBefore()
    {
        // 模拟当前时间为凌晨1:59（禁止时间前1分钟）
        $validator = new TimeValidator('01:59');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试边界时间验证通过 - 凌晨6:31
     */
    public function testValidateSuccessAtBoundaryTimeAfter()
    {
        // 模拟当前时间为凌晨6:31（禁止时间后1分钟）
        $validator = new TimeValidator('06:31');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试自定义时间限制配置
     */
    public function testValidateWithCustomTimeConfig()
    {
        // 测试自定义时间限制：上午9:00-12:00禁止充值
        $validator = new TimeValidator('10:00', '09:00', '12:00');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('"上午9点到12点"时间段不可充值');

        $validator->validate($data);
    }

    /**
     * 测试自定义时间限制配置验证通过
     */
    public function testValidateSuccessWithCustomTimeConfig()
    {
        // 测试自定义时间限制：上午9:00-12:00禁止充值，当前时间8:00
        $validator = new TimeValidator('08:00', '09:00', '12:00');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试链式验证设置
     */
    public function testSetNext()
    {
        $nextValidator = $this->createMock(ValidatorInterface::class);
        
        $result = $this->validator->setNext($nextValidator);
        
        $this->assertInstanceOf(ValidatorInterface::class, $result);
        $this->assertSame($nextValidator, $result);
    }

    /**
     * 测试链式验证执行
     */
    public function testHandleWithNextValidator()
    {
        // 模拟当前时间为上午10:00（允许充值的时间）
        $validator = new TimeValidator('10:00');
        
        $nextValidator = $this->createMock(ValidatorInterface::class);
        $nextValidator->expects($this->once())
                     ->method('handle')
                     ->willReturn(true);
        
        $validator->setNext($nextValidator);
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $validator->handle($data);
        $this->assertTrue($result);
    }

    /**
     * 测试获取错误信息
     */
    public function testGetErrorMessage()
    {
        // 模拟当前时间为凌晨3:00（禁止充值时间）
        $validator = new TimeValidator('03:00');
        
        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        try {
            $validator->validate($data);
        } catch (\Exception $e) {
            // 验证异常被抛出
        }

        $this->assertEquals('"凌晨2点到6点30分"时间段不可充值', $validator->getErrorMessage());
    }
}