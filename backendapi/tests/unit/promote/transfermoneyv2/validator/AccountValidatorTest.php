<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\validator;

use backendapi\services\promote\transfermoneyv2\validator\AccountValidator;
use backendapi\services\promote\transfermoneyv2\validator\ValidatorInterface;
use PHPUnit\Framework\TestCase;

/**
 * 账户验证器测试用例
 * 
 * 测试账户验证器的各种功能和边界情况
 * 基于现有TransferMoneyBatchService::verificationAccount()方法逻辑
 */
class AccountValidatorTest extends TestCase
{
    /**
     * @var AccountValidator
     */
    private $validator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->validator = new AccountValidator();
    }

    /**
     * 测试验证器实现了正确的接口
     */
    public function testImplementsCorrectInterface()
    {
        $this->assertInstanceOf(ValidatorInterface::class, $this->validator);
    }

    /**
     * 测试获取验证器名称
     */
    public function testGetName()
    {
        $this->assertEquals('account', $this->validator->getName());
    }

    /**
     * 测试有效账户ID数组验证通过
     */
    public function testValidateSuccessWithValidAccountIds()
    {
        $data = [
            'target_advertiser_ids' => ['*********', '*********'],
            'amount' => 1000
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试有效账户ID字符串验证通过
     */
    public function testValidateSuccessWithValidAccountIdsString()
    {
        $data = [
            'target_advertiser_ids' => '*********、*********',
            'amount' => 1000
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试缺少账户ID参数验证失败
     */
    public function testValidateFailureWithMissingAccountIds()
    {
        $data = [
            'amount' => 1000
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('缺少必要参数: target_advertiser_ids');

        $this->validator->validate($data);
    }

    /**
     * 测试空账户ID验证失败
     */
    public function testValidateFailureWithEmptyAccountIds()
    {
        $data = [
            'target_advertiser_ids' => [],
            'amount' => 1000
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('账户ID不能为空');

        $this->validator->validate($data);
    }

    /**
     * 测试空字符串账户ID验证失败
     */
    public function testValidateFailureWithEmptyStringAccountIds()
    {
        $data = [
            'target_advertiser_ids' => '',
            'amount' => 1000
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('账户ID不能为空');

        $this->validator->validate($data);
    }

    /**
     * 测试包含空值的账户ID字符串验证通过（过滤空值后）
     */
    public function testValidateSuccessWithEmptyValuesInString()
    {
        $data = [
            'target_advertiser_ids' => '*********、、*********、',
            'amount' => 1000
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试带平台查询回调的同平台验证通过
     */
    public function testValidateSuccessWithSamePlatform()
    {
        // 创建模拟平台查询回调，返回同一平台
        $mockCallback = AccountValidator::createMockPlatformQueryCallback([
            '*********' => 'tiktok',
            '*********' => 'tiktok'
        ]);

        $this->validator->setPlatformQueryCallback($mockCallback);

        $data = [
            'target_advertiser_ids' => ['*********', '*********'],
            'amount' => 1000
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试带平台查询回调的多平台验证失败
     */
    public function testValidateFailureWithMultiplePlatforms()
    {
        // 创建模拟平台查询回调，返回不同平台
        $mockCallback = AccountValidator::createMockPlatformQueryCallback([
            '*********' => 'tiktok',
            '*********' => 'adq'
        ]);

        $this->validator->setPlatformQueryCallback($mockCallback);

        $data = [
            'target_advertiser_ids' => ['*********', '*********'],
            'amount' => 1000
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('不允许多平台账户充值');

        $this->validator->validate($data);
    }

    /**
     * 测试带平台查询回调但无法确定平台验证失败
     */
    public function testValidateFailureWithUnknownPlatform()
    {
        // 创建模拟平台查询回调，返回空平台
        $mockCallback = AccountValidator::createMockPlatformQueryCallback([]);

        $this->validator->setPlatformQueryCallback($mockCallback);

        $data = [
            'target_advertiser_ids' => ['*********', '*********'],
            'amount' => 1000
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('无法确定账户所属平台');

        $this->validator->validate($data);
    }

    /**
     * 测试设置和获取平台查询回调函数
     */
    public function testSetAndGetPlatformQueryCallback()
    {
        $this->assertNull($this->validator->getPlatformQueryCallback());

        $mockCallback = function() { return []; };
        $this->validator->setPlatformQueryCallback($mockCallback);
        
        $this->assertSame($mockCallback, $this->validator->getPlatformQueryCallback());
    }

    /**
     * 测试有效账户ID格式验证
     */
    public function testIsValidAccountIdFormat()
    {
        // 有效格式
        $this->assertTrue(AccountValidator::isValidAccountIdFormat('*********'));
        $this->assertTrue(AccountValidator::isValidAccountIdFormat(*********));
        $this->assertTrue(AccountValidator::isValidAccountIdFormat('*********0123456'));

        // 无效格式
        $this->assertFalse(AccountValidator::isValidAccountIdFormat(''));
        $this->assertFalse(AccountValidator::isValidAccountIdFormat('12345')); // 太短
        $this->assertFalse(AccountValidator::isValidAccountIdFormat('*********0*********01')); // 太长
        $this->assertFalse(AccountValidator::isValidAccountIdFormat('12345abc')); // 包含字母
        $this->assertFalse(AccountValidator::isValidAccountIdFormat(null));
        $this->assertFalse(AccountValidator::isValidAccountIdFormat([]));
    }

    /**
     * 测试账户ID数组格式化
     */
    public function testFormatAccountIds()
    {
        $accountIds = ['*********', *********, '*********']; // 包含重复
        $formatted = AccountValidator::formatAccountIds($accountIds);
        
        $this->assertEquals(['*********', '*********'], $formatted);
    }

    /**
     * 测试账户ID数组格式化失败
     */
    public function testFormatAccountIdsFailure()
    {
        $accountIds = ['*********', 'invalid'];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('无效的账户ID格式: invalid');

        AccountValidator::formatAccountIds($accountIds);
    }

    /**
     * 测试创建模拟平台查询回调函数
     */
    public function testCreateMockPlatformQueryCallback()
    {
        $mockData = [
            '*********' => 'tiktok',
            '*********' => 'adq'
        ];

        $callback = AccountValidator::createMockPlatformQueryCallback($mockData);
        
        // 测试同平台
        $platforms1 = $callback(['*********']);
        $this->assertEquals(['tiktok'], $platforms1);
        
        // 测试多平台
        $platforms2 = $callback(['*********', '*********']);
        $this->assertEquals(['tiktok', 'adq'], $platforms2);
        
        // 测试未知账户
        $platforms3 = $callback(['unknown']);
        $this->assertEquals([], $platforms3);
    }

    /**
     * 测试创建数据库平台查询回调函数
     */
    public function testCreateDatabasePlatformQueryCallback()
    {
        $callback = AccountValidator::createDatabasePlatformQueryCallback();
        $this->assertTrue(is_callable($callback));
        
        // 暂时返回空数组（实际实现时需要连接数据库）
        $platforms = $callback(['*********']);
        $this->assertEquals([], $platforms);
    }

    /**
     * 测试链式验证设置
     */
    public function testSetNext()
    {
        $nextValidator = $this->createMock(ValidatorInterface::class);
        
        $result = $this->validator->setNext($nextValidator);
        
        $this->assertInstanceOf(ValidatorInterface::class, $result);
        $this->assertSame($nextValidator, $result);
    }

    /**
     * 测试链式验证执行
     */
    public function testHandleWithNextValidator()
    {
        $nextValidator = $this->createMock(ValidatorInterface::class);
        $nextValidator->expects($this->once())
                     ->method('handle')
                     ->willReturn(true);
        
        $this->validator->setNext($nextValidator);
        
        $data = [
            'target_advertiser_ids' => ['*********'],
            'amount' => 1000
        ];

        $result = $this->validator->handle($data);
        $this->assertTrue($result);
    }

    /**
     * 测试获取错误信息
     */
    public function testGetErrorMessage()
    {
        $data = [
            'target_advertiser_ids' => [],
            'amount' => 1000
        ];

        try {
            $this->validator->validate($data);
        } catch (\Exception $e) {
            // 验证异常被抛出
        }

        $this->assertEquals('账户ID不能为空', $this->validator->getErrorMessage());
    }

    /**
     * 测试账户ID去重功能
     */
    public function testAccountIdDeduplication()
    {
        // 创建模拟平台查询回调，确保去重后的账户ID被正确处理
        $mockCallback = function(array $accountIds) {
            // 验证传入的账户ID已经去重
            $this->assertEquals(['*********', '*********'], $accountIds);
            return ['tiktok'];
        };

        $this->validator->setPlatformQueryCallback($mockCallback);

        $data = [
            'target_advertiser_ids' => ['*********', '*********', '*********'], // 包含重复
            'amount' => 1000
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }
}