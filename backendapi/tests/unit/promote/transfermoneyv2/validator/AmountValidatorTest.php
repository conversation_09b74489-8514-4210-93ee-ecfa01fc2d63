<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\validator;

use backendapi\services\promote\transfermoneyv2\validator\AmountValidator;
use backendapi\services\promote\transfermoneyv2\validator\ValidatorInterface;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * 金额验证器测试用例
 * 
 * 测试金额验证器的各种功能和边界情况
 * 基于现有TransferMoneyBatchService::verificationAmount()方法逻辑
 */
class AmountValidatorTest extends TestCase
{
    /**
     * @var AmountValidator
     */
    private $validator;

    /**
     * @var MockObject|PlatformAdapterInterface
     */
    private $mockPlatformAdapter;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建平台适配器的Mock对象
        $this->mockPlatformAdapter = $this->createMock(PlatformAdapterInterface::class);
        
        // 创建验证器实例
        $this->validator = new AmountValidator();
    }

    /**
     * 测试验证器实现了正确的接口
     */
    public function testImplementsCorrectInterface()
    {
        $this->assertInstanceOf(ValidatorInterface::class, $this->validator);
    }

    /**
     * 测试获取验证器名称
     */
    public function testGetName()
    {
        $this->assertEquals('amount', $this->validator->getName());
    }

    /**
     * 测试有效金额验证通过
     */
    public function testValidateSuccessWithValidAmount()
    {
        $data = [
            'amount' => 500,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试缺少金额参数验证失败
     */
    public function testValidateFailureWithMissingAmount()
    {
        $data = [
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('缺少必要参数: amount');

        $this->validator->validate($data);
    }

    /**
     * 测试零金额验证失败
     */
    public function testValidateFailureWithZeroAmount()
    {
        $data = [
            'amount' => 0,
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('单次充值金额必须大于0');

        $this->validator->validate($data);
    }

    /**
     * 测试负金额验证失败
     */
    public function testValidateFailureWithNegativeAmount()
    {
        $data = [
            'amount' => -100,
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('单次充值金额必须大于0');

        $this->validator->validate($data);
    }

    /**
     * 测试带平台适配器的金额验证通过
     */
    public function testValidateSuccessWithPlatformAdapter()
    {
        // 设置平台适配器返回单次限额1000元
        $this->mockPlatformAdapter
            ->expects($this->once())
            ->method('getSingleLimit')
            ->willReturn(1000);

        $this->validator->setPlatformAdapter($this->mockPlatformAdapter);

        $data = [
            'amount' => 500,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试超过平台单次限额验证失败
     */
    public function testValidateFailureExceedsPlatformLimit()
    {
        // 设置平台适配器返回单次限额1000元
        $this->mockPlatformAdapter
            ->expects($this->once())
            ->method('getSingleLimit')
            ->willReturn(1000);

        $this->validator->setPlatformAdapter($this->mockPlatformAdapter);

        $data = [
            'amount' => 1500,
            'target_advertiser_ids' => ['123456789']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('单次充值金额不得超过1000');

        $this->validator->validate($data);
    }

    /**
     * 测试边界值 - 等于平台限额
     */
    public function testValidateSuccessAtPlatformLimit()
    {
        // 设置平台适配器返回单次限额1000元
        $this->mockPlatformAdapter
            ->expects($this->once())
            ->method('getSingleLimit')
            ->willReturn(1000);

        $this->validator->setPlatformAdapter($this->mockPlatformAdapter);

        $data = [
            'amount' => 1000,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $this->validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试设置和获取平台适配器
     */
    public function testSetAndGetPlatformAdapter()
    {
        $this->assertNull($this->validator->getPlatformAdapter());

        $this->validator->setPlatformAdapter($this->mockPlatformAdapter);
        $this->assertSame($this->mockPlatformAdapter, $this->validator->getPlatformAdapter());
    }

    /**
     * 测试创建基于平台的验证器 - 抖音平台
     */
    public function testCreateForPlatformTiktok()
    {
        $validator = AmountValidator::createForPlatform('tiktok');
        $this->assertInstanceOf(AmountValidator::class, $validator);
        $this->assertEquals('amount', $validator->getName());
    }

    /**
     * 测试创建基于平台的验证器 - ADQ平台
     */
    public function testCreateForPlatformAdq()
    {
        $validator = AmountValidator::createForPlatform('adq');
        $this->assertInstanceOf(AmountValidator::class, $validator);
        $this->assertEquals('amount', $validator->getName());
    }

    /**
     * 测试创建基于平台的验证器 - 不支持的平台
     */
    public function testCreateForPlatformUnsupported()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('不支持的平台: unknown');

        AmountValidator::createForPlatform('unknown');
    }

    /**
     * 测试有效金额格式验证
     */
    public function testIsValidAmountFormat()
    {
        // 有效格式
        $this->assertTrue(AmountValidator::isValidAmountFormat(100));
        $this->assertTrue(AmountValidator::isValidAmountFormat(100.5));
        $this->assertTrue(AmountValidator::isValidAmountFormat(100.50));
        $this->assertTrue(AmountValidator::isValidAmountFormat('100'));
        $this->assertTrue(AmountValidator::isValidAmountFormat('100.5'));

        // 无效格式
        $this->assertFalse(AmountValidator::isValidAmountFormat(0));
        $this->assertFalse(AmountValidator::isValidAmountFormat(-100));
        $this->assertFalse(AmountValidator::isValidAmountFormat('abc'));
        $this->assertFalse(AmountValidator::isValidAmountFormat(''));
        $this->assertFalse(AmountValidator::isValidAmountFormat(null));
        $this->assertFalse(AmountValidator::isValidAmountFormat(100.123)); // 超过2位小数
    }

    /**
     * 测试金额格式化
     */
    public function testFormatAmount()
    {
        $this->assertEquals(100.0, AmountValidator::formatAmount(100));
        $this->assertEquals(100.5, AmountValidator::formatAmount(100.5));
        $this->assertEquals(100.50, AmountValidator::formatAmount('100.50'));
        $this->assertEquals(100.0, AmountValidator::formatAmount('100'));
    }

    /**
     * 测试金额格式化失败
     */
    public function testFormatAmountFailure()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('无效的金额格式: abc');

        AmountValidator::formatAmount('abc');
    }

    /**
     * 测试链式验证设置
     */
    public function testSetNext()
    {
        $nextValidator = $this->createMock(ValidatorInterface::class);
        
        $result = $this->validator->setNext($nextValidator);
        
        $this->assertInstanceOf(ValidatorInterface::class, $result);
        $this->assertSame($nextValidator, $result);
    }

    /**
     * 测试链式验证执行
     */
    public function testHandleWithNextValidator()
    {
        $nextValidator = $this->createMock(ValidatorInterface::class);
        $nextValidator->expects($this->once())
                     ->method('handle')
                     ->willReturn(true);
        
        $this->validator->setNext($nextValidator);
        
        $data = [
            'amount' => 500,
            'target_advertiser_ids' => ['123456789']
        ];

        $result = $this->validator->handle($data);
        $this->assertTrue($result);
    }

    /**
     * 测试获取错误信息
     */
    public function testGetErrorMessage()
    {
        $data = [
            'amount' => 0,
            'target_advertiser_ids' => ['123456789']
        ];

        try {
            $this->validator->validate($data);
        } catch (\Exception $e) {
            // 验证异常被抛出
        }

        $this->assertEquals('单次充值金额必须大于0', $this->validator->getErrorMessage());
    }
}