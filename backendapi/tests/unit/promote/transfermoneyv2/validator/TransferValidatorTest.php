<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\validator;

use backendapi\services\promote\transfermoneyv2\validator\TransferValidator;
use backendapi\services\promote\transfermoneyv2\validator\ValidatorInterface;
use backendapi\services\promote\transfermoneyv2\validator\TimeValidator;
use backendapi\services\promote\transfermoneyv2\validator\AmountValidator;
use backendapi\services\promote\transfermoneyv2\validator\AccountValidator;
use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * 充值验证器测试用例
 * 
 * 测试充值验证器的各种功能和边界情况
 * 组合所有验证器进行链式验证
 */
class TransferValidatorTest extends TestCase
{
    /**
     * @var TransferValidator
     */
    private $validator;

    /**
     * @var MockObject|PlatformAdapterInterface
     */
    private $mockPlatformAdapter;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建平台适配器的Mock对象
        $this->mockPlatformAdapter = $this->createMock(PlatformAdapterInterface::class);
        $this->mockPlatformAdapter->method('getSingleLimit')->willReturn(1000);
        
        // 创建验证器实例
        $this->validator = new TransferValidator();
    }

    /**
     * 测试验证器实现了正确的接口
     */
    public function testImplementsCorrectInterface()
    {
        $this->assertInstanceOf(ValidatorInterface::class, $this->validator);
    }

    /**
     * 测试获取验证器名称
     */
    public function testGetName()
    {
        $this->assertEquals('transfer', $this->validator->getName());
    }

    /**
     * 测试默认验证器链
     */
    public function testDefaultValidatorChain()
    {
        $validators = $this->validator->getValidators();
        $this->assertCount(3, $validators);
        
        $validatorNames = array_map(function($validator) {
            return $validator->getName();
        }, $validators);
        
        $this->assertEquals(['time', 'amount', 'account'], $validatorNames);
    }

    /**
     * 测试有效数据验证通过
     */
    public function testValidateSuccessWithValidData()
    {
        // 创建在允许时间内的验证器
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        // 添加允许时间的时间验证器
        $timeValidator = new TimeValidator('10:00'); // 允许的时间
        $amountValidator = new AmountValidator();
        $accountValidator = new AccountValidator();
        
        $validator->addValidator($timeValidator);
        $validator->addValidator($amountValidator);
        $validator->addValidator($accountValidator);

        $data = [
            'amount' => 500,
            'target_advertiser_ids' => ['*********', '*********']
        ];

        $result = $validator->validate($data);
        $this->assertTrue($result);
    }

    /**
     * 测试时间验证失败
     */
    public function testValidateFailureWithInvalidTime()
    {
        // 创建在禁止时间内的验证器
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        // 添加禁止时间的时间验证器
        $timeValidator = new TimeValidator('03:00'); // 禁止的时间
        $validator->addValidator($timeValidator);

        $data = [
            'amount' => 500,
            'target_advertiser_ids' => ['*********']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('"凌晨2点到6点30分"时间段不可充值');

        $validator->validate($data);
    }

    /**
     * 测试金额验证失败
     */
    public function testValidateFailureWithInvalidAmount()
    {
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        // 只添加金额验证器
        $amountValidator = new AmountValidator();
        $validator->addValidator($amountValidator);

        $data = [
            'amount' => 0, // 无效金额
            'target_advertiser_ids' => ['*********']
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('单次充值金额必须大于0');

        $validator->validate($data);
    }

    /**
     * 测试账户验证失败
     */
    public function testValidateFailureWithInvalidAccount()
    {
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        // 只添加账户验证器
        $accountValidator = new AccountValidator();
        $validator->addValidator($accountValidator);

        $data = [
            'amount' => 500,
            'target_advertiser_ids' => [] // 空账户ID
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('账户ID不能为空');

        $validator->validate($data);
    }

    /**
     * 测试添加验证器
     */
    public function testAddValidator()
    {
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        $mockValidator = $this->createMock(ValidatorInterface::class);
        $mockValidator->method('getName')->willReturn('mock');
        
        $result = $validator->addValidator($mockValidator);
        
        $this->assertSame($validator, $result); // 支持链式调用
        $this->assertCount(1, $validator->getValidators());
        $this->assertSame($mockValidator, $validator->getValidator('mock'));
    }

    /**
     * 测试移除验证器
     */
    public function testRemoveValidator()
    {
        $validator = new TransferValidator();
        $initialCount = count($validator->getValidators());
        
        $result = $validator->removeValidator('amount');
        
        $this->assertSame($validator, $result); // 支持链式调用
        $this->assertCount($initialCount - 1, $validator->getValidators());
        $this->assertNull($validator->getValidator('amount'));
    }

    /**
     * 测试获取验证器
     */
    public function testGetValidator()
    {
        $timeValidator = $this->validator->getValidator('time');
        $this->assertInstanceOf(TimeValidator::class, $timeValidator);
        
        $amountValidator = $this->validator->getValidator('amount');
        $this->assertInstanceOf(AmountValidator::class, $amountValidator);
        
        $accountValidator = $this->validator->getValidator('account');
        $this->assertInstanceOf(AccountValidator::class, $accountValidator);
        
        $nonExistentValidator = $this->validator->getValidator('nonexistent');
        $this->assertNull($nonExistentValidator);
    }

    /**
     * 测试清空验证器
     */
    public function testClearValidators()
    {
        $result = $this->validator->clearValidators();
        
        $this->assertSame($this->validator, $result); // 支持链式调用
        $this->assertCount(0, $this->validator->getValidators());
    }

    /**
     * 测试设置和获取平台适配器
     */
    public function testSetAndGetPlatformAdapter()
    {
        $this->assertNull($this->validator->getPlatformAdapter());
        
        $this->validator->setPlatformAdapter($this->mockPlatformAdapter);
        $this->assertSame($this->mockPlatformAdapter, $this->validator->getPlatformAdapter());
    }

    /**
     * 测试创建基于平台的验证器
     */
    public function testCreateForPlatform()
    {
        $validator = TransferValidator::createForPlatform($this->mockPlatformAdapter);
        
        $this->assertInstanceOf(TransferValidator::class, $validator);
        $this->assertSame($this->mockPlatformAdapter, $validator->getPlatformAdapter());
        $this->assertCount(3, $validator->getValidators());
    }

    /**
     * 测试创建默认验证器
     */
    public function testCreateDefault()
    {
        $validator = TransferValidator::createDefault();
        
        $this->assertInstanceOf(TransferValidator::class, $validator);
        $this->assertNull($validator->getPlatformAdapter());
        $this->assertCount(3, $validator->getValidators());
    }

    /**
     * 测试创建自定义验证器
     */
    public function testCreateCustom()
    {
        $mockValidator1 = $this->createMock(ValidatorInterface::class);
        $mockValidator1->method('getName')->willReturn('mock1');
        $mockValidator1->method('validate')->willReturn(true);
        $mockValidator1->method('handle')->willReturn(true);
        
        $mockValidator2 = $this->createMock(ValidatorInterface::class);
        $mockValidator2->method('getName')->willReturn('mock2');
        
        $validator = TransferValidator::createCustom([$mockValidator1, $mockValidator2]);
        
        $this->assertInstanceOf(TransferValidator::class, $validator);
        $this->assertCount(2, $validator->getValidators());
        $this->assertSame($mockValidator1, $validator->getValidator('mock1'));
        $this->assertSame($mockValidator2, $validator->getValidator('mock2'));
    }

    /**
     * 测试获取验证器链信息
     */
    public function testGetValidatorChainInfo()
    {
        $info = $this->validator->getValidatorChainInfo();
        
        $this->assertTrue(is_array($info));
        $this->assertCount(3, $info);
        
        $this->assertEquals(0, $info[0]['index']);
        $this->assertEquals('time', $info[0]['name']);
        $this->assertContains('TimeValidator', $info[0]['class']);
        
        $this->assertEquals(1, $info[1]['index']);
        $this->assertEquals('amount', $info[1]['name']);
        $this->assertContains('AmountValidator', $info[1]['class']);
        
        $this->assertEquals(2, $info[2]['index']);
        $this->assertEquals('account', $info[2]['name']);
        $this->assertContains('AccountValidator', $info[2]['class']);
    }

    /**
     * 测试详细验证结果
     */
    public function testValidateWithDetails()
    {
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        // 添加一个会成功的验证器和一个会失败的验证器
        $successValidator = $this->createMock(ValidatorInterface::class);
        $successValidator->method('getName')->willReturn('success');
        $successValidator->method('validate')->willReturn(true);
        
        $failValidator = $this->createMock(ValidatorInterface::class);
        $failValidator->method('getName')->willReturn('fail');
        $failValidator->method('validate')->willThrowException(new \Exception('验证失败'));
        
        $validator->addValidator($successValidator);
        $validator->addValidator($failValidator);

        $data = ['test' => 'data'];
        $results = $validator->validateWithDetails($data);
        
        $this->assertCount(2, $results);
        
        $this->assertEquals('success', $results[0]['validator']);
        $this->assertTrue($results[0]['success']);
        $this->assertNull($results[0]['error']);
        
        $this->assertEquals('fail', $results[1]['validator']);
        $this->assertFalse($results[1]['success']);
        $this->assertEquals('验证失败', $results[1]['error']);
    }

    /**
     * 测试链式验证设置
     */
    public function testSetNext()
    {
        $nextValidator = $this->createMock(ValidatorInterface::class);
        
        $result = $this->validator->setNext($nextValidator);
        
        $this->assertInstanceOf(ValidatorInterface::class, $result);
        $this->assertSame($nextValidator, $result);
    }

    /**
     * 测试链式验证执行
     */
    public function testHandleWithNextValidator()
    {
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        // 添加一个简单的验证器
        $mockValidator = $this->createMock(ValidatorInterface::class);
        $mockValidator->method('handle')->willReturn(true);
        $validator->addValidator($mockValidator);
        
        $nextValidator = $this->createMock(ValidatorInterface::class);
        $nextValidator->expects($this->once())
                     ->method('handle')
                     ->willReturn(true);
        
        $validator->setNext($nextValidator);
        
        $data = ['test' => 'data'];
        $result = $validator->handle($data);
        $this->assertTrue($result);
    }

    /**
     * 测试空验证器链的验证
     */
    public function testValidateWithEmptyValidators()
    {
        $validator = new TransferValidator();
        $validator->clearValidators();
        
        $data = ['test' => 'data'];
        $result = $validator->validate($data);
        
        $this->assertTrue($result); // 空验证器链应该返回true
    }
}