<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\config;

use backendapi\services\promote\transfermoneyv2\config\ConfigManager;
use Codeception\Test\Unit;
use Exception;
use Yii;

/**
 * ConfigManager 配置管理器测试
 * 
 * 测试配置管理器的各种功能：
 * - 配置文件加载和缓存
 * - 配置验证
 * - 嵌套配置获取
 * - 配置热更新
 * - 平台配置管理
 * - 限制配置管理
 * - 预算配置管理
 */
class ConfigManagerTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    protected function _before()
    {
        // 清理配置缓存
        ConfigManager::clearCache();
    }

    protected function _after()
    {
        // 清理配置缓存
        ConfigManager::clearCache();
    }

    /**
     * 测试获取平台配置
     */
    public function testGetPlatformConfig()
    {
        // 获取全部平台配置
        $config = ConfigManager::getPlatformConfig();
        $this->assertIsArray($config);
        $this->assertArrayHasKey('platforms', $config);
        $this->assertArrayHasKey('platform_limits', $config);
        $this->assertArrayHasKey('default', $config);

        // 获取特定配置项
        $platforms = ConfigManager::getPlatformConfig('platforms');
        $this->assertIsArray($platforms);
        $this->assertArrayHasKey('tiktok', $platforms);
        $this->assertArrayHasKey('adq', $platforms);

        // 获取嵌套配置项
        $tiktokName = ConfigManager::getPlatformConfig('platforms.tiktok.name');
        $this->assertEquals('抖音', $tiktokName);

        // 获取不存在的配置项，返回默认值
        $nonExistent = ConfigManager::getPlatformConfig('non.existent.key', 'default_value');
        $this->assertEquals('default_value', $nonExistent);
    }

    /**
     * 测试获取充值限制配置
     */
    public function testGetTransferLimitsConfig()
    {
        // 获取全部限制配置
        $config = ConfigManager::getTransferLimitsConfig();
        $this->assertIsArray($config);
        $this->assertArrayHasKey('time_restrictions', $config);
        $this->assertArrayHasKey('batch_limits', $config);
        $this->assertArrayHasKey('frequency_control', $config);

        // 获取特定配置项
        $timeRestrictions = ConfigManager::getTransferLimitsConfig('time_restrictions');
        $this->assertIsArray($timeRestrictions);
        $this->assertArrayHasKey('forbidden_hours', $timeRestrictions);

        // 获取嵌套配置项
        $forbiddenStart = ConfigManager::getTransferLimitsConfig('time_restrictions.forbidden_hours.start');
        $this->assertEquals('02:00', $forbiddenStart);

        // 获取批量限制
        $maxAccounts = ConfigManager::getTransferLimitsConfig('batch_limits.max_accounts_per_batch');
        $this->assertEquals(50, $maxAccounts);
    }

    /**
     * 测试获取预算规则配置
     */
    public function testGetBudgetRulesConfig()
    {
        // 获取全部预算配置
        $config = ConfigManager::getBudgetRulesConfig();
        $this->assertIsArray($config);
        $this->assertArrayHasKey('budget_control', $config);
        $this->assertArrayHasKey('user_daily_limits', $config);
        $this->assertArrayHasKey('balance_warnings', $config);

        // 获取特定配置项
        $budgetControl = ConfigManager::getBudgetRulesConfig('budget_control');
        $this->assertIsArray($budgetControl);
        $this->assertArrayHasKey('enabled', $budgetControl);

        // 获取嵌套配置项
        $warningThreshold = ConfigManager::getBudgetRulesConfig('balance_warnings.warning_threshold');
        $this->assertEquals(3000, $warningThreshold);
    }

    /**
     * 测试配置缓存机制
     */
    public function testConfigCaching()
    {
        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        
        // 第一次调用时缓存为空，需要从文件加载
        $mockCache->expects($this->once())
            ->method('get')
            ->with('config_platform')
            ->willReturn(false);

        // 设置缓存
        $mockCache->expects($this->once())
            ->method('set')
            ->with('config_platform', $this->isType('array'), 3600);

        Yii::$app->set('cache', $mockCache);

        // 第一次获取配置
        $config1 = ConfigManager::getPlatformConfig();
        $this->assertIsArray($config1);

        // 第二次获取配置应该从内存缓存获取，不会再调用Redis
        $config2 = ConfigManager::getPlatformConfig();
        $this->assertEquals($config1, $config2);
    }

    /**
     * 测试配置验证
     */
    public function testConfigValidation()
    {
        // 测试平台配置验证
        $platforms = ConfigManager::getPlatformConfig('platforms');
        foreach ($platforms as $platform => $info) {
            $this->assertArrayHasKey('adapter_class', $info, "平台 {$platform} 缺少 adapter_class");
            $this->assertArrayHasKey('enabled', $info, "平台 {$platform} 缺少 enabled");
            $this->assertIsString($info['adapter_class']);
            $this->assertIsBool($info['enabled']);
        }

        // 测试限制配置验证
        $limits = ConfigManager::getTransferLimitsConfig();
        $this->assertArrayHasKey('time_restrictions', $limits);
        $this->assertArrayHasKey('batch_limits', $limits);
        $this->assertArrayHasKey('frequency_control', $limits);

        // 测试预算配置验证
        $budget = ConfigManager::getBudgetRulesConfig();
        $this->assertArrayHasKey('budget_control', $budget);
        $this->assertArrayHasKey('user_daily_limits', $budget);
        $this->assertArrayHasKey('balance_warnings', $budget);
    }

    /**
     * 测试清除配置缓存
     */
    public function testClearCache()
    {
        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        
        // 清除所有缓存
        $mockCache->expects($this->exactly(3))
            ->method('delete')
            ->withConsecutive(
                ['config_platform'],
                ['config_limits'],
                ['config_budget']
            );

        Yii::$app->set('cache', $mockCache);

        ConfigManager::clearCache();

        // 清除特定类型缓存
        $mockCache->expects($this->once())
            ->method('delete')
            ->with('config_platform');

        ConfigManager::clearCache('platform');
    }

    /**
     * 测试重新加载配置
     */
    public function testReloadConfig()
    {
        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        
        // 重新加载时会清除缓存并重新设置
        $mockCache->expects($this->once())
            ->method('delete')
            ->with('config_platform');

        $mockCache->expects($this->once())
            ->method('get')
            ->with('config_platform')
            ->willReturn(false);

        $mockCache->expects($this->once())
            ->method('set')
            ->with('config_platform', $this->isType('array'), 3600);

        Yii::$app->set('cache', $mockCache);

        ConfigManager::reload('platform');
    }

    /**
     * 测试获取平台适配器类名
     */
    public function testGetPlatformAdapterClass()
    {
        // 获取抖音平台适配器类名
        $adapterClass = ConfigManager::getPlatformAdapterClass('tiktok');
        $this->assertEquals('backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter', $adapterClass);

        // 获取ADQ平台适配器类名
        $adapterClass = ConfigManager::getPlatformAdapterClass('adq');
        $this->assertEquals('backendapi\services\promote\transfermoneyv2\platform\AdqAdapter', $adapterClass);

        // 获取不存在的平台应该抛出异常
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('未知的平台: unknown');
        ConfigManager::getPlatformAdapterClass('unknown');
    }

    /**
     * 测试获取平台限额配置
     */
    public function testGetPlatformLimit()
    {
        // 获取抖音平台限额
        $tiktokLimits = ConfigManager::getPlatformLimit('tiktok');
        $this->assertIsArray($tiktokLimits);
        $this->assertArrayHasKey('single_recharge_amount', $tiktokLimits);
        $this->assertEquals(1000, $tiktokLimits['single_recharge_amount']);

        // 获取特定限额类型
        $singleLimit = ConfigManager::getPlatformLimit('tiktok', 'single_recharge_amount');
        $this->assertEquals(1000, $singleLimit);

        // 获取ADQ平台限额
        $adqSingleLimit = ConfigManager::getPlatformLimit('adq', 'single_recharge_amount');
        $this->assertEquals(2000, $adqSingleLimit);

        // 获取不存在的平台应该返回默认配置
        $unknownLimits = ConfigManager::getPlatformLimit('unknown');
        $this->assertIsArray($unknownLimits);
    }

    /**
     * 测试时间限制检查
     */
    public function testIsTimeRestricted()
    {
        // 测试禁止时间段
        $this->assertTrue(ConfigManager::isTimeRestricted('03:00'));
        $this->assertTrue(ConfigManager::isTimeRestricted('06:00'));

        // 测试允许时间段
        $this->assertFalse(ConfigManager::isTimeRestricted('08:00'));
        $this->assertFalse(ConfigManager::isTimeRestricted('20:00'));

        // 测试边界时间
        $this->assertTrue(ConfigManager::isTimeRestricted('02:00'));
        $this->assertTrue(ConfigManager::isTimeRestricted('06:30'));
        $this->assertFalse(ConfigManager::isTimeRestricted('01:59'));
        $this->assertFalse(ConfigManager::isTimeRestricted('06:31'));
    }

    /**
     * 测试获取批量限制
     */
    public function testGetBatchLimit()
    {
        // 获取所有批量限制
        $batchLimits = ConfigManager::getBatchLimit();
        $this->assertIsArray($batchLimits);
        $this->assertArrayHasKey('max_accounts_per_batch', $batchLimits);

        // 获取特定限制
        $maxAccounts = ConfigManager::getBatchLimit('max_accounts_per_batch');
        $this->assertEquals(50, $maxAccounts);

        $maxBatches = ConfigManager::getBatchLimit('max_daily_batches');
        $this->assertEquals(100, $maxBatches);
    }

    /**
     * 测试获取余额警告阈值
     */
    public function testGetBalanceWarningThreshold()
    {
        $threshold = ConfigManager::getBalanceWarningThreshold();
        $this->assertEquals(3000, $threshold);
    }

    /**
     * 测试检查预算控制是否启用
     */
    public function testIsBudgetControlEnabled()
    {
        $enabled = ConfigManager::isBudgetControlEnabled();
        $this->assertTrue($enabled);
    }

    /**
     * 测试获取用户每日限额
     */
    public function testGetUserDailyLimit()
    {
        // 默认用户限额
        $defaultLimit = ConfigManager::getUserDailyLimit('default');
        $this->assertEquals(10000, $defaultLimit);

        // VIP用户限额
        $vipLimit = ConfigManager::getUserDailyLimit('vip');
        $this->assertEquals(50000, $vipLimit);

        // 管理员限额
        $adminLimit = ConfigManager::getUserDailyLimit('admin');
        $this->assertEquals(100000, $adminLimit);

        // 不存在的用户类型应该返回默认限额
        $unknownLimit = ConfigManager::getUserDailyLimit('unknown');
        $this->assertEquals(10000, $unknownLimit);
    }

    /**
     * 测试获取配置版本信息
     */
    public function testGetConfigVersion()
    {
        // 获取平台配置版本
        $platformVersion = ConfigManager::getConfigVersion('platform');
        $this->assertIsArray($platformVersion);
        $this->assertArrayHasKey('version', $platformVersion);
        $this->assertArrayHasKey('updated_at', $platformVersion);

        // 获取所有配置版本
        $allVersions = ConfigManager::getAllConfigVersions();
        $this->assertIsArray($allVersions);
        $this->assertArrayHasKey('platform', $allVersions);
        $this->assertArrayHasKey('limits', $allVersions);
        $this->assertArrayHasKey('budget', $allVersions);
    }

    /**
     * 测试嵌套配置值获取
     */
    public function testNestedConfigValues()
    {
        // 测试多层嵌套
        $value = ConfigManager::getPlatformConfig('platforms.tiktok.adapter_class');
        $this->assertEquals('backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter', $value);

        // 测试深层嵌套
        $value = ConfigManager::getTransferLimitsConfig('time_restrictions.forbidden_hours.enabled');
        $this->assertTrue($value);

        // 测试不存在的嵌套路径
        $value = ConfigManager::getPlatformConfig('platforms.unknown.adapter_class', 'default');
        $this->assertEquals('default', $value);

        // 测试部分存在的嵌套路径
        $value = ConfigManager::getPlatformConfig('platforms.tiktok.unknown_field', 'default');
        $this->assertEquals('default', $value);
    }

    /**
     * 测试配置文件不存在的情况
     */
    public function testConfigFileNotExists()
    {
        // 这个测试需要模拟配置文件不存在的情况
        // 由于我们使用的是真实的配置文件，这里只是验证异常处理逻辑
        $this->expectException(Exception::class);
        
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass(ConfigManager::class);
        $method = $reflection->getMethod('loadConfigFromFile');
        $method->setAccessible(true);
        
        // 尝试加载不存在的配置类型
        $method->invoke(null, 'nonexistent');
    }

    /**
     * 测试配置数据类型验证
     */
    public function testConfigDataTypes()
    {
        // 验证平台配置数据类型
        $platforms = ConfigManager::getPlatformConfig('platforms');
        foreach ($platforms as $platform => $config) {
            $this->assertIsString($config['name']);
            $this->assertIsString($config['adapter_class']);
            $this->assertIsBool($config['enabled']);
        }

        // 验证限制配置数据类型
        $maxAccounts = ConfigManager::getTransferLimitsConfig('batch_limits.max_accounts_per_batch');
        $this->assertIsInt($maxAccounts);

        $sleepDuration = ConfigManager::getTransferLimitsConfig('frequency_control.sleep_duration');
        $this->assertIsInt($sleepDuration);

        // 验证预算配置数据类型
        $budgetEnabled = ConfigManager::getBudgetRulesConfig('budget_control.enabled');
        $this->assertIsBool($budgetEnabled);

        $warningThreshold = ConfigManager::getBudgetRulesConfig('balance_warnings.warning_threshold');
        $this->assertIsInt($warningThreshold);
    }
}