<?php

namespace backendapi\tests\unit\promote\transfermoneyv2\queue;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\config\ConfigManager;
use common\queues\TransferMoneyJobV2;
use common\models\backend\Member;
use services\common\FeishuExamineService;
use Codeception\Test\Unit;
use Yii;
use Exception;

/**
 * TransferMoneyJobV2 队列任务测试
 * 
 * 测试新队列任务处理类的各种功能：
 * - 基本队列任务执行
 * - 添加任务到队列
 * - 加粉充值任务
 * - 充值频次检查
 * - 消息发送机制
 */
class TransferMoneyJobV2Test extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * @var TransferMoneyJobV2
     */
    private $job;

    /**
     * @var array 测试数据
     */
    private $testData;

    protected function _before()
    {
        // 初始化测试数据
        $this->testData = [
            'target_advertiser_ids' => ['**********', '0987654321'],
            'amount' => 100,
            'user_name' => '测试用户',
            'user_id' => 1,
            'execute_time' => time(),
            'isTimeRecharge' => false,
            'create_time' => time(),
        ];

        // 创建队列任务实例
        $this->job = new TransferMoneyJobV2([
            'data' => $this->testData
        ]);

        // 清理配置缓存
        ConfigManager::clearCache();
    }

    protected function _after()
    {
        // 清理测试数据
        ConfigManager::clearCache();
    }

    /**
     * 测试队列任务基本属性
     */
    public function testJobBasicProperties()
    {
        $this->assertEquals(10, $this->job->delay);
        $this->assertEquals(1, $this->job->retryTimes);
        $this->assertTrue($this->job->isSendMessage);
        $this->assertEquals($this->testData, $this->job->data);
    }

    /**
     * 测试队列任务执行成功
     */
    public function testRunSuccess()
    {
        // Mock TransferMoneyServiceV2
        $mockService = $this->createMock(TransferMoneyServiceV2::class);
        $mockService->expects($this->once())
            ->method('execute')
            ->with($this->testData)
            ->willReturn([
                200 => [
                    [
                        'msg' => '充值成功',
                        'target_advertiser_id' => '**********',
                        'target_advertiser_name' => '测试账户',
                        'main_body' => '测试主体',
                        'advertiser_id' => '1111111111',
                        'insufficientNalance' => 5000
                    ]
                ]
            ]);

        $mockService->expects($this->once())
            ->method('resRealData')
            ->willReturn([
                200 => ['code' => 200, 'msg' => '充值成功']
            ]);

        // 使用反射设置私有属性
        $reflection = new \ReflectionClass($this->job);
        $serviceProperty = $reflection->getProperty('transferService');
        $serviceProperty->setAccessible(true);
        $serviceProperty->setValue($this->job, $mockService);

        // 执行任务
        $result = $this->job->run(null);

        $this->assertTrue($result);
    }

    /**
     * 测试队列任务执行失败
     */
    public function testRunFailure()
    {
        // Mock TransferMoneyServiceV2 抛出异常
        $mockService = $this->createMock(TransferMoneyServiceV2::class);
        $mockService->expects($this->once())
            ->method('execute')
            ->with($this->testData)
            ->willThrowException(new Exception('充值失败'));

        // 使用反射设置私有属性
        $reflection = new \ReflectionClass($this->job);
        $serviceProperty = $reflection->getProperty('transferService');
        $serviceProperty->setAccessible(true);
        $serviceProperty->setValue($this->job, $mockService);

        // 执行任务
        $result = $this->job->run(null);

        $this->assertTrue($result);
    }

    /**
     * 测试添加普通充值任务到队列
     */
    public function testAddJob()
    {
        $data = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户',
            'isTimeRecharge' => false,
            'execute_time' => time() + 60,
        ];

        // Mock 队列
        $mockQueue = $this->createMock(\yii\queue\Queue::class);
        $mockQueue->expects($this->once())
            ->method('has')
            ->willReturn(false);
        
        $mockQueue->expects($this->once())
            ->method('delay')
            ->with($this->greaterThan(0))
            ->willReturnSelf();
            
        $mockQueue->expects($this->once())
            ->method('push')
            ->with($this->isInstanceOf(TransferMoneyJobV2::class));

        // Mock Yii::$app->que
        Yii::$app->set('que', $mockQueue);

        $result = TransferMoneyJobV2::addJob($data);
        $this->assertTrue($result);
    }

    /**
     * 测试添加定时充值任务到队列
     */
    public function testAddTimeRechargeJob()
    {
        $data = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户',
            'isTimeRecharge' => true,
            'execute_time' => time() + 3600, // 1小时后执行
        ];

        // Mock 队列
        $mockQueue = $this->createMock(\yii\queue\Queue::class);
        $mockQueue->expects($this->once())
            ->method('has')
            ->willReturn(false);
        
        // 定时任务不设置重要级别
        $mockQueue->expects($this->never())
            ->method('setImportant');
            
        $mockQueue->expects($this->once())
            ->method('delay')
            ->with($this->greaterThan(3500)) // 大约1小时
            ->willReturnSelf();
            
        $mockQueue->expects($this->once())
            ->method('push')
            ->with($this->isInstanceOf(TransferMoneyJobV2::class));

        // Mock Yii::$app->que
        Yii::$app->set('que', $mockQueue);

        $result = TransferMoneyJobV2::addJob($data);
        $this->assertTrue($result);
    }

    /**
     * 测试任务已存在时不重复添加
     */
    public function testAddJobWhenExists()
    {
        $data = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户',
            'isTimeRecharge' => false,
            'execute_time' => time() + 60,
        ];

        // Mock 队列
        $mockQueue = $this->createMock(\yii\queue\Queue::class);
        $mockQueue->expects($this->once())
            ->method('has')
            ->willReturn(true); // 任务已存在

        $mockQueue->expects($this->never())
            ->method('push');

        // Mock Yii::$app->que
        Yii::$app->set('que', $mockQueue);

        $result = TransferMoneyJobV2::addJob($data);
        $this->assertTrue($result);
    }

    /**
     * 测试加粉充值任务
     */
    public function testAddFansJob()
    {
        $subAdvertiserId = '**********';

        // Mock 配置
        $mockConfig = $this->createMock(\common\models\Config::class);
        $mockConfig::staticExpects($this->once())
            ->method('getByName')
            ->with('transferAccount')
            ->willReturn("**********\n0987654321");

        // Mock 队列
        $mockQueue = $this->createMock(\yii\queue\Queue::class);
        $mockQueue->expects($this->once())
            ->method('has')
            ->willReturn(false);
            
        $mockQueue->expects($this->once())
            ->method('push')
            ->with($this->callback(function ($job) {
                return $job instanceof TransferMoneyJobV2 
                    && $job->data['amount'] == 50
                    && $job->data['user_name'] == '系统自动充值'
                    && !$job->isSendMessage;
            }));

        // Mock Yii::$app->que
        Yii::$app->set('que', $mockQueue);

        // Mock checkTransferMoneyCount 返回 true
        $mockJob = $this->getMockBuilder(TransferMoneyJobV2::class)
            ->onlyMethods(['checkTransferMoneyCount'])
            ->getMock();
        $mockJob::staticExpects($this->once())
            ->method('checkTransferMoneyCount')
            ->with($subAdvertiserId)
            ->willReturn(true);

        $result = $mockJob::addFansJob($subAdvertiserId);
        $this->assertTrue($result);
    }

    /**
     * 测试加粉充值任务 - 账户不在白名单
     */
    public function testAddFansJobNotInWhitelist()
    {
        $subAdvertiserId = '**********'; // 不在白名单中

        // Mock 配置
        $mockConfig = $this->createMock(\common\models\Config::class);
        $mockConfig::staticExpects($this->once())
            ->method('getByName')
            ->with('transferAccount')
            ->willReturn("**********\n0987654321");

        $result = TransferMoneyJobV2::addFansJob($subAdvertiserId);
        $this->assertFalse($result);
    }

    /**
     * 测试加粉充值任务 - 频次检查失败
     */
    public function testAddFansJobFrequencyCheckFailed()
    {
        $subAdvertiserId = '**********';

        // Mock 配置
        $mockConfig = $this->createMock(\common\models\Config::class);
        $mockConfig::staticExpects($this->once())
            ->method('getByName')
            ->with('transferAccount')
            ->willReturn("**********\n0987654321");

        // Mock checkTransferMoneyCount 返回 false
        $mockJob = $this->getMockBuilder(TransferMoneyJobV2::class)
            ->onlyMethods(['checkTransferMoneyCount'])
            ->getMock();
        $mockJob::staticExpects($this->once())
            ->method('checkTransferMoneyCount')
            ->with($subAdvertiserId)
            ->willReturn(false);

        $result = $mockJob::addFansJob($subAdvertiserId);
        $this->assertFalse($result);
    }

    /**
     * 测试充值频次检查 - 正常情况
     */
    public function testCheckTransferMoneyCountNormal()
    {
        $subAdvertiserId = '**********';

        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->expects($this->exactly(2))
            ->method('get')
            ->willReturnOnConsecutiveCalls(null, false); // 当天限制列表为空，计数为false

        $mockCache->expects($this->once())
            ->method('set')
            ->with(
                $this->stringContains('AddFansTransferMoneyCount:' . $subAdvertiserId),
                1,
                300 // 5分钟
            );

        Yii::$app->set('cache', $mockCache);

        $result = TransferMoneyJobV2::checkTransferMoneyCount($subAdvertiserId);
        $this->assertTrue($result);
    }

    /**
     * 测试充值频次检查 - 当天已被限制
     */
    public function testCheckTransferMoneyCountDailyRestricted()
    {
        $subAdvertiserId = '**********';

        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->expects($this->once())
            ->method('get')
            ->willReturn([$subAdvertiserId]); // 当天限制列表包含该账户

        Yii::$app->set('cache', $mockCache);

        $result = TransferMoneyJobV2::checkTransferMoneyCount($subAdvertiserId);
        $this->assertFalse($result);
    }

    /**
     * 测试充值频次检查 - 超过5次限制
     */
    public function testCheckTransferMoneyCountExceedLimit()
    {
        $subAdvertiserId = '**********';

        // Mock Redis缓存
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        $mockCache->expects($this->exactly(2))
            ->method('get')
            ->willReturnOnConsecutiveCalls(null, 5); // 当天限制列表为空，但计数已达5次

        $mockCache->expects($this->exactly(2))
            ->method('set')
            ->withConsecutive(
                [$this->stringContains('AddFansTransferMoneyCount:' . date('Y-m-d')), [$subAdvertiserId]],
                [$this->stringContains('AddFansTransferMoneyCount:' . $subAdvertiserId), 6, 300]
            );

        Yii::$app->set('cache', $mockCache);

        // Mock 飞书通知
        $mockFeishuNotice = $this->createMock(\stdClass::class);
        $mockFeishuNotice->expects($this->once())
            ->method('text')
            ->with($this->stringContains('加粉异常,在一分钟内充值超过5次'));

        Yii::$app->set('feishuNotice', $mockFeishuNotice);

        $result = TransferMoneyJobV2::checkTransferMoneyCount($subAdvertiserId);
        $this->assertFalse($result);
    }

    /**
     * 测试消息发送 - 成功情况
     */
    public function testSendMsgSuccess()
    {
        $arrError = [
            200 => ['msg' => '充值成功']
        ];

        $this->job->isSendMessage = true;

        // Mock 获取发送用户ID
        $mockMember = $this->createMock(Member::class);
        $mockMember::staticExpects($this->once())
            ->method('find')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('select')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('where')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('scalar')
            ->willReturn('test_user_id');

        // Mock 飞书通知
        $mockFeishuNotice = $this->createMock(\stdClass::class);
        $mockFeishuNotice->expects($this->once())
            ->method('text')
            ->with(
                $this->stringContains('充值成功'),
                'test_user_id',
                'user_id'
            );

        Yii::$app->set('feishuNotice', $mockFeishuNotice);

        $result = $this->job->sendMsg($arrError);
        $this->assertTrue($result);
    }

    /**
     * 测试消息发送 - 禁用发送
     */
    public function testSendMsgDisabled()
    {
        $arrError = [
            200 => ['msg' => '充值成功']
        ];

        $this->job->isSendMessage = false;

        $result = $this->job->sendMsg($arrError);
        $this->assertFalse($result);
    }

    /**
     * 测试消息发送 - 失败情况
     */
    public function testSendMsgFailure()
    {
        $arrError = [
            422 => ['msg' => '充值失败：余额不足']
        ];

        $this->job->isSendMessage = true;

        // Mock 飞书通知
        $mockFeishuNotice = $this->createMock(\stdClass::class);
        $mockFeishuNotice->expects($this->once())
            ->method('text')
            ->with($this->stringContains('充值失败'));

        Yii::$app->set('feishuNotice', $mockFeishuNotice);

        $result = $this->job->sendMsg($arrError);
        $this->assertTrue($result);
    }

    /**
     * 测试获取发送用户ID
     */
    public function testGetSendUserId()
    {
        // Mock Member模型
        $mockMember = $this->createMock(Member::class);
        $mockMember::staticExpects($this->once())
            ->method('find')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('select')
            ->with('feishu_userid')
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('where')
            ->with(['username' => '测试用户'])
            ->willReturnSelf();
        $mockMember->expects($this->once())
            ->method('scalar')
            ->willReturn('test_feishu_id');

        $result = $this->job->getSendUserId();
        $this->assertEquals('test_feishu_id', $result);
    }

    /**
     * 测试任务唯一性标识
     */
    public function testJobUniqueness()
    {
        $job1 = new TransferMoneyJobV2(['data' => $this->testData]);
        $job2 = new TransferMoneyJobV2(['data' => $this->testData]);

        // 相同数据的任务应该有相同的唯一标识
        $this->assertEquals($job1->getJobKey(), $job2->getJobKey());

        // 不同数据的任务应该有不同的唯一标识
        $differentData = $this->testData;
        $differentData['amount'] = 200;
        $job3 = new TransferMoneyJobV2(['data' => $differentData]);

        $this->assertNotEquals($job1->getJobKey(), $job3->getJobKey());
    }

    /**
     * 测试任务延迟执行
     */
    public function testJobDelay()
    {
        $mockQueue = $this->createMock(\yii\queue\Queue::class);
        
        // 验证延迟时间计算
        $mockQueue->expects($this->once())
            ->method('delay')
            ->with($this->callback(function ($delay) {
                return $delay > 0 && $delay <= 3600; // 延迟时间应该在合理范围内
            }))
            ->willReturnSelf();

        $mockQueue->expects($this->once())
            ->method('push');

        $this->job->delay($mockQueue);
    }
}