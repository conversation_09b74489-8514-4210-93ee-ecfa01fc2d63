<?php

namespace backendapi\tests\unit\promote\transfermoneyv2;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\platform\MockAdapter;
use backendapi\services\promote\transfermoneyv2\validator\TransferValidator;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use common\queues\TransferMoneyJob;
use common\enums\reportEnum;
use Codeception\Test\Unit;
use Yii;
use Exception;

/**
 * TransferMoneyServiceV2 测试用例
 * 
 * 基于TDD方式开发，测试统一业务服务的所有核心功能
 * 包括四种充值模式：正常充值、批量充值、定时充值、加粉充值
 */
class TransferMoneyServiceV2Test extends Unit
{
    /**
     * @var TransferMoneyServiceV2
     */
    private $service;

    /**
     * @var PlatformFactory
     */
    private $platformFactory;

    /**
     * @var TransferCacheManager
     */
    private $cacheManager;

    /**
     * 设置测试环境
     */
    protected function _before()
    {
        parent::_before();
        
        // 创建Mock缓存组件
        $mockCache = $this->createMock(\yii\caching\CacheInterface::class);
        
        // 初始化组件
        $this->platformFactory = new PlatformFactory();
        $this->cacheManager = new TransferCacheManager($mockCache);
        
        // 创建服务实例
        $this->service = new TransferMoneyServiceV2(
            $this->platformFactory,
            $this->cacheManager
        );
    }

    /**
     * 清理测试环境
     */
    protected function _after()
    {
        parent::_after();
        $this->platformFactory->reset();
    }

    /**
     * 测试服务初始化
     */
    public function testServiceInitialization()
    {
        $this->assertInstanceOf(TransferMoneyServiceV2::class, $this->service);
        $this->assertInstanceOf(PlatformFactory::class, $this->service->getPlatformFactory());
        $this->assertInstanceOf(TransferCacheManager::class, $this->service->getCacheManager());
    }

    /**
     * 测试run方法 - 正常充值流程
     */
    public function testRunNormalRecharge()
    {
        $params = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：**********\n转账金额：100"
        ];

        // 模拟正常充值（非定时）
        $result = $this->service->run($params);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('200', $result);
        $this->assertEquals(200, $result['200']['code']);
        $this->assertEquals('充值成功', $result['200']['msg']);
    }

    /**
     * 测试run方法 - 定时充值流程
     */
    public function testRunTimedRecharge()
    {
        $tomorrow = date('Y-m-d H:i:s', strtotime('+1 day'));
        $params = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：**********\n转账金额：100\n定时充值：{$tomorrow}"
        ];

        $result = $this->service->run($params);
        
        $this->assertEquals('定时充值操作成功', $result);
        $this->assertEquals(100, $this->service->getCode());
    }

    /**
     * 测试run方法 - 参数验证失败
     */
    public function testRunWithInvalidParams()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('充值数据不能为空');
        
        $this->service->run([]);
    }

    /**
     * 测试run方法 - 时间限制检查
     */
    public function testRunWithTimeLimit()
    {
        // 模拟凌晨时间
        $mockTime = '03:00';
        
        // 这里需要Mock时间函数，实际测试中可能需要使用时间Mock库
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('"凌晨2点到6点30分"时间段不可充值');
        
        // 创建一个会触发时间限制的服务实例
        $service = $this->createServiceWithMockedTime($mockTime);
        
        $params = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：**********\n转账金额：100"
        ];
        
        $service->run($params);
    }

    /**
     * 测试execute方法 - 批量充值
     */
    public function testExecuteBatchRecharge()
    {
        $data = [
            'target_advertiser_ids' => ['**********', '**********'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        $result = $this->service->execute($data);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey(200, $result);
        $this->assertCount(2, $result[200]);
    }

    /**
     * 测试execute方法 - 批量限制检查
     */
    public function testExecuteBatchLimit()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('充值一次最多只能50个户');
        
        // 创建超过50个账户的数据
        $targetIds = array_map(function($i) {
            return "account_{$i}";
        }, range(1, 51));
        
        $data = [
            'target_advertiser_ids' => $targetIds,
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        $this->service->execute($data);
    }

    /**
     * 测试transferMoney方法 - 单个账户充值
     */
    public function testTransferMoney()
    {
        // 设置测试数据
        $this->service->setTargetAdvertiserId('**********');
        $this->service->setAmount(100);
        $this->service->setPlatform(reportEnum::ADQ);
        
        // 执行充值
        $result = $this->service->transferMoney();
        
        $this->assertTrue($result);
    }

    /**
     * 测试getBalance方法 - 获取账户余额
     */
    public function testGetBalance()
    {
        $this->service->setAdvertiserId('**********');
        $this->service->setPlatform(reportEnum::ADQ);
        
        $balance = $this->service->getBalance();
        
        $this->assertIsFloat($balance);
        $this->assertGreaterThanOrEqual(0, $balance);
    }

    /**
     * 测试getAccountBalance方法 - 查询所有账户余额
     */
    public function testGetAccountBalance()
    {
        $result = $this->service->getAccountBalance();
        
        $this->assertIsString($result);
        $this->assertStringContainsString('：', $result);
    }

    /**
     * 测试dealParams方法 - 参数处理
     */
    public function testDealParams()
    {
        $params = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "账户ID：**********\n转账金额：100"
        ];

        $result = $this->service->dealParams($params);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('账户ID', $result);
        $this->assertArrayHasKey('转账金额', $result);
        $this->assertEquals('**********', $result['账户ID']);
        $this->assertEquals('100', $result['转账金额']);
    }

    /**
     * 测试dealParams方法 - 格式错误
     */
    public function testDealParamsWithInvalidFormat()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('数据格式有误，请认真审查');
        
        $params = [
            'user_id' => 1,
            'user_name' => '测试用户',
            'data' => "无效格式的数据"
        ];

        $this->service->dealParams($params);
    }

    /**
     * 测试verificationAccount方法 - 账户验证
     */
    public function testVerificationAccount()
    {
        $data = ['账户ID' => '**********、**********'];
        
        $result = $this->service->verificationAccount($data);
        
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertContains('**********', $result);
        $this->assertContains('**********', $result);
    }

    /**
     * 测试verificationAccount方法 - 空账户ID
     */
    public function testVerificationAccountWithEmpty()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('账户ID不能为空');
        
        $data = ['账户ID' => ''];
        $this->service->verificationAccount($data);
    }

    /**
     * 测试verificationAmount方法 - 金额验证
     */
    public function testVerificationAmount()
    {
        $this->service->setPlatform(reportEnum::ADQ);
        $data = ['转账金额' => 100];
        
        $result = $this->service->verificationAmount($data);
        
        $this->assertEquals(100, $result);
    }

    /**
     * 测试verificationAmount方法 - 金额过大
     */
    public function testVerificationAmountTooLarge()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('单次充值金额不得超过');
        
        $this->service->setPlatform(reportEnum::ADQ);
        $data = ['转账金额' => 5000]; // 超过ADQ限额2000
        
        $this->service->verificationAmount($data);
    }

    /**
     * 测试verificationAmount方法 - 金额为零或负数
     */
    public function testVerificationAmountZeroOrNegative()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('单次充值金额必须大于0');
        
        $this->service->setPlatform(reportEnum::ADQ);
        $data = ['转账金额' => 0];
        
        $this->service->verificationAmount($data);
    }

    /**
     * 测试isTimeRecharge方法 - 定时充值判断
     */
    public function testIsTimeRecharge()
    {
        $tomorrow = date('Y-m-d H:i:s', strtotime('+1 day'));
        $data = ['定时充值' => $tomorrow];
        
        $this->service->isTimeRecharge($data);
        
        $this->assertTrue($this->service->isTimeRechargeEnabled());
        $this->assertEquals(strtotime($tomorrow), $this->service->getTimeRecharge());
    }

    /**
     * 测试isTimeRecharge方法 - 时间过期
     */
    public function testIsTimeRechargeWithPastTime()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('定时充值时间不能小于当前时间');
        
        $yesterday = date('Y-m-d H:i:s', strtotime('-1 day'));
        $data = ['定时充值' => $yesterday];
        
        $this->service->isTimeRecharge($data);
    }

    /**
     * 测试isTimeRecharge方法 - 时间超出范围
     */
    public function testIsTimeRechargeWithFarFuture()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('定时充值时间只能在今天和明天之间');
        
        $farFuture = date('Y-m-d H:i:s', strtotime('+3 days'));
        $data = ['定时充值' => $farFuture];
        
        $this->service->isTimeRecharge($data);
    }

    /**
     * 测试setTargetAdvertiserIds方法 - 设置目标账户
     */
    public function testSetTargetAdvertiserIds()
    {
        $targetId = '**********';
        
        $this->service->setTargetAdvertiserIds($targetId);
        
        $this->assertEquals($targetId, $this->service->getTargetAdvertiserId());
        $this->assertNotEmpty($this->service->getMainBody());
        $this->assertNotEmpty($this->service->getAdvertiserId());
    }

    /**
     * 测试setTargetAdvertiserIds方法 - 账户不存在
     */
    public function testSetTargetAdvertiserIdsNotFound()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('账户ID不存在系统中，请核对下账户ID是否正确');
        
        $this->service->setTargetAdvertiserIds('nonexistent_account');
    }

    /**
     * 测试amountLimit方法 - 金额限制检查
     */
    public function testAmountLimit()
    {
        $this->service->setTargetAdvertiserId('**********');
        $this->service->setAmount(100);
        $this->service->setPlatform(reportEnum::ADQ);
        
        $result = $this->service->amountLimit();
        
        $this->assertTrue($result);
    }

    /**
     * 测试success方法 - 成功处理
     */
    public function testSuccess()
    {
        $this->service->setTargetAdvertiserId('**********');
        $this->service->setAmount(100);
        $this->service->setUserName('测试用户');
        $this->service->setInsufficientBalance(5000);
        
        $this->service->success();
        
        $this->assertEquals(200, $this->service->getCode());
    }

    /**
     * 测试success方法 - 余额不足警告
     */
    public function testSuccessWithLowBalance()
    {
        $this->service->setTargetAdvertiserId('**********');
        $this->service->setAmount(100);
        $this->service->setUserName('测试用户');
        $this->service->setInsufficientBalance(2000); // 低于3000的警告线
        
        $this->service->success();
        
        $this->assertEquals(201, $this->service->getCode());
    }

    /**
     * 测试initialize方法 - 初始化
     */
    public function testInitialize()
    {
        // 设置一些值
        $this->service->setTargetAdvertiserId('test');
        $this->service->setAmount(100);
        
        // 执行初始化
        $this->service->initialize();
        
        // 验证值被重置
        $this->assertEmpty($this->service->getTargetAdvertiserId());
        $this->assertEquals(0, $this->service->getAmount());
        $this->assertEquals(422, $this->service->getCode());
    }

    /**
     * 测试resRealData方法 - 结果数据处理
     */
    public function testResRealData()
    {
        $res = [
            200 => [
                ['msg' => '充值成功', 'target_advertiser_id' => '**********']
            ],
            201 => [
                [
                    'msg' => '充值成功但余额不足',
                    'target_advertiser_id' => '**********',
                    'main_body' => '测试主体',
                    'advertiser_id' => '**********',
                    'insufficientNalance' => 2000
                ]
            ],
            422 => [
                ['msg' => '充值失败', 'target_advertiser_id' => '**********']
            ]
        ];

        $result = $this->service->resRealData($res);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey(200, $result);
        $this->assertArrayHasKey(201, $result);
        $this->assertArrayHasKey(422, $result);
        
        $this->assertEquals('充值成功', $result[200]['msg']);
        $this->assertStringContainsString('备用金仅剩', $result[201]['msg']);
        $this->assertStringContainsString('充值失败', $result[422]['msg']);
    }

    /**
     * 测试加粉充值流程
     */
    public function testAddFansRecharge()
    {
        $subAdvertiserId = '**********';
        
        // 模拟加粉充值
        $result = $this->service->addFansRecharge($subAdvertiserId);
        
        $this->assertTrue($result);
    }

    /**
     * 测试加粉充值频次检查
     */
    public function testAddFansRechargeFrequencyCheck()
    {
        $subAdvertiserId = '**********';
        
        // 模拟已达到频次限制
        $this->cacheManager->incrementAddFansTransferCount($subAdvertiserId, 5);
        
        $result = $this->service->checkAddFansTransferFrequency($subAdvertiserId);
        
        $this->assertFalse($result);
    }

    /**
     * 测试混合平台充值
     */
    public function testMixedPlatformRecharge()
    {
        $data = [
            'target_advertiser_ids' => ['tiktok_account', 'adq_account'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];

        // 这应该抛出异常，因为不允许混合平台充值
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('不允许多平台账户充值');
        
        $this->service->execute($data);
    }

    /**
     * 测试错误码体系
     */
    public function testErrorCodeSystem()
    {
        $this->assertEquals(200, $this->service->getSuccessCode());
        $this->assertEquals(100, $this->service->getTimeCode());
        $this->assertEquals(201, $this->service->getSuccessInsufficientBalanceCode());
        $this->assertEquals(422, $this->service->getErrorCodeIt());
        $this->assertEquals(423, $this->service->getErrorCodePromote());
        $this->assertEquals(424, $this->service->getErrorCodeInsufficientBalance());
    }

    /**
     * 测试平台适配器集成
     */
    public function testPlatformAdapterIntegration()
    {
        $mockAdapter = $this->platformFactory->create('mock');
        
        $this->assertInstanceOf(MockAdapter::class, $mockAdapter);
        
        // 测试充值操作
        $result = $mockAdapter->transferMoney('source', 'target', 100);
        $this->assertTrue($result['success']);
        
        // 测试余额查询
        $balance = $mockAdapter->getBalance('account');
        $this->assertIsFloat($balance);
    }

    /**
     * 测试验证器集成
     */
    public function testValidatorIntegration()
    {
        $validator = TransferValidator::createDefault();
        
        $validData = [
            'target_advertiser_ids' => ['**********'],
            'amount' => 100,
            'user_name' => '测试用户'
        ];
        
        $result = $validator->validate($validData);
        $this->assertTrue($result);
    }

    /**
     * 测试缓存管理器集成
     */
    public function testCacheManagerIntegration()
    {
        $targetId = '**********';
        $amount = 100;
        $userName = '测试用户';
        
        // 测试记录成功充值
        $result = $this->cacheManager->recordSuccessfulTransfer($targetId, $amount, $userName);
        $this->assertTrue($result);
        
        // 测试小时限额检查
        $result = $this->cacheManager->checkHourlyLimit($targetId, $amount, 3000);
        $this->assertTrue($result);
    }

    /**
     * 创建带有Mock时间的服务实例
     * 
     * @param string $mockTime 模拟时间
     * @return TransferMoneyServiceV2
     */
    private function createServiceWithMockedTime(string $mockTime): TransferMoneyServiceV2
    {
        // 这里应该创建一个Mock的服务实例，重写时间检查方法
        // 实际实现中可能需要使用依赖注入或其他Mock技术
        return $this->service;
    }

    /**
     * 测试服务状态管理
     */
    public function testServiceStateManagement()
    {
        // 测试初始状态
        $this->assertEquals(422, $this->service->getCode());
        
        // 测试状态变更
        $this->service->setCode(200);
        $this->assertEquals(200, $this->service->getCode());
        
        // 测试重置状态
        $this->service->initialize();
        $this->assertEquals(422, $this->service->getCode());
    }

    /**
     * 测试并发安全性
     */
    public function testConcurrencySafety()
    {
        // 模拟并发充值场景
        $targetId = '**********';
        $amount = 100;
        
        // 这里应该测试并发情况下的缓存一致性
        // 实际测试中可能需要使用多线程或进程模拟
        $this->assertTrue(true); // 占位测试
    }

    /**
     * 测试异常恢复机制
     */
    public function testExceptionRecovery()
    {
        // 模拟异常情况
        try {
            $this->service->transferMoney();
            $this->fail('应该抛出异常');
        } catch (Exception $e) {
            // 验证异常后服务状态正确
            $this->assertEquals(422, $this->service->getCode());
        }
    }

    /**
     * 测试日志记录
     */
    public function testLogging()
    {
        // 这里应该测试各种操作的日志记录
        // 实际测试中需要Mock Yii的日志组件
        $this->assertTrue(true); // 占位测试
    }
}