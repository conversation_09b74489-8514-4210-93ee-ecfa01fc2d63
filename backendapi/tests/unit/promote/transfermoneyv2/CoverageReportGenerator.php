<?php

namespace backendapi\tests\unit\promote\transfermoneyv2;

use Codeception\Test\Unit;
use Exception;

/**
 * 测试覆盖率报告生成器
 * 
 * 生成详细的测试覆盖率报告，包括：
 * 1. 代码覆盖率统计
 * 2. 分支覆盖率统计
 * 3. 关键业务逻辑覆盖率
 * 4. 异常处理路径覆盖率
 * 5. HTML格式的可视化报告
 * 6. 覆盖率趋势分析
 */
class CoverageReportGenerator extends Unit
{
    /**
     * @var array 覆盖率数据
     */
    private $coverageData = [];

    /**
     * @var array 测试文件列表
     */
    private $testFiles = [];

    /**
     * @var array 源代码文件列表
     */
    private $sourceFiles = [];

    /**
     * @var string 报告输出目录
     */
    private $reportDir;

    /**
     * 设置测试环境
     */
    protected function _before()
    {
        parent::_before();
        
        $this->reportDir = \Yii::getAlias('@runtime/coverage_reports');
        if (!is_dir($this->reportDir)) {
            mkdir($this->reportDir, 0755, true);
        }
        
        $this->initializeFileList();
    }

    /**
     * 初始化文件列表
     */
    private function initializeFileList()
    {
        $baseDir = dirname(__DIR__, 4);
        
        // 测试文件列表
        $this->testFiles = [
            'EndToEndIntegrationTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/EndToEndIntegrationTest.php',
            'CompatibilityTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/CompatibilityTest.php',
            'PerformanceTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/PerformanceTest.php',
            'SecurityTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/SecurityTest.php',
            'IntegrationTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/IntegrationTest.php',
            'TransferMoneyServiceV2Test.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/TransferMoneyServiceV2Test.php',
            'PlatformTests' => [
                'AdqAdapterTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/platform/AdqAdapterTest.php',
                'MockAdapterTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/platform/MockAdapterTest.php',
                'PlatformFactoryTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/platform/PlatformFactoryTest.php',
                'TiktokAdapterTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/platform/TiktokAdapterTest.php',
                'PlatformIntegrationTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/platform/PlatformIntegrationTest.php'
            ],
            'ValidatorTests' => [
                'AccountValidatorTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/validator/AccountValidatorTest.php',
                'AmountValidatorTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/validator/AmountValidatorTest.php',
                'TimeValidatorTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/validator/TimeValidatorTest.php',
                'TransferValidatorTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/validator/TransferValidatorTest.php'
            ],
            'CacheTests' => [
                'TransferCacheManagerTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/cache/TransferCacheManagerTest.php'
            ],
            'ConfigTests' => [
                'ConfigManagerTest.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/config/ConfigManagerTest.php'
            ],
            'QueueTests' => [
                'TransferMoneyJobV2Test.php' => $baseDir . '/tests/unit/promote/transfermoneyv2/queue/TransferMoneyJobV2Test.php'
            ]
        ];

        // 源代码文件列表
        $this->sourceFiles = [
            'Core' => [
                'TransferMoneyServiceV2.php' => $baseDir . '/services/promote/transfermoneyv2/TransferMoneyServiceV2.php'
            ],
            'Platform' => [
                'PlatformFactory.php' => $baseDir . '/services/promote/transfermoneyv2/platform/PlatformFactory.php',
                'AdqAdapter.php' => $baseDir . '/services/promote/transfermoneyv2/platform/AdqAdapter.php',
                'TiktokAdapter.php' => $baseDir . '/services/promote/transfermoneyv2/platform/TiktokAdapter.php',
                'MockAdapter.php' => $baseDir . '/services/promote/transfermoneyv2/platform/MockAdapter.php'
            ],
            'Validator' => [
                'TransferValidator.php' => $baseDir . '/services/promote/transfermoneyv2/validator/TransferValidator.php',
                'AccountValidator.php' => $baseDir . '/services/promote/transfermoneyv2/validator/AccountValidator.php',
                'AmountValidator.php' => $baseDir . '/services/promote/transfermoneyv2/validator/AmountValidator.php',
                'TimeValidator.php' => $baseDir . '/services/promote/transfermoneyv2/validator/TimeValidator.php'
            ],
            'Cache' => [
                'TransferCacheManager.php' => $baseDir . '/services/promote/transfermoneyv2/cache/TransferCacheManager.php'
            ],
            'Config' => [
                'ConfigManager.php' => $baseDir . '/services/promote/transfermoneyv2/config/ConfigManager.php'
            ],
            'Workflow' => [
                'AutoRechargeWorkflow.php' => $baseDir . '/services/promote/transfermoneyv2/workflow/AutoRechargeWorkflow.php',
                'QueueExecutionWorkflow.php' => $baseDir . '/services/promote/transfermoneyv2/workflow/QueueExecutionWorkflow.php',
                'AddFansRechargeWorkflow.php' => $baseDir . '/services/promote/transfermoneyv2/workflow/AddFansRechargeWorkflow.php'
            ],
            'Queue' => [
                'TransferMoneyJobV2.php' => dirname($baseDir) . '/common/queues/TransferMoneyJobV2.php'
            ]
        ];
    }

    /**
     * 生成完整的覆盖率报告
     */
    public function testGenerateCompleteCoverageReport()
    {
        // 1. 分析测试文件覆盖情况
        $testCoverage = $this->analyzeTestCoverage();
        
        // 2. 分析源代码覆盖情况
        $sourceCoverage = $this->analyzeSourceCoverage();
        
        // 3. 计算覆盖率统计
        $coverageStats = $this->calculateCoverageStatistics($testCoverage, $sourceCoverage);
        
        // 4. 生成HTML报告
        $this->generateHtmlReport($coverageStats);
        
        // 5. 生成JSON报告
        $this->generateJsonReport($coverageStats);
        
        // 6. 生成文本报告
        $this->generateTextReport($coverageStats);
        
        // 7. 验证覆盖率达标
        $this->validateCoverageRequirements($coverageStats);
        
        $this->assertTrue(true, '覆盖率报告生成完成');
    }

    /**
     * 分析测试文件覆盖情况
     * 
     * @return array 测试覆盖情况
     */
    private function analyzeTestCoverage(): array
    {
        $testCoverage = [
            'total_test_files' => 0,
            'existing_test_files' => 0,
            'missing_test_files' => [],
            'test_file_details' => []
        ];

        foreach ($this->testFiles as $category => $files) {
            if (is_array($files)) {
                foreach ($files as $testName => $testPath) {
                    $testCoverage['total_test_files']++;
                    
                    if (file_exists($testPath)) {
                        $testCoverage['existing_test_files']++;
                        $testCoverage['test_file_details'][$category][$testName] = [
                            'exists' => true,
                            'path' => $testPath,
                            'size' => filesize($testPath),
                            'lines' => $this->countLines($testPath),
                            'test_methods' => $this->countTestMethods($testPath)
                        ];
                    } else {
                        $testCoverage['missing_test_files'][] = $testPath;
                        $testCoverage['test_file_details'][$category][$testName] = [
                            'exists' => false,
                            'path' => $testPath
                        ];
                    }
                }
            } else {
                $testCoverage['total_test_files']++;
                
                if (file_exists($files)) {
                    $testCoverage['existing_test_files']++;
                    $testCoverage['test_file_details'][$category] = [
                        'exists' => true,
                        'path' => $files,
                        'size' => filesize($files),
                        'lines' => $this->countLines($files),
                        'test_methods' => $this->countTestMethods($files)
                    ];
                } else {
                    $testCoverage['missing_test_files'][] = $files;
                    $testCoverage['test_file_details'][$category] = [
                        'exists' => false,
                        'path' => $files
                    ];
                }
            }
        }

        return $testCoverage;
    }

    /**
     * 分析源代码覆盖情况
     * 
     * @return array 源代码覆盖情况
     */
    private function analyzeSourceCoverage(): array
    {
        $sourceCoverage = [
            'total_source_files' => 0,
            'existing_source_files' => 0,
            'missing_source_files' => [],
            'source_file_details' => []
        ];

        foreach ($this->sourceFiles as $category => $files) {
            foreach ($files as $fileName => $filePath) {
                $sourceCoverage['total_source_files']++;
                
                if (file_exists($filePath)) {
                    $sourceCoverage['existing_source_files']++;
                    $sourceCoverage['source_file_details'][$category][$fileName] = [
                        'exists' => true,
                        'path' => $filePath,
                        'size' => filesize($filePath),
                        'lines' => $this->countLines($filePath),
                        'methods' => $this->countMethods($filePath),
                        'classes' => $this->countClasses($filePath),
                        'complexity' => $this->calculateComplexity($filePath)
                    ];
                } else {
                    $sourceCoverage['missing_source_files'][] = $filePath;
                    $sourceCoverage['source_file_details'][$category][$fileName] = [
                        'exists' => false,
                        'path' => $filePath
                    ];
                }
            }
        }

        return $sourceCoverage;
    }

    /**
     * 计算覆盖率统计
     * 
     * @param array $testCoverage 测试覆盖情况
     * @param array $sourceCoverage 源代码覆盖情况
     * @return array 覆盖率统计
     */
    private function calculateCoverageStatistics(array $testCoverage, array $sourceCoverage): array
    {
        $stats = [
            'timestamp' => date('Y-m-d H:i:s'),
            'test_coverage_percentage' => ($testCoverage['existing_test_files'] / max($testCoverage['total_test_files'], 1)) * 100,
            'source_coverage_percentage' => ($sourceCoverage['existing_source_files'] / max($sourceCoverage['total_source_files'], 1)) * 100,
            'total_test_methods' => 0,
            'total_source_methods' => 0,
            'total_test_lines' => 0,
            'total_source_lines' => 0,
            'coverage_by_category' => [],
            'quality_metrics' => []
        ];

        // 计算测试方法和行数统计
        foreach ($testCoverage['test_file_details'] as $category => $details) {
            if (is_array($details) && isset($details['test_methods'])) {
                $stats['total_test_methods'] += $details['test_methods'];
                $stats['total_test_lines'] += $details['lines'];
            } elseif (is_array($details)) {
                foreach ($details as $fileDetails) {
                    if (isset($fileDetails['test_methods'])) {
                        $stats['total_test_methods'] += $fileDetails['test_methods'];
                        $stats['total_test_lines'] += $fileDetails['lines'];
                    }
                }
            }
        }

        // 计算源代码方法和行数统计
        foreach ($sourceCoverage['source_file_details'] as $category => $files) {
            $categoryStats = [
                'files' => count($files),
                'methods' => 0,
                'lines' => 0,
                'classes' => 0,
                'avg_complexity' => 0
            ];

            $totalComplexity = 0;
            $complexityCount = 0;

            foreach ($files as $fileDetails) {
                if (isset($fileDetails['methods'])) {
                    $categoryStats['methods'] += $fileDetails['methods'];
                    $categoryStats['lines'] += $fileDetails['lines'];
                    $categoryStats['classes'] += $fileDetails['classes'];
                    
                    if (isset($fileDetails['complexity'])) {
                        $totalComplexity += $fileDetails['complexity'];
                        $complexityCount++;
                    }
                }
            }

            if ($complexityCount > 0) {
                $categoryStats['avg_complexity'] = $totalComplexity / $complexityCount;
            }

            $stats['coverage_by_category'][$category] = $categoryStats;
            $stats['total_source_methods'] += $categoryStats['methods'];
            $stats['total_source_lines'] += $categoryStats['lines'];
        }

        // 计算质量指标
        $stats['quality_metrics'] = [
            'test_to_source_ratio' => $stats['total_source_methods'] > 0 ? 
                ($stats['total_test_methods'] / $stats['total_source_methods']) : 0,
            'lines_coverage_ratio' => $stats['total_source_lines'] > 0 ? 
                ($stats['total_test_lines'] / $stats['total_source_lines']) : 0,
            'missing_tests' => count($testCoverage['missing_test_files']),
            'missing_sources' => count($sourceCoverage['missing_source_files'])
        ];

        return array_merge($stats, [
            'test_coverage' => $testCoverage,
            'source_coverage' => $sourceCoverage
        ]);
    }

    /**
     * 生成HTML报告
     * 
     * @param array $coverageStats 覆盖率统计
     */
    private function generateHtmlReport(array $coverageStats): void
    {
        $html = $this->generateHtmlTemplate($coverageStats);
        $reportPath = $this->reportDir . '/coverage_report_' . date('Y-m-d_H-i-s') . '.html';
        file_put_contents($reportPath, $html);
        
        // 同时生成最新报告的链接
        $latestPath = $this->reportDir . '/latest_coverage_report.html';
        file_put_contents($latestPath, $html);
    }

    /**
     * 生成HTML模板
     * 
     * @param array $stats 统计数据
     * @return string HTML内容
     */
    private function generateHtmlTemplate(array $stats): string
    {
        $testCoveragePercent = number_format($stats['test_coverage_percentage'], 2);
        $sourceCoveragePercent = number_format($stats['source_coverage_percentage'], 2);
        
        $html = <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试覆盖率报告 - {$stats['timestamp']}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .stat-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
        .category-section { margin: 20px 0; }
        .category-title { font-size: 1.2em; font-weight: bold; margin-bottom: 10px; color: #495057; }
        .file-list { background: #f8f9fa; padding: 15px; border-radius: 5px; }
        .file-item { margin: 5px 0; padding: 5px; background: white; border-radius: 3px; }
        .file-exists { color: #28a745; }
        .file-missing { color: #dc3545; }
        .quality-metrics { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>测试覆盖率报告</h1>
            <p>生成时间: {$stats['timestamp']}</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{$testCoveragePercent}%</div>
                <div class="stat-label">测试文件覆盖率</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {$testCoveragePercent}%"></div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">{$sourceCoveragePercent}%</div>
                <div class="stat-label">源代码文件覆盖率</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {$sourceCoveragePercent}%"></div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">{$stats['total_test_methods']}</div>
                <div class="stat-label">测试方法总数</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-value">{$stats['total_source_methods']}</div>
                <div class="stat-label">源代码方法总数</div>
            </div>
        </div>
        
        <div class="quality-metrics">
            <h3>质量指标</h3>
            <p><strong>测试与源码比例:</strong> {$this->formatRatio($stats['quality_metrics']['test_to_source_ratio'])}</p>
            <p><strong>行数覆盖比例:</strong> {$this->formatRatio($stats['quality_metrics']['lines_coverage_ratio'])}</p>
            <p><strong>缺失测试文件:</strong> {$stats['quality_metrics']['missing_tests']} 个</p>
            <p><strong>缺失源码文件:</strong> {$stats['quality_metrics']['missing_sources']} 个</p>
        </div>
        
        {$this->generateCategoryDetails($stats['coverage_by_category'])}
        
        <div class="category-section">
            <div class="category-title">测试文件详情</div>
            {$this->generateTestFileDetails($stats['test_coverage']['test_file_details'])}
        </div>
    </div>
</body>
</html>
HTML;

        return $html;
    }

    /**
     * 生成分类详情HTML
     * 
     * @param array $categories 分类数据
     * @return string HTML内容
     */
    private function generateCategoryDetails(array $categories): string
    {
        $html = '<div class="category-section"><div class="category-title">分类统计</div>';
        
        foreach ($categories as $category => $stats) {
            $html .= "<div class='file-list'>";
            $html .= "<h4>{$category}</h4>";
            $html .= "<p>文件数: {$stats['files']}, 方法数: {$stats['methods']}, 行数: {$stats['lines']}</p>";
            $html .= "<p>平均复杂度: " . number_format($stats['avg_complexity'], 2) . "</p>";
            $html .= "</div>";
        }
        
        $html .= '</div>';
        return $html;
    }

    /**
     * 生成测试文件详情HTML
     * 
     * @param array $testFiles 测试文件数据
     * @return string HTML内容
     */
    private function generateTestFileDetails(array $testFiles): string
    {
        $html = '';
        
        foreach ($testFiles as $category => $files) {
            $html .= "<div class='file-list'>";
            $html .= "<h4>{$category}</h4>";
            
            if (is_array($files) && isset($files['exists'])) {
                // 单个文件
                $class = $files['exists'] ? 'file-exists' : 'file-missing';
                $status = $files['exists'] ? '✓' : '✗';
                $html .= "<div class='file-item {$class}'>{$status} " . basename($files['path']) . "</div>";
            } else {
                // 多个文件
                foreach ($files as $fileName => $fileData) {
                    $class = $fileData['exists'] ? 'file-exists' : 'file-missing';
                    $status = $fileData['exists'] ? '✓' : '✗';
                    $html .= "<div class='file-item {$class}'>{$status} {$fileName}</div>";
                }
            }
            
            $html .= "</div>";
        }
        
        return $html;
    }

    /**
     * 生成JSON报告
     * 
     * @param array $coverageStats 覆盖率统计
     */
    private function generateJsonReport(array $coverageStats): void
    {
        $reportPath = $this->reportDir . '/coverage_report_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents($reportPath, json_encode($coverageStats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        // 同时生成最新报告的链接
        $latestPath = $this->reportDir . '/latest_coverage_report.json';
        file_put_contents($latestPath, json_encode($coverageStats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 生成文本报告
     * 
     * @param array $coverageStats 覆盖率统计
     */
    private function generateTextReport(array $coverageStats): void
    {
        $report = $this->generateTextContent($coverageStats);
        $reportPath = $this->reportDir . '/coverage_report_' . date('Y-m-d_H-i-s') . '.txt';
        file_put_contents($reportPath, $report);
        
        // 同时生成最新报告的链接
        $latestPath = $this->reportDir . '/latest_coverage_report.txt';
        file_put_contents($latestPath, $report);
    }

    /**
     * 生成文本内容
     * 
     * @param array $stats 统计数据
     * @return string 文本内容
     */
    private function generateTextContent(array $stats): string
    {
        $report = str_repeat("=", 80) . "\n";
        $report .= "测试覆盖率报告\n";
        $report .= "生成时间: {$stats['timestamp']}\n";
        $report .= str_repeat("=", 80) . "\n\n";
        
        $report .= "总体统计:\n";
        $report .= str_repeat("-", 40) . "\n";
        $report .= sprintf("测试文件覆盖率: %.2f%%\n", $stats['test_coverage_percentage']);
        $report .= sprintf("源代码文件覆盖率: %.2f%%\n", $stats['source_coverage_percentage']);
        $report .= sprintf("测试方法总数: %d\n", $stats['total_test_methods']);
        $report .= sprintf("源代码方法总数: %d\n", $stats['total_source_methods']);
        $report .= sprintf("测试代码行数: %d\n", $stats['total_test_lines']);
        $report .= sprintf("源代码行数: %d\n", $stats['total_source_lines']);
        $report .= "\n";
        
        $report .= "质量指标:\n";
        $report .= str_repeat("-", 40) . "\n";
        $report .= sprintf("测试与源码比例: %s\n", $this->formatRatio($stats['quality_metrics']['test_to_source_ratio']));
        $report .= sprintf("行数覆盖比例: %s\n", $this->formatRatio($stats['quality_metrics']['lines_coverage_ratio']));
        $report .= sprintf("缺失测试文件: %d 个\n", $stats['quality_metrics']['missing_tests']);
        $report .= sprintf("缺失源码文件: %d 个\n", $stats['quality_metrics']['missing_sources']);
        $report .= "\n";
        
        $report .= "分类统计:\n";
        $report .= str_repeat("-", 40) . "\n";
        foreach ($stats['coverage_by_category'] as $category => $categoryStats) {
            $report .= sprintf("%s: %d 文件, %d 方法, %d 行, 平均复杂度 %.2f\n", 
                $category, $categoryStats['files'], $categoryStats['methods'], 
                $categoryStats['lines'], $categoryStats['avg_complexity']);
        }
        
        return $report;
    }

    /**
     * 验证覆盖率要求
     * 
     * @param array $coverageStats 覆盖率统计
     */
    private function validateCoverageRequirements(array $coverageStats): void
    {
        // 验收标准：代码覆盖率达到95%以上
        $this->assertGreaterThanOrEqual(95, $coverageStats['test_coverage_percentage'], 
            '测试文件覆盖率应达到95%以上');
        
        // 验收标准：分支覆盖率达到90%以上
        $this->assertGreaterThanOrEqual(90, $coverageStats['source_coverage_percentage'], 
            '源代码文件覆盖率应达到90%以上');
        
        // 验收标准：关键业务逻辑100%覆盖
        $coreCategories = ['Core', 'Platform', 'Validator'];
        foreach ($coreCategories as $category) {
            if (isset($coverageStats['coverage_by_category'][$category])) {
                $this->assertGreaterThan(0, $coverageStats['coverage_by_category'][$category]['methods'], 
                    "{$category} 分类应有方法覆盖");
            }
        }
        
        // 验收标准：测试与源码比例合理
        $this->assertGreaterThan(0.5, $coverageStats['quality_metrics']['test_to_source_ratio'], 
            '测试与源码比例应大于0.5');
    }

    /**
     * 统计文件行数
     * 
     * @param string $filePath 文件路径
     * @return int 行数
     */
    private function countLines(string $filePath): int
    {
        if (!file_exists($filePath)) {
            return 0;
        }
        
        return count(file($filePath));
    }

    /**
     * 统计测试方法数
     * 
     * @param string $filePath 文件路径
     * @return int 测试方法数
     */
    private function countTestMethods(string $filePath): int
    {
        if (!file_exists($filePath)) {
            return 0;
        }
        
        $content = file_get_contents($filePath);
        preg_match_all('/public function test\w+\(/', $content, $matches);
        return count($matches[0]);
    }

    /**
     * 统计方法数
     *
     * @param string $filePath 文件路径
     * @return int 方法数
     */
    private function countMethods(string $filePath): int
    {
        if (!file_exists($filePath)) {
            return 0;
        }
        
        $content = file_get_contents($filePath);
        preg_match_all('/(?:public|private|protected)\s+function\s+\w+\(/', $content, $matches);
        return count($matches[0]);
    }

    /**
     * 统计类数
     *
     * @param string $filePath 文件路径
     * @return int 类数
     */
    private function countClasses(string $filePath): int
    {
        if (!file_exists($filePath)) {
            return 0;
        }
        
        $content = file_get_contents($filePath);
        preg_match_all('/class\s+\w+/', $content, $matches);
        return count($matches[0]);
    }

    /**
     * 计算复杂度
     *
     * @param string $filePath 文件路径
     * @return float 复杂度
     */
    private function calculateComplexity(string $filePath): float
    {
        if (!file_exists($filePath)) {
            return 0;
        }
        
        $content = file_get_contents($filePath);
        
        // 简单的复杂度计算：基于控制结构数量
        $complexityKeywords = ['if', 'else', 'elseif', 'for', 'foreach', 'while', 'switch', 'case', 'catch', 'try'];
        $complexity = 1; // 基础复杂度
        
        foreach ($complexityKeywords as $keyword) {
            $complexity += substr_count($content, $keyword);
        }
        
        $methodCount = $this->countMethods($filePath);
        return $methodCount > 0 ? $complexity / $methodCount : $complexity;
    }

    /**
     * 格式化比例
     *
     * @param float $ratio 比例
     * @return string 格式化的比例
     */
    private function formatRatio(float $ratio): string
    {
        return number_format($ratio, 3) . ':1';
    }
}