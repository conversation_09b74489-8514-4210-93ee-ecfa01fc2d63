<?php

namespace backendapi\services\complaint;

use auth\services\complaint\ComplaintService as AuthComplaintService;
use common\enums\complaint\StatusEnum;
use common\enums\complaint\TypeEnum;
use common\helpers\ArrayHelper;
use common\helpers\Tool;
use common\models\complaint\Complaint;
use common\models\complaint\Log;
use common\models\complaint\Reason;
use common\models\Config;
use common\models\wxcom\Com;
use common\models\wxcom\User;
use Exception;
use Yii;

class ComplaintService extends AuthComplaintService
{
    /**
     * @var Complaint
     */
    public static $modelClass = Complaint::class;

    public static function complaintList($params, $isExport = false)
    {
        $arr = [
            'statusText',
            'imgText' => 'img',
            'typeText' => 'type',
            'comName' => 'com_name',
            'mobileText' => 'mobile',
            'reason.content' => 'reason_name',
            'store.store_name' => 'store_name',
            'respondent.name' => 'respondent_name',
            'sourceTypeText' => 'source_type_text',
            'respondentUser.username' => 'user_name',
            'respondentUser.jobnumber' => 'job_number',
            'respondent.avatar' => 'respondent_avatar',
            'principal.username' => 'principal_username',
        ];
        if ($isExport) {
            unset($arr['mobileText']);
        }
        self::$modelClass::setExtendAttrs($arr);
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;
        $query = self::$modelClass::find();
        $query->with(['principal', 'store', 'reason', 'respondent']);
        $query->andFilterWhere(['=', 'status', $params['status'] ?? '']);
        $query->andFilterWhere(['=', 'reason_id', $params['reason_id'] ?? '']);
        $query->andFilterWhere(['=', 'source_type', $params['source_type'] ?? '']);
        $query->andFilterWhere(['=', 'respondent_user_id', $params['respondent_user_id'] ?? '']);
        $query->andFilterWhere(['=', 'principal_id', $params['principal_id'] ?? '']);
        $query->andWhere(['=', 'entity_id', Yii::$app->user->identity->current_entity_id]);
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $query->andWhere(['between', 'created_at', $params['start_time'], $params['end_time']]);
        }
        if (!empty($params['customer'])) {
            $query->andWhere(['or', ['like', 'name', $params['customer'] ?? ''], ['like', 'mobile', $params['customer'] ?? '']]);
        }
        if ($isExport) {
            if (!empty($params['getTotal'])) return ['list' => 0, 'totalCount' => $query->count()];//获取总条数
            $totalCount = 0;
        } else {
            $totalCount = $query->count();
        }
        $list = $query->offset($offset)->limit($limit)->orderBy('status ASC, created_at DESC')->all();
        if ($isExport) {
            $list = ArrayHelper::toArray($list);
            $ids = array_column($list, 'id');
            $logs = ArrayHelper::toArray(self::getLog($ids));
            foreach ($list as &$val) {
                $id = $val['id'] ?? 0;
                $log = '';
                foreach ($logs as $v) {
                    $status = $v['status'] ?? 0;
                    $content = $v['content'] ?? '';
                    $createdAt = $v['created_at'] ?? 0;
                    $complaintId = $v['complaint_id'] ?? 0;
                    if ($complaintId != $id) {
                        continue;
                    }
                    $statusName = StatusEnum::getValue($status);
                    $processingUsername = $v['processing_username'] ?? '';
                    $log = $status == 0 ? ($content . '|' . date('Y-m-d H:i:s', $createdAt) . PHP_EOL . $log) : ($log . $processingUsername . '|' . date('Y-m-d H:i:s', $createdAt) . '|' . $statusName . '|' . $v['content'] . PHP_EOL);
                }
                $val['log'] = $log;
            }
        }
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    public static function info($id)
    {
        self::$modelClass::setExtendAttrs([
            'statusText',
            'imgText' => 'img',
            'typeText' => 'type',
            'comName' => 'com_name',
            'mobileText' => 'mobile',
            'reason.content' => 'reason_name',
            'store.store_name' => 'store_name',
            'respondent.name' => 'respondent_name',
            'sourceTypeText' => 'source_type_text',
            'respondentUser.username' => 'user_name',
            'respondentUser.jobnumber' => 'job_number',
            'respondent.avatar' => 'respondent_avatar',
            'principal.username' => 'principal_username',
        ]);
        $query = self::$modelClass::find();
        return $query->orFilterWhere(['=', 'id', $id])->one();
    }

    public static function getLog($id)
    {
        Log::setExtendAttrs([
            'statusText',
            'processing.username' => 'processing_username'
        ]);
        $query = Log::find();
        return $query->with(['processing'])->orFilterWhere(['in', 'complaint_id', $id])->orderBy('created_at ASC')->all();
    }

    /**
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function dealWith($params)
    {
        if (empty($params['content'])) {
            throw new Exception('投诉内容不能为空');
        }
        if (empty($params['complaint_id'])) {
            throw new Exception('投诉表id不能为空');
        }
        if (empty($params['is_processing'])) {
            throw new Exception('是否处理完成不能为空');
        }
        $complaint = complaint::findOne($params['complaint_id']);
        if (empty($complaint)) {
            throw new Exception('投诉数据不存在');
        }
        if ($complaint->status == StatusEnum::PROCESSING_COMPLETED) {
            throw new Exception('投诉数据已经处理完成');
        }
        if (!in_array($params['is_processing'], array(StatusEnum::PROCESSING_COMPLETED, StatusEnum::PROCESSING))) {
            throw new Exception('处理状态范围错误');
        }
        $complaint->scenario = 'status';
        $complaint->status = $params['is_processing'];
        $complaint->principal_id = Yii::$app->user->id ?? 0;
        if (!$complaint->save()) {
            throw new Exception(current($complaint->getFirstErrors()));
        }
        $model = new Log();
        $model->content = $params['content'] ?? '';
        $model->complaint_id = $params['complaint_id'] ?? 0;
        $model->status = $params['is_processing'] ?? 0;
        $model->processing_id = Yii::$app->user->id ?? 0;
        if (!$model->save()) {
            throw new Exception(current($complaint->getFirstErrors()));
        }
        return true;
    }

    public static function updateUserWxcom($userId = 0, $entityId = 1)
    {
        $systemDomain = Config::find()->where(['name' => 'systemDomain'])->andFilterWhere(['=', 'entity_id', $entityId])->one();
        if (empty($systemDomain)) {
            throw new Exception('域名未配置');
        }
        $com = Com::find()->where(['status' => \common\enums\StatusEnum::ENABLED])->andWhere(['entity_id' => $entityId])->indexBy('id')->asArray()->all();
        $model = User::find();
        if ($userId > 0) {
            $model = $model->andFilterWhere(['=', 'id', $userId]);
        }
        $data = $model->asArray()->all();
        foreach ($data as $v) {
            $id = $v['id'] ?? 0;
            $userId = $v['wxcom_user_id'] ?? '';
            $comId = $v['com_id'] ?? 0;
            if (!isset($com[$comId])) {
                Yii::$app->notice->important('客服投诉建议脚本设置失败,id:' . $id . '企微配置为空');
                continue;
            }
            $tokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={$com[$comId]['corp_id']}&corpsecret={$com[$comId]['con_secret']}";
            $result = Tool::curlRequest($tokenUrl);
            $token = $result['access_token'];
            $url = "https://qyapi.weixin.qq.com/cgi-bin/user/update?access_token={$token}";
            $param = [
                'userid' => $userId,
                'external_profile' => [
                    'external_attr' => [
                        [
                            "type" => 1,
                            "name" => "意见反馈",
                            "web" => [
                                "url" => $systemDomain->value . "mobile/#/complaintAdvice?wxcomUser={$id}&wxcomCom={$com[$comId]['id']}&entityId={$com[$comId]['entity_id']}&comCode={$com[$comId]['code']}",
                                "title" => "建议反馈"
                            ]
                        ]
                    ]
                ]
            ];
            $result = Tool::curlRequest($url, $param, true);
            $errCode = $result['errcode'] ?? '';
            if ($errCode != 0) {
                Yii::$app->feishuNotice->text('客服投诉建议脚本设置失败,id:' . $id . '。企微api接口返回信息：' . json_encode($result));
            }
        }
        return true;
    }

    public static function reasonSave($params)
    {
        $sort = $params['sort'] ?? 0;
        if ($sort < 0 || $sort > 255) {
            throw new Exception('排序取值范围只能0~255');
        }
        $type = $params['type'] ?? 1;
        if (!in_array($type, [TypeEnum::CUSTOMER_SERVICE, TypeEnum::STORE])) {
            throw new Exception('取值范围错误');
        }
        if (isset($params['id'])) {
            $model = Reason::findOne($params['id']);
            if (empty($model)) {
                throw new Exception('数据不存在');
            }
        } else {
            $model = new Reason();
        }
        $model->content = $params['content'] ?? '';
        $model->type = $type;
        $model->entity_id = Yii::$app->user->identity->current_entity_id;
        $model->sort = $sort;
        if (!$model->save()) {
            throw new Exception($model->getFirstError());
        }
        return true;
    }

    public static function reasonList($params, $isAll = false): array
    {
        $query = Reason::find();
        $query->andWhere(['=', 'entity_id', Yii::$app->user->identity->current_entity_id]);
        if ($isAll) {
            return ['list' => $query->select('id,content')->andWhere(['=', 'status', \common\enums\StatusEnum::ENABLED])->all()];
        } else {
            Reason::setExtendAttrs([
                'statusText',
            ]);
            $page = ArrayHelper::getValue($params, 'page', 1);   //页码
            $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
            $offset = ($page - 1) * $limit;
            $list = $query->offset($offset)->limit($limit)->all();
            $totalCount = $query->count();
            return ['list' => $list, 'totalCount' => $totalCount];
        }
    }

    public static function reasonStatusUpdate($id, $status)
    {
        $model = Reason::findOne($id);
        if (empty($model)) {
            throw new Exception('数据不存在');
        }
        if (!in_array($status, array(0, 1))) {
            throw new Exception('状态取值错误');
        }
        $model->status = $status;
        if (!$model->save()) {
            throw new Exception(current($model->getFirstErrors()));
        }
        return true;
    }
}
