# 第五阶段：队列任务和配置系统实现

## 概述

第五阶段实现了新的队列任务处理类 `TransferMoneyJobV2` 和完整的配置文件系统，基于现有 `TransferMoneyJob` 的业务逻辑，使用新的 `TransferMoneyServiceV2` 服务，保持与现有系统的完全兼容性。

## 实现内容

### 1. 队列任务实现

#### TransferMoneyJobV2 类
- **位置**: `common/queues/TransferMoneyJobV2.php`
- **功能**: 基于现有 `TransferMoneyJob` 的业务逻辑，使用新的服务架构
- **特性**:
  - 支持立即充值和定时充值
  - 支持加粉后自动充值
  - 支持批量充值处理
  - 支持充值频次控制
  - 支持消息通知机制

#### 核心方法

```php
// 添加任务到队列
TransferMoneyJobV2::addJob(array $data)

// 添加加粉充值任务
TransferMoneyJobV2::addFansJob($sub_advertiser_id)

// 检查充值频次
TransferMoneyJobV2::checkTransferMoneyCount($sub_advertiser_id)

// 批量添加任务
TransferMoneyJobV2::addBatchJobs(array $batchData)
```

### 2. 配置文件系统

#### 配置文件结构
```
backendapi/services/promote/transfermoneyv2/config/
├── platform_config.php      # 平台配置
├── transfer_limits.php       # 充值限制配置
├── budget_rules.php         # 预算规则配置
└── ConfigManager.php        # 配置管理器
```

#### 平台配置 (platform_config.php)
- 平台名称和适配器类映射
- 各平台的充值限额配置
- 平台特定参数配置

```php
'platforms' => [
    'tiktok' => [
        'name' => '抖音',
        'adapter_class' => 'backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter',
        'enabled' => true,
    ],
    'adq' => [
        'name' => 'ADQ',
        'adapter_class' => 'backendapi\services\promote\transfermoneyv2\platform\AdqAdapter',
        'enabled' => true,
    ],
],
'platform_limits' => [
    'tiktok' => [
        'single_recharge_amount' => 1000,
        'hourly_max_amount' => 3000,
    ],
    'adq' => [
        'single_recharge_amount' => 2000,
        'hourly_max_amount' => 20000,
    ],
],
```

#### 充值限制配置 (transfer_limits.php)
- 时间限制规则（凌晨2:00-6:30禁止充值）
- 批量充值限制（50个户）
- 频率控制参数（每10个户睡眠500毫秒）

```php
'time_restrictions' => [
    'forbidden_hours' => [
        'start' => '02:00',
        'end' => '06:30',
        'enabled' => true,
    ],
],
'batch_limits' => [
    'max_accounts_per_batch' => 50,
],
'frequency_control' => [
    'accounts_per_sleep' => 10,
    'sleep_duration' => 500000,
],
```

#### 预算规则配置 (budget_rules.php)
- 预算限制开关
- 用户每日限额配置
- 余额警告阈值（3000元）

```php
'budget_control' => [
    'enabled' => true,
],
'user_daily_limits' => [
    'default_limit' => 10000,
    'vip_limit' => 50000,
    'admin_limit' => 100000,
],
'balance_warnings' => [
    'warning_threshold' => 3000,
],
```

#### 配置管理器 (ConfigManager)
- 统一配置管理
- 配置缓存和热更新
- 配置验证和默认值

```php
// 获取平台配置
$config = ConfigManager::getPlatformConfig();
$adapterClass = ConfigManager::getPlatformAdapterClass('tiktok');

// 获取限制配置
$maxBatch = ConfigManager::getBatchLimit('max_accounts_per_batch');
$isRestricted = ConfigManager::isTimeRestricted('03:00');

// 获取预算配置
$threshold = ConfigManager::getBalanceWarningThreshold();
$enabled = ConfigManager::isBudgetControlEnabled();
```

### 3. 测试用例

#### 队列任务测试
- **位置**: `backendapi/tests/unit/promote/transfermoneyv2/queue/TransferMoneyJobV2Test.php`
- **覆盖**: 队列任务的所有核心功能

#### 配置系统测试
- **位置**: `backendapi/tests/unit/promote/transfermoneyv2/config/ConfigManagerTest.php`
- **覆盖**: 配置管理器的所有功能

#### 集成测试
- **位置**: `backendapi/tests/unit/promote/transfermoneyv2/integration/TransferMoneyIntegrationTest.php`
- **覆盖**: 系统各组件的集成功能

## 使用方法

### 1. 基本充值任务

```php
// 立即充值
$data = [
    'target_advertiser_ids' => ['**********', '**********'],
    'amount' => 100,
    'user_name' => '操作员',
    'user_id' => 1,
    'execute_time' => time(),
    'isTimeRecharge' => false,
];

TransferMoneyJobV2::addJob($data);
```

### 2. 定时充值任务

```php
// 定时充值（1小时后执行）
$data = [
    'target_advertiser_ids' => ['**********'],
    'amount' => 200,
    'user_name' => '操作员',
    'user_id' => 1,
    'execute_time' => time() + 3600,
    'isTimeRecharge' => true,
];

TransferMoneyJobV2::addJob($data);
```

### 3. 加粉充值任务

```php
// 加粉后自动充值
$subAdvertiserId = '**********';
TransferMoneyJobV2::addFansJob($subAdvertiserId);
```

### 4. 批量充值任务

```php
// 批量添加充值任务
$batchData = [
    [
        'target_advertiser_ids' => ['1111111111'],
        'amount' => 100,
        'user_name' => '用户1',
        'execute_time' => time() + 60,
        'isTimeRecharge' => false,
    ],
    [
        'target_advertiser_ids' => ['2222222222'],
        'amount' => 200,
        'user_name' => '用户2',
        'execute_time' => time() + 120,
        'isTimeRecharge' => false,
    ],
];

$results = TransferMoneyJobV2::addBatchJobs($batchData);
```

### 5. 配置管理

```php
// 获取平台限额
$tiktokLimit = ConfigManager::getPlatformLimit('tiktok', 'single_recharge_amount');

// 检查时间限制
if (ConfigManager::isTimeRestricted()) {
    throw new Exception('当前时间段不允许充值');
}

// 获取批量限制
$maxBatch = ConfigManager::getBatchLimit('max_accounts_per_batch');

// 热更新配置
ConfigManager::reload('platform');
```

## 兼容性保证

### 1. 接口兼容性
- 保持与现有 `TransferMoneyJob` 相同的方法签名
- 支持现有的队列任务参数格式
- 保持现有的错误码体系

### 2. 数据兼容性
- 支持现有的数据库结构
- 兼容现有的缓存键格式
- 保持现有的消息通知格式

### 3. 业务兼容性
- 保持现有的业务逻辑流程
- 支持现有的充值规则
- 兼容现有的权限控制

## 配置说明

### 1. 平台限额配置
- **抖音**: 单次1000元，小时3000元
- **ADQ**: 单次2000元，小时20000元

### 2. 时间限制配置
- **禁止时间**: 凌晨2:00-6:30
- **时区**: Asia/Shanghai

### 3. 批量限制配置
- **最大账户数**: 50个/批次
- **频率控制**: 每10个户睡眠500毫秒

### 4. 预算控制配置
- **余额警告**: 3000元
- **用户限额**: 默认10000元/天

## 监控和告警

### 1. 任务统计
```php
// 获取任务统计信息
$stats = TransferMoneyJobV2::getJobStats();
```

### 2. 清理过期记录
```php
// 清理7天前的限制记录
$cleanupCount = TransferMoneyJobV2::cleanupExpiredRestrictions(7);
```

### 3. 配置版本管理
```php
// 获取所有配置版本
$versions = ConfigManager::getAllConfigVersions();
```

## 部署步骤

### 1. 文件部署
1. 复制所有新文件到对应目录
2. 确保配置文件权限正确
3. 验证类自动加载

### 2. 配置验证
1. 检查配置文件语法
2. 验证配置项完整性
3. 测试配置加载

### 3. 测试验证
1. 运行单元测试
2. 执行集成测试
3. 进行性能测试

### 4. 灰度发布
1. 小范围测试新队列任务
2. 监控系统运行状态
3. 逐步扩大使用范围

## 注意事项

### 1. 向后兼容
- 新队列任务与现有系统完全兼容
- 可以与现有 `TransferMoneyJob` 并行使用
- 支持逐步迁移

### 2. 配置管理
- 配置文件支持热更新
- 建议定期备份配置文件
- 配置变更需要测试验证

### 3. 性能优化
- 配置缓存提高性能
- 批量处理减少资源消耗
- 频率控制避免系统过载

### 4. 错误处理
- 完整的异常处理机制
- 详细的错误日志记录
- 自动重试和降级策略

## 总结

第五阶段成功实现了：

1. ✅ **队列任务系统**: 基于现有业务逻辑的新队列任务处理类
2. ✅ **配置文件系统**: 完整的配置管理和热更新机制
3. ✅ **测试用例**: 全面的单元测试和集成测试
4. ✅ **兼容性保证**: 与现有系统完全兼容
5. ✅ **文档完善**: 详细的使用说明和部署指南

新系统在保持完全向后兼容的同时，提供了更好的可配置性、可维护性和可扩展性，为后续的系统优化和功能扩展奠定了坚实基础。