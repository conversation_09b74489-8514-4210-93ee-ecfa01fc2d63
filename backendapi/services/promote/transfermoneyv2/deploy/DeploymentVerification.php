<?php

namespace backendapi\services\promote\transfermoneyv2\deploy;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use backendapi\services\promote\transfermoneyv2\workflow\AutoRechargeWorkflow;
use backendapi\services\promote\transfermoneyv2\workflow\QueueExecutionWorkflow;
use backendapi\services\promote\transfermoneyv2\workflow\AddFansRechargeWorkflow;
use backendapi\services\promote\transfermoneyv2\verification\BusinessLogicVerification;
use Exception;
use Yii;

/**
 * 部署验证脚本
 * 
 * 第四阶段：统一业务服务实现的最终验证和部署脚本
 * 确保所有组件正常工作，业务逻辑一致，系统可以安全部署
 */
class DeploymentVerification
{
    /**
     * @var array 验证结果
     */
    private $verificationResults = [];

    /**
     * @var array 部署配置
     */
    private $deployConfig = [
        'enable_safety_checks' => true,
        'enable_performance_tests' => true,
        'enable_compatibility_tests' => true,
        'enable_integration_tests' => true,
        'max_execution_time' => 300, // 5分钟
        'memory_limit' => '512M'
    ];

    /**
     * 执行完整的部署前验证
     * 
     * @return array 验证结果
     */
    public function executeDeploymentVerification(): array
    {
        $startTime = microtime(true);
        
        $this->verificationResults = [
            'deployment_verification' => [
                'start_time' => time(),
                'status' => 'running',
                'checks' => [],
                'summary' => [
                    'total_checks' => 0,
                    'passed_checks' => 0,
                    'failed_checks' => 0,
                    'warnings' => 0
                ]
            ]
        ];

        try {
            echo "🚀 开始执行第四阶段部署验证...\n\n";

            // 1. 环境检查
            $this->performEnvironmentChecks();

            // 2. 组件完整性检查
            $this->performComponentIntegrityChecks();

            // 3. 业务逻辑一致性验证
            $this->performBusinessLogicVerification();

            // 4. 集成测试
            $this->performIntegrationTests();

            // 5. 性能测试
            $this->performPerformanceTests();

            // 6. 安全性检查
            $this->performSecurityChecks();

            // 7. 兼容性测试
            $this->performCompatibilityTests();

            // 8. 最终验证
            $this->performFinalVerification();

            $this->verificationResults['deployment_verification']['status'] = 'completed';
            $this->verificationResults['deployment_verification']['end_time'] = time();
            $this->verificationResults['deployment_verification']['duration'] = microtime(true) - $startTime;

            echo "\n✅ 部署验证完成！\n";
            $this->printVerificationSummary();

        } catch (Exception $e) {
            $this->verificationResults['deployment_verification']['status'] = 'failed';
            $this->verificationResults['deployment_verification']['error'] = $e->getMessage();
            
            echo "\n❌ 部署验证失败: " . $e->getMessage() . "\n";
        }

        return $this->verificationResults;
    }

    /**
     * 执行环境检查
     */
    private function performEnvironmentChecks(): void
    {
        echo "📋 执行环境检查...\n";

        // PHP版本检查
        $phpVersion = PHP_VERSION;
        $minPhpVersion = '7.4.0';
        $phpVersionOk = version_compare($phpVersion, $minPhpVersion, '>=');
        
        $this->addCheck('php_version', $phpVersionOk, 
            $phpVersionOk ? "PHP版本检查通过: {$phpVersion}" : "PHP版本过低: {$phpVersion} < {$minPhpVersion}");

        // 内存限制检查
        $memoryLimit = ini_get('memory_limit');
        $memoryOk = $this->parseMemoryLimit($memoryLimit) >= $this->parseMemoryLimit($this->deployConfig['memory_limit']);
        
        $this->addCheck('memory_limit', $memoryOk, 
            $memoryOk ? "内存限制检查通过: {$memoryLimit}" : "内存限制不足: {$memoryLimit}");

        // 扩展检查
        $requiredExtensions = ['redis', 'pdo', 'json', 'mbstring'];
        foreach ($requiredExtensions as $extension) {
            $extensionLoaded = extension_loaded($extension);
            $this->addCheck("extension_{$extension}", $extensionLoaded, 
                $extensionLoaded ? "扩展 {$extension} 已加载" : "扩展 {$extension} 未加载");
        }

        echo "✓ 环境检查完成\n\n";
    }

    /**
     * 执行组件完整性检查
     */
    private function performComponentIntegrityChecks(): void
    {
        echo "🔧 执行组件完整性检查...\n";

        // 核心服务检查
        try {
            $service = new TransferMoneyServiceV2();
            $this->addCheck('core_service', true, 'TransferMoneyServiceV2 创建成功');
        } catch (Exception $e) {
            $this->addCheck('core_service', false, 'TransferMoneyServiceV2 创建失败: ' . $e->getMessage());
        }

        // 平台工厂检查
        try {
            $factory = new PlatformFactory();
            $supportedPlatforms = $factory->getSupportedPlatforms();
            $platformsOk = count($supportedPlatforms) >= 3; // mock, tiktok, adq
            $this->addCheck('platform_factory', $platformsOk, 
                $platformsOk ? '平台工厂检查通过，支持平台: ' . implode(', ', $supportedPlatforms) : '平台工厂支持平台不足');
        } catch (Exception $e) {
            $this->addCheck('platform_factory', false, '平台工厂检查失败: ' . $e->getMessage());
        }

        // 缓存管理器检查
        try {
            $cacheManager = new TransferCacheManager();
            $this->addCheck('cache_manager', true, 'TransferCacheManager 创建成功');
        } catch (Exception $e) {
            $this->addCheck('cache_manager', false, 'TransferCacheManager 创建失败: ' . $e->getMessage());
        }

        // 工作流检查
        $workflows = [
            'AutoRechargeWorkflow' => AutoRechargeWorkflow::class,
            'QueueExecutionWorkflow' => QueueExecutionWorkflow::class,
            'AddFansRechargeWorkflow' => AddFansRechargeWorkflow::class
        ];

        foreach ($workflows as $name => $class) {
            try {
                $workflow = $class::create();
                $this->addCheck("workflow_{$name}", true, "{$name} 创建成功");
            } catch (Exception $e) {
                $this->addCheck("workflow_{$name}", false, "{$name} 创建失败: " . $e->getMessage());
            }
        }

        echo "✓ 组件完整性检查完成\n\n";
    }

    /**
     * 执行业务逻辑一致性验证
     */
    private function performBusinessLogicVerification(): void
    {
        echo "🔍 执行业务逻辑一致性验证...\n";

        try {
            $verification = new BusinessLogicVerification();
            $results = $verification->executeFullVerification();
            
            $successRate = $results['summary']['success_rate'];
            $passed = $successRate >= 95; // 要求95%以上的一致性
            
            $this->addCheck('business_logic_consistency', $passed, 
                "业务逻辑一致性: {$successRate}% ({$results['summary']['passed_checks']}/{$results['summary']['total_checks']})");
                
            // 存储详细结果
            $this->verificationResults['business_logic_details'] = $results;
            
        } catch (Exception $e) {
            $this->addCheck('business_logic_consistency', false, '业务逻辑验证失败: ' . $e->getMessage());
        }

        echo "✓ 业务逻辑一致性验证完成\n\n";
    }

    /**
     * 执行集成测试
     */
    private function performIntegrationTests(): void
    {
        echo "🔗 执行集成测试...\n";

        // 测试完整充值流程
        try {
            $service = new TransferMoneyServiceV2();
            $workflow = AutoRechargeWorkflow::createWithService($service);
            
            $testParams = [
                'user_id' => 999,
                'user_name' => '部署测试用户',
                'data' => "账户ID：test_account_123\n转账金额：1"
            ];
            
            $result = $workflow->execute($testParams);
            $integrationOk = isset($result['success']);
            
            $this->addCheck('integration_recharge_flow', $integrationOk, 
                $integrationOk ? '充值流程集成测试通过' : '充值流程集成测试失败');
                
        } catch (Exception $e) {
            $this->addCheck('integration_recharge_flow', false, '充值流程集成测试异常: ' . $e->getMessage());
        }

        // 测试平台适配器集成
        try {
            $factory = new PlatformFactory();
            $mockAdapter = $factory->create('mock');
            
            $transferResult = $mockAdapter->transferMoney('test_source', 'test_target', 1, 'test_token', 'test_org');
            $adapterOk = isset($transferResult['success']) && $transferResult['success'];
            
            $this->addCheck('integration_platform_adapter', $adapterOk, 
                $adapterOk ? '平台适配器集成测试通过' : '平台适配器集成测试失败');
                
        } catch (Exception $e) {
            $this->addCheck('integration_platform_adapter', false, '平台适配器集成测试异常: ' . $e->getMessage());
        }

        echo "✓ 集成测试完成\n\n";
    }

    /**
     * 执行性能测试
     */
    private function performPerformanceTests(): void
    {
        echo "⚡ 执行性能测试...\n";

        // 测试服务创建性能
        $startTime = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            $service = new TransferMoneyServiceV2();
        }
        $creationTime = microtime(true) - $startTime;
        $creationOk = $creationTime < 1.0; // 100次创建应在1秒内完成
        
        $this->addCheck('performance_service_creation', $creationOk, 
            $creationOk ? "服务创建性能测试通过: {$creationTime}s" : "服务创建性能测试失败: {$creationTime}s");

        // 测试验证器性能
        $startTime = microtime(true);
        $service = new TransferMoneyServiceV2();
        for ($i = 0; $i < 50; $i++) {
            try {
                $service->dealParams([
                    'user_id' => $i,
                    'user_name' => "测试用户{$i}",
                    'data' => "账户ID：test_account_{$i}\n转账金额：100"
                ]);
            } catch (Exception $e) {
                // 忽略验证异常，只测试性能
            }
        }
        $validationTime = microtime(true) - $startTime;
        $validationOk = $validationTime < 2.0; // 50次验证应在2秒内完成
        
        $this->addCheck('performance_validation', $validationOk, 
            $validationOk ? "验证器性能测试通过: {$validationTime}s" : "验证器性能测试失败: {$validationTime}s");

        echo "✓ 性能测试完成\n\n";
    }

    /**
     * 执行安全性检查
     */
    private function performSecurityChecks(): void
    {
        echo "🔒 执行安全性检查...\n";

        // 检查Mock适配器安全性
        try {
            $factory = new PlatformFactory();
            $mockAdapter = $factory->create('mock');
            
            // Mock适配器应该是安全的，不会执行真实操作
            $result = $mockAdapter->transferMoney('any', 'any', 999999, 'any', 'any');
            $safetyOk = isset($result['success']) && $result['success'] && isset($result['mock']) && $result['mock'];
            
            $this->addCheck('security_mock_adapter', $safetyOk, 
                $safetyOk ? 'Mock适配器安全性检查通过' : 'Mock适配器安全性检查失败');
                
        } catch (Exception $e) {
            $this->addCheck('security_mock_adapter', false, 'Mock适配器安全性检查异常: ' . $e->getMessage());
        }

        // 检查输入验证安全性
        $maliciousInputs = [
            "账户ID：'; DROP TABLE users; --\n转账金额：100",
            "账户ID：<script>alert('xss')</script>\n转账金额：100",
            "账户ID：test\n转账金额：-999999"
        ];

        $securityOk = true;
        foreach ($maliciousInputs as $input) {
            try {
                $service = new TransferMoneyServiceV2();
                $service->dealParams([
                    'user_id' => 1,
                    'user_name' => '测试',
                    'data' => $input
                ]);
                // 如果没有抛出异常，说明验证不够严格
                $securityOk = false;
                break;
            } catch (Exception $e) {
                // 抛出异常是正确的，说明输入验证有效
            }
        }
        
        $this->addCheck('security_input_validation', $securityOk, 
            $securityOk ? '输入验证安全性检查通过' : '输入验证安全性检查失败');

        echo "✓ 安全性检查完成\n\n";
    }

    /**
     * 执行兼容性测试
     */
    private function performCompatibilityTests(): void
    {
        echo "🔄 执行兼容性测试...\n";

        // 测试错误码兼容性
        $service = new TransferMoneyServiceV2();
        $errorCodes = [
            'success_code' => 200,
            'time_code' => 100,
            'success_insufficient_balance_code' => 201,
            'error_code_it' => 422,
            'error_code_promote' => 423,
            'error_code_insufficient_balance' => 424
        ];

        $compatibilityOk = true;
        foreach ($errorCodes as $codeName => $expectedCode) {
            $actualCode = $service->{'get' . str_replace('_', '', ucwords($codeName, '_'))}();
            if ($actualCode !== $expectedCode) {
                $compatibilityOk = false;
                break;
            }
        }
        
        $this->addCheck('compatibility_error_codes', $compatibilityOk, 
            $compatibilityOk ? '错误码兼容性检查通过' : '错误码兼容性检查失败');

        // 测试方法兼容性
        $requiredMethods = ['run', 'execute', 'transferMoney', 'getBalance', 'getAccountBalance'];
        $methodsOk = true;
        foreach ($requiredMethods as $method) {
            if (!method_exists($service, $method)) {
                $methodsOk = false;
                break;
            }
        }
        
        $this->addCheck('compatibility_methods', $methodsOk, 
            $methodsOk ? '方法兼容性检查通过' : '方法兼容性检查失败');

        echo "✓ 兼容性测试完成\n\n";
    }

    /**
     * 执行最终验证
     */
    private function performFinalVerification(): void
    {
        echo "🎯 执行最终验证...\n";

        $summary = $this->verificationResults['deployment_verification']['summary'];
        $totalChecks = $summary['total_checks'];
        $passedChecks = $summary['passed_checks'];
        $successRate = $totalChecks > 0 ? ($passedChecks / $totalChecks) * 100 : 0;

        // 最终验证标准
        $finalOk = $successRate >= 90 && $summary['failed_checks'] === 0;
        
        $this->addCheck('final_verification', $finalOk, 
            $finalOk ? "最终验证通过，成功率: {$successRate}%" : "最终验证失败，成功率: {$successRate}%");

        if ($finalOk) {
            echo "🎉 系统已准备好部署！\n";
        } else {
            echo "⚠️  系统尚未准备好部署，请检查失败项目\n";
        }

        echo "✓ 最终验证完成\n\n";
    }

    /**
     * 添加检查结果
     */
    private function addCheck(string $checkName, bool $passed, string $message): void
    {
        $this->verificationResults['deployment_verification']['checks'][] = [
            'name' => $checkName,
            'passed' => $passed,
            'message' => $message,
            'timestamp' => time()
        ];

        $summary = &$this->verificationResults['deployment_verification']['summary'];
        $summary['total_checks']++;
        
        if ($passed) {
            $summary['passed_checks']++;
            echo "  ✓ {$message}\n";
        } else {
            $summary['failed_checks']++;
            echo "  ✗ {$message}\n";
        }
    }

    /**
     * 打印验证摘要
     */
    private function printVerificationSummary(): void
    {
        $summary = $this->verificationResults['deployment_verification']['summary'];
        $successRate = $summary['total_checks'] > 0 ? ($summary['passed_checks'] / $summary['total_checks']) * 100 : 0;

        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 部署验证摘要\n";
        echo str_repeat("=", 60) . "\n";
        echo "总检查项: {$summary['total_checks']}\n";
        echo "通过检查: {$summary['passed_checks']}\n";
        echo "失败检查: {$summary['failed_checks']}\n";
        echo "成功率: " . number_format($successRate, 2) . "%\n";
        echo "执行时间: {$this->verificationResults['deployment_verification']['duration']}秒\n";
        echo str_repeat("=", 60) . "\n";

        if ($summary['failed_checks'] > 0) {
            echo "\n❌ 失败的检查项:\n";
            foreach ($this->verificationResults['deployment_verification']['checks'] as $check) {
                if (!$check['passed']) {
                    echo "  - {$check['name']}: {$check['message']}\n";
                }
            }
        }
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        $memoryLimit = trim($memoryLimit);
        $last = strtolower($memoryLimit[strlen($memoryLimit) - 1]);
        $value = (int)$memoryLimit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * 生成部署报告
     */
    public function generateDeploymentReport(): string
    {
        $results = $this->executeDeploymentVerification();
        
        $report = "# 第四阶段部署验证报告\n\n";
        $report .= "## 验证概要\n";
        $report .= "- 验证状态: {$results['deployment_verification']['status']}\n";
        $report .= "- 总检查项: {$results['deployment_verification']['summary']['total_checks']}\n";
        $report .= "- 通过检查: {$results['deployment_verification']['summary']['passed_checks']}\n";
        $report .= "- 失败检查: {$results['deployment_verification']['summary']['failed_checks']}\n";
        $report .= "- 验证耗时: {$results['deployment_verification']['duration']}秒\n\n";

        $report .= "## 检查详情\n\n";
        foreach ($results['deployment_verification']['checks'] as $check) {
            $status = $check['passed'] ? '✅ 通过' : '❌ 失败';
            $report .= "### {$check['name']}\n";
            $report .= "- 状态: {$status}\n";
            $report .= "- 消息: {$check['message']}\n\n";
        }

        if (isset($results['business_logic_details'])) {
            $report .= "## 业务逻辑一致性详情\n\n";
            $report .= "- 成功率: {$results['business_logic_details']['summary']['success_rate']}%\n";
            $report .= "- 通过验证: {$results['business_logic_details']['summary']['passed_checks']}\n";
            $report .= "- 失败验证: {$results['business_logic_details']['summary']['failed_checks']}\n\n";
        }

        return $report;
    }
}