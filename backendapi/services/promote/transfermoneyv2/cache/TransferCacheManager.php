<?php

namespace backendapi\services\promote\transfermoneyv2\cache;

use common\models\promote\AdsTransferMoneyRecord;
use Yii;
use Exception;

/**
 * 充值缓存管理器
 * 
 * 基于现有TransferMoneyBatchService::amountlimit()和success()方法逻辑
 * 统一管理所有充值相关的Redis缓存操作，保持现有缓存键命名规则和过期时间
 */
class TransferCacheManager
{
    /**
     * 缓存键前缀
     */
    const TRANSFER_MONEY_DATA_KEY = 'transferMoneyData:';
    const TRANSFER_MONEY_BALANCE_KEY = 'transferMoneyBalance:';
    const ADD_FANS_TRANSFER_MONEY_COUNT_KEY = 'AddFansTransferMoneyCount:';
    const ADD_FANS_TRANSFER_MONEY_DATE_KEY = 'AddFansTransferMoneyCount:';

    /**
     * 缓存过期时间（秒）
     */
    const TRANSFER_DATA_EXPIRE = 3600; // 1小时
    const BALANCE_EXPIRE = 500; // 500秒
    const ADD_FANS_COUNT_EXPIRE = 300; // 5分钟
    const ADD_FANS_DATE_EXPIRE = 86400; // 24小时

    /**
     * @var \yii\caching\CacheInterface 缓存组件
     */
    private $cache;

    /**
     * @var bool 是否启用数据库查询（替代Redis缓存）
     */
    private $useDatabase;

    /**
     * 构造函数
     * 
     * @param \yii\caching\CacheInterface|null $cache 缓存组件
     * @param bool $useDatabase 是否使用数据库查询替代缓存
     */
    public function __construct($cache = null, $useDatabase = false)
    {
        $this->cache = $cache ?: Yii::$app->cache;
        $this->useDatabase = $useDatabase;
    }

    /**
     * 检查账户小时充值限额
     * 
     * 基于现有amountlimit()方法逻辑，支持数据库查询和Redis缓存两种方式
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param float $amount 充值金额
     * @param int $hourlyLimit 小时限额
     * @return bool 是否通过限额检查
     * @throws Exception 超过限额时抛出异常
     */
    public function checkHourlyLimit(string $targetAdvertiserId, float $amount, int $hourlyLimit): bool
    {
        if ($this->useDatabase) {
            // 使用数据库查询
            return AdsTransferMoneyRecord::checkHourlyLimit($targetAdvertiserId, $amount, $hourlyLimit);
        }

        // 使用Redis缓存（原有逻辑）
        $key = self::TRANSFER_MONEY_DATA_KEY . $targetAdvertiserId;
        $cacheData = $this->cache->get($key);
        
        if (empty($cacheData)) {
            return true;
        }

        $oneHourTime = time() - 3600; // 一小时前的时间戳
        $totalAmount = 0;
        $validData = [];

        foreach ($cacheData as $item) {
            if ($item['time'] < $oneHourTime) {
                continue; // 跳过一小时前的记录
            }
            $totalAmount += $item['amount'];
            $validData[] = $item;
        }

        // 更新缓存，移除过期数据
        if (count($validData) !== count($cacheData)) {
            $this->cache->set($key, $validData, self::TRANSFER_DATA_EXPIRE);
        }

        if (($totalAmount + $amount) > $hourlyLimit) {
            throw new Exception(sprintf(
                '1小时内限制充值金额不能超过%d，账户%s已经充值了%s',
                $hourlyLimit,
                $targetAdvertiserId,
                $totalAmount
            ));
        }

        return true;
    }

    /**
     * 记录成功的充值操作
     * 
     * 基于现有success()方法逻辑
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param float $amount 充值金额
     * @param string $userName 用户名
     * @return bool 是否记录成功
     */
    public function recordSuccessfulTransfer(string $targetAdvertiserId, float $amount, string $userName): bool
    {
        $key = self::TRANSFER_MONEY_DATA_KEY . $targetAdvertiserId;
        $cacheData = $this->cache->get($key) ?: [];

        // 添加新的充值记录
        $cacheData[] = [
            'user_name' => $userName,
            'amount' => $amount,
            'time' => time()
        ];

        return $this->cache->set($key, $cacheData, self::TRANSFER_DATA_EXPIRE);
    }

    /**
     * 获取账户余额缓存
     * 
     * @param string $advertiserId 账户ID
     * @return float|null 账户余额，null表示缓存中不存在
     */
    public function getBalance(string $advertiserId): ?float
    {
        $key = self::TRANSFER_MONEY_BALANCE_KEY . $advertiserId;
        $balance = $this->cache->get($key);
        
        return $balance !== false ? (float)$balance : null;
    }

    /**
     * 设置账户余额缓存
     * 
     * @param string $advertiserId 账户ID
     * @param float $balance 账户余额
     * @return bool 是否设置成功
     */
    public function setBalance(string $advertiserId, float $balance): bool
    {
        $key = self::TRANSFER_MONEY_BALANCE_KEY . $advertiserId;
        return $this->cache->set($key, $balance, self::BALANCE_EXPIRE);
    }

    /**
     * 删除账户余额缓存
     * 
     * @param string $advertiserId 账户ID
     * @return bool 是否删除成功
     */
    public function deleteBalance(string $advertiserId): bool
    {
        $key = self::TRANSFER_MONEY_BALANCE_KEY . $advertiserId;
        return $this->cache->delete($key);
    }

    /**
     * 获取加粉充值计数
     * 
     * @param string $subAdvertiserId 子账户ID
     * @return int 充值次数
     */
    public function getAddFansTransferCount(string $subAdvertiserId): int
    {
        $key = self::ADD_FANS_TRANSFER_MONEY_COUNT_KEY . $subAdvertiserId;
        $count = $this->cache->get($key);
        
        return $count !== false ? (int)$count : 0;
    }

    /**
     * 增加加粉充值计数
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param int $increment 增加数量（默认1）
     * @return int 新的计数值
     */
    public function incrementAddFansTransferCount(string $subAdvertiserId, int $increment = 1): int
    {
        $key = self::ADD_FANS_TRANSFER_MONEY_COUNT_KEY . $subAdvertiserId;
        $currentCount = $this->getAddFansTransferCount($subAdvertiserId);
        $newCount = $currentCount + $increment;
        
        $this->cache->set($key, $newCount, self::ADD_FANS_COUNT_EXPIRE);
        return $newCount;
    }

    /**
     * 获取当天已限制的加粉充值账户列表
     * 
     * @param string $date 日期（格式：Y-m-d）
     * @return array 已限制的账户ID列表
     */
    public function getAddFansRestrictedAccounts(string $date): array
    {
        $key = self::ADD_FANS_TRANSFER_MONEY_DATE_KEY . $date;
        $accounts = $this->cache->get($key);
        
        return $accounts !== false ? $accounts : [];
    }

    /**
     * 添加账户到当天加粉充值限制列表
     * 
     * @param string $date 日期（格式：Y-m-d）
     * @param string $subAdvertiserId 子账户ID
     * @return bool 是否添加成功
     */
    public function addAddFansRestrictedAccount(string $date, string $subAdvertiserId): bool
    {
        $key = self::ADD_FANS_TRANSFER_MONEY_DATE_KEY . $date;
        $accounts = $this->getAddFansRestrictedAccounts($date);
        
        if (!in_array($subAdvertiserId, $accounts)) {
            $accounts[] = $subAdvertiserId;
            return $this->cache->set($key, $accounts, self::ADD_FANS_DATE_EXPIRE);
        }
        
        return true;
    }

    /**
     * 检查账户是否在当天加粉充值限制列表中
     * 
     * @param string $date 日期（格式：Y-m-d）
     * @param string $subAdvertiserId 子账户ID
     * @return bool 是否被限制
     */
    public function isAddFansRestricted(string $date, string $subAdvertiserId): bool
    {
        $accounts = $this->getAddFansRestrictedAccounts($date);
        return in_array($subAdvertiserId, $accounts);
    }

    /**
     * 获取账户的充值历史记录
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param int $hours 查询小时数（默认1小时）
     * @return array 充值历史记录
     */
    public function getTransferHistory(string $targetAdvertiserId, int $hours = 1): array
    {
        if ($this->useDatabase) {
            // 使用数据库查询
            $records = AdsTransferMoneyRecord::getTransferHistory($targetAdvertiserId, $hours);
            
            // 转换为与Redis缓存相同的格式
            $history = [];
            foreach ($records as $record) {
                $history[] = [
                    'user_name' => $record['user_name'],
                    'amount' => $record['amount'],
                    'time' => $record['created_at'],
                    'formatted_time' => date('Y-m-d H:i:s', $record['created_at']),
                    'platform' => $record['platform'],
                    'serial_number' => $record['serial_number']
                ];
            }
            
            return $history;
        }

        // 使用Redis缓存（原有逻辑）
        $key = self::TRANSFER_MONEY_DATA_KEY . $targetAdvertiserId;
        $cacheData = $this->cache->get($key);
        
        if (empty($cacheData)) {
            return [];
        }

        $timeLimit = time() - ($hours * 3600);
        $history = [];

        foreach ($cacheData as $item) {
            if ($item['time'] >= $timeLimit) {
                $history[] = [
                    'user_name' => $item['user_name'],
                    'amount' => $item['amount'],
                    'time' => $item['time'],
                    'formatted_time' => date('Y-m-d H:i:s', $item['time'])
                ];
            }
        }

        return $history;
    }

    /**
     * 获取账户在指定时间内的充值总额
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param int $hours 查询小时数（默认1小时）
     * @return float 充值总额
     */
    public function getTotalTransferAmount(string $targetAdvertiserId, int $hours = 1): float
    {
        if ($this->useDatabase) {
            // 使用数据库查询
            return AdsTransferMoneyRecord::getHourlyTransferAmount($targetAdvertiserId, $hours);
        }

        // 使用Redis缓存（原有逻辑）
        $history = $this->getTransferHistory($targetAdvertiserId, $hours);
        $total = 0;

        foreach ($history as $record) {
            $total += $record['amount'];
        }

        return $total;
    }

    /**
     * 清理过期的充值记录
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @return bool 是否清理成功
     */
    public function cleanExpiredTransferRecords(string $targetAdvertiserId): bool
    {
        $key = self::TRANSFER_MONEY_DATA_KEY . $targetAdvertiserId;
        $cacheData = $this->cache->get($key);
        
        if (empty($cacheData)) {
            return true;
        }

        $oneHourTime = time() - 3600;
        $validData = [];

        foreach ($cacheData as $item) {
            if ($item['time'] >= $oneHourTime) {
                $validData[] = $item;
            }
        }

        if (empty($validData)) {
            return $this->cache->delete($key);
        } else {
            return $this->cache->set($key, $validData, self::TRANSFER_DATA_EXPIRE);
        }
    }

    /**
     * 批量清理多个账户的过期记录
     * 
     * @param array $targetAdvertiserIds 目标账户ID数组
     * @return array 清理结果，键为账户ID，值为是否成功
     */
    public function batchCleanExpiredRecords(array $targetAdvertiserIds): array
    {
        $results = [];
        
        foreach ($targetAdvertiserIds as $advertiserId) {
            $results[$advertiserId] = $this->cleanExpiredTransferRecords($advertiserId);
        }
        
        return $results;
    }

    /**
     * 获取缓存统计信息
     * 
     * @return array 缓存统计信息
     */
    public function getCacheStats(): array
    {
        return [
            'cache_component' => get_class($this->cache),
            'use_database' => $this->useDatabase,
            'key_prefixes' => [
                'transfer_data' => self::TRANSFER_MONEY_DATA_KEY,
                'balance' => self::TRANSFER_MONEY_BALANCE_KEY,
                'add_fans_count' => self::ADD_FANS_TRANSFER_MONEY_COUNT_KEY,
                'add_fans_date' => self::ADD_FANS_TRANSFER_MONEY_DATE_KEY,
            ],
            'expire_times' => [
                'transfer_data' => self::TRANSFER_DATA_EXPIRE,
                'balance' => self::BALANCE_EXPIRE,
                'add_fans_count' => self::ADD_FANS_COUNT_EXPIRE,
                'add_fans_date' => self::ADD_FANS_DATE_EXPIRE,
            ]
        ];
    }

    /**
     * 设置是否使用数据库查询
     * 
     * @param bool $useDatabase 是否使用数据库
     */
    public function setUseDatabase(bool $useDatabase): void
    {
        $this->useDatabase = $useDatabase;
    }

    /**
     * 检查是否使用数据库查询
     * 
     * @return bool 是否使用数据库
     */
    public function isUsingDatabase(): bool
    {
        return $this->useDatabase;
    }

    /**
     * 创建使用数据库查询的缓存管理器实例
     * 
     * @param \yii\caching\CacheInterface|null $cache 缓存组件
     * @return TransferCacheManager
     */
    public static function createWithDatabase($cache = null): TransferCacheManager
    {
        return new self($cache, true);
    }
}