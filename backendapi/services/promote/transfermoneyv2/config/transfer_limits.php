<?php

/**
 * 充值限制配置文件
 * 
 * 定义时间限制、批量限制、频率控制等充值限制规则
 */

return [
    // 时间限制规则
    'time_restrictions' => [
        'forbidden_hours' => [
            'start' => '02:00',                    // 禁止充值开始时间
            'end' => '06:30',                      // 禁止充值结束时间
            'timezone' => 'Asia/Shanghai',         // 时区
            'enabled' => true,                     // 是否启用时间限制
        ],
        'working_hours' => [
            'start' => '08:00',                    // 工作时间开始
            'end' => '22:00',                      // 工作时间结束
            'weekend_enabled' => true,             // 周末是否允许充值
            'holiday_enabled' => false,            // 节假日是否允许充值
        ],
    ],

    // 批量充值限制
    'batch_limits' => [
        'max_accounts_per_batch' => 50,           // 单次批量充值最大账户数
        'max_daily_batches' => 100,               // 每日最大批次数
        'max_concurrent_batches' => 5,            // 最大并发批次数
        'batch_interval' => 60,                   // 批次间隔时间（秒）
    ],

    // 频率控制参数
    'frequency_control' => [
        'accounts_per_sleep' => 10,               // 每充值多少个账户后睡眠
        'sleep_duration' => 500000,               // 睡眠时长（微秒，500毫秒）
        'max_requests_per_minute' => 60,          // 每分钟最大请求数
        'max_requests_per_hour' => 1000,          // 每小时最大请求数
        'rate_limit_window' => 60,                // 限流窗口时间（秒）
    ],

    // 加粉充值特殊限制
    'fans_recharge_limits' => [
        'max_attempts_per_minute' => 5,           // 每分钟最大尝试次数
        'daily_restriction_enabled' => true,      // 是否启用每日限制
        'auto_amount' => 50,                      // 自动充值金额
        'check_interval' => 300,                  // 检查间隔（秒）
        'restriction_duration' => 86400,          // 限制持续时间（秒，24小时）
    ],

    // 定时充值限制
    'scheduled_recharge_limits' => [
        'max_advance_days' => 2,                  // 最大提前天数
        'min_advance_minutes' => 5,               // 最小提前分钟数
        'max_scheduled_per_day' => 200,           // 每日最大定时任务数
        'cleanup_expired_after' => 7,             // 清理过期任务天数
    ],

    // 重试机制配置
    'retry_config' => [
        'max_retry_times' => 3,                   // 最大重试次数
        'retry_delay_base' => 1000,               // 基础重试延迟（毫秒）
        'retry_delay_multiplier' => 2,            // 重试延迟倍数
        'max_retry_delay' => 30000,               // 最大重试延迟（毫秒）
        'exponential_backoff' => true,            // 是否使用指数退避
    ],

    // 缓存配置
    'cache_config' => [
        'transfer_data_ttl' => 3600,              // 充值数据缓存时间（秒）
        'balance_cache_ttl' => 500,               // 余额缓存时间（秒）
        'limit_check_ttl' => 300,                 // 限制检查缓存时间（秒）
        'redis_key_prefix' => 'transferMoneyV2:', // Redis键前缀
    ],

    // 监控和告警配置
    'monitoring' => [
        'enable_alerts' => true,                  // 是否启用告警
        'alert_threshold' => [
            'error_rate' => 0.1,                  // 错误率阈值（10%）
            'response_time' => 5000,              // 响应时间阈值（毫秒）
            'queue_length' => 1000,               // 队列长度阈值
        ],
        'notification_channels' => [
            'feishu' => true,                     // 飞书通知
            'email' => false,                     // 邮件通知
            'sms' => false,                       // 短信通知
        ],
    ],

    // 默认配置
    'default' => [
        'max_accounts_per_batch' => 50,
        'sleep_duration' => 500000,
        'max_retry_times' => 3,
        'cache_ttl' => 3600,
    ],

    // 配置版本和更新时间
    'version' => '1.0.0',
    'updated_at' => '2025-01-23 16:50:00',
];