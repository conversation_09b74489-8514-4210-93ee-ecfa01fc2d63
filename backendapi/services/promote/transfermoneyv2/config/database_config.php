<?php

/**
 * 数据库查询配置
 * 
 * 用于配置是否使用数据库查询替代Redis缓存
 */

return [
    // 是否启用数据库查询模式
    'enable_database_query' => [
        'hourly_limit_check' => true,    // 小时限额检查
        'transfer_history' => true,      // 充值历史查询
        'transfer_stats' => true,        // 充值统计分析
        'add_fans_check' => false,       // 加粉检查（仍使用Redis）
    ],

    // 数据库查询性能配置
    'query_performance' => [
        'use_index_hint' => true,        // 使用索引提示
        'cache_query_results' => true,   // 缓存查询结果
        'cache_duration' => 300,         // 查询结果缓存时间（秒）
    ],

    // 混合模式配置（Redis + Database）
    'hybrid_mode' => [
        'enable' => true,                // 是否启用混合模式
        'fallback_to_redis' => true,     // 数据库查询失败时回退到Redis
        'sync_to_redis' => false,        // 是否同步数据到Redis
    ],

    // 数据库索引建议
    'recommended_indexes' => [
        'idx_target_advertiser_created' => [
            'table' => 'erp_ads_transfer_money_record',
            'columns' => ['target_advertiser_id', 'created_at'],
            'type' => 'btree'
        ],
        'idx_status_created' => [
            'table' => 'erp_ads_transfer_money_record',
            'columns' => ['status', 'created_at'],
            'type' => 'btree'
        ],
        'idx_platform_created' => [
            'table' => 'erp_ads_transfer_money_record', 
            'columns' => ['platform', 'created_at'],
            'type' => 'btree'
        ]
    ]
];
