<?php

/**
 * 平台配置文件
 * 
 * 定义各平台的基本信息、适配器类映射、充值限额等配置
 */

return [
    // 平台基本配置
    'platforms' => [
        'tiktok' => [
            'name' => '抖音',
            'adapter_class' => 'backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter',
            'enum_value' => 'tiktok', // 对应 reportEnum::TIKTOL
            'enabled' => true,
        ],
        'adq' => [
            'name' => 'ADQ',
            'adapter_class' => 'backendapi\services\promote\transfermoneyv2\platform\AdqAdapter',
            'enum_value' => 'adq', // 对应 reportEnum::ADQ
            'enabled' => true,
        ],
    ],

    // 平台充值限额配置
    'platform_limits' => [
        'tiktok' => [
            'single_recharge_amount' => 1000,      // 单次充值限额（元）
            'hourly_max_amount' => 3000,           // 小时充值限额（元）
            'daily_max_amount' => 50000,           // 日充值限额（元）
            'monthly_max_amount' => 1000000,       // 月充值限额（元）
        ],
        'adq' => [
            'single_recharge_amount' => 2000,      // 单次充值限额（元）
            'hourly_max_amount' => 20000,          // 小时充值限额（元）
            'daily_max_amount' => 200000,          // 日充值限额（元）
            'monthly_max_amount' => 5000000,       // 月充值限额（元）
        ],
    ],

    // 平台特定参数配置
    'platform_params' => [
        'tiktok' => [
            'require_organization_id' => true,     // 是否需要组织ID
            'api_timeout' => 30,                   // API超时时间（秒）
            'retry_times' => 3,                    // 重试次数
            'retry_delay' => 1000,                 // 重试延迟（毫秒）
        ],
        'adq' => [
            'require_organization_id' => false,    // 是否需要组织ID
            'api_timeout' => 60,                   // API超时时间（秒）
            'retry_times' => 2,                    // 重试次数
            'retry_delay' => 2000,                 // 重试延迟（毫秒）
        ],
    ],

    // 默认配置
    'default' => [
        'platform' => 'tiktok',                   // 默认平台
        'single_recharge_amount' => 1000,         // 默认单次充值限额
        'hourly_max_amount' => 3000,              // 默认小时限额
        'api_timeout' => 30,                      // 默认API超时时间
        'retry_times' => 3,                       // 默认重试次数
    ],

    // 配置版本和更新时间
    'version' => '1.0.0',
    'updated_at' => '2025-01-23 16:49:00',
];