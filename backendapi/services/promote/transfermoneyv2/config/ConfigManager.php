<?php

namespace backendapi\services\promote\transfermoneyv2\config;

use Exception;
use Yii;

/**
 * 配置管理器
 * 
 * 统一管理平台配置、充值限制配置、预算规则配置
 * 支持配置缓存、热更新、验证等功能
 */
class ConfigManager
{
    /**
     * @var array 配置缓存
     */
    private static $configCache = [];

    /**
     * @var string 配置文件目录
     */
    private static $configDir = __DIR__;

    /**
     * @var int 配置缓存时间（秒）
     */
    private static $cacheTime = 3600;

    /**
     * 配置文件映射
     */
    const CONFIG_FILES = [
        'platform' => 'platform_config.php',
        'limits' => 'transfer_limits.php',
        'budget' => 'budget_rules.php',
    ];

    /**
     * 获取平台配置
     * 
     * @param string|null $key 配置键，为空则返回全部配置
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getPlatformConfig($key = null, $default = null)
    {
        $config = self::getConfig('platform');
        
        if ($key === null) {
            return $config;
        }
        
        return self::getNestedValue($config, $key, $default);
    }

    /**
     * 获取充值限制配置
     * 
     * @param string|null $key 配置键，为空则返回全部配置
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getTransferLimitsConfig($key = null, $default = null)
    {
        $config = self::getConfig('limits');
        
        if ($key === null) {
            return $config;
        }
        
        return self::getNestedValue($config, $key, $default);
    }

    /**
     * 获取预算规则配置
     * 
     * @param string|null $key 配置键，为空则返回全部配置
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getBudgetRulesConfig($key = null, $default = null)
    {
        $config = self::getConfig('budget');
        
        if ($key === null) {
            return $config;
        }
        
        return self::getNestedValue($config, $key, $default);
    }

    /**
     * 获取指定类型的配置
     * 
     * @param string $type 配置类型
     * @return array
     * @throws Exception
     */
    private static function getConfig($type)
    {
        if (!isset(self::CONFIG_FILES[$type])) {
            throw new Exception("未知的配置类型: {$type}");
        }

        $cacheKey = "config_{$type}";
        
        // 尝试从内存缓存获取
        if (isset(self::$configCache[$cacheKey])) {
            return self::$configCache[$cacheKey];
        }

        // 尝试从Redis缓存获取
        if (Yii::$app->cache) {
            $cached = Yii::$app->cache->get($cacheKey);
            if ($cached !== false) {
                self::$configCache[$cacheKey] = $cached;
                return $cached;
            }
        }

        // 从文件加载配置
        $config = self::loadConfigFromFile($type);
        
        // 验证配置
        self::validateConfig($type, $config);
        
        // 缓存配置
        self::$configCache[$cacheKey] = $config;
        if (Yii::$app->cache) {
            Yii::$app->cache->set($cacheKey, $config, self::$cacheTime);
        }

        return $config;
    }

    /**
     * 从文件加载配置
     * 
     * @param string $type 配置类型
     * @return array
     * @throws Exception
     */
    private static function loadConfigFromFile($type)
    {
        $filename = self::CONFIG_FILES[$type];
        $filepath = self::$configDir . DIRECTORY_SEPARATOR . $filename;

        if (!file_exists($filepath)) {
            throw new Exception("配置文件不存在: {$filepath}");
        }

        $config = require $filepath;

        if (!is_array($config)) {
            throw new Exception("配置文件格式错误: {$filepath}");
        }

        return $config;
    }

    /**
     * 验证配置
     * 
     * @param string $type 配置类型
     * @param array $config 配置数据
     * @throws Exception
     */
    private static function validateConfig($type, $config)
    {
        switch ($type) {
            case 'platform':
                self::validatePlatformConfig($config);
                break;
            case 'limits':
                self::validateLimitsConfig($config);
                break;
            case 'budget':
                self::validateBudgetConfig($config);
                break;
        }
    }

    /**
     * 验证平台配置
     * 
     * @param array $config
     * @throws Exception
     */
    private static function validatePlatformConfig($config)
    {
        $required = ['platforms', 'platform_limits', 'default'];
        foreach ($required as $key) {
            if (!isset($config[$key])) {
                throw new Exception("平台配置缺少必需字段: {$key}");
            }
        }

        // 验证平台配置完整性
        foreach ($config['platforms'] as $platform => $info) {
            if (!isset($info['adapter_class']) || !isset($info['enabled'])) {
                throw new Exception("平台 {$platform} 配置不完整");
            }
        }
    }

    /**
     * 验证限制配置
     * 
     * @param array $config
     * @throws Exception
     */
    private static function validateLimitsConfig($config)
    {
        $required = ['time_restrictions', 'batch_limits', 'frequency_control'];
        foreach ($required as $key) {
            if (!isset($config[$key])) {
                throw new Exception("限制配置缺少必需字段: {$key}");
            }
        }
    }

    /**
     * 验证预算配置
     * 
     * @param array $config
     * @throws Exception
     */
    private static function validateBudgetConfig($config)
    {
        $required = ['budget_control', 'user_daily_limits', 'balance_warnings'];
        foreach ($required as $key) {
            if (!isset($config[$key])) {
                throw new Exception("预算配置缺少必需字段: {$key}");
            }
        }
    }

    /**
     * 获取嵌套配置值
     * 
     * @param array $config 配置数组
     * @param string $key 配置键，支持点号分隔的嵌套键
     * @param mixed $default 默认值
     * @return mixed
     */
    private static function getNestedValue($config, $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $config;

        foreach ($keys as $k) {
            if (!is_array($value) || !isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * 清除配置缓存
     * 
     * @param string|null $type 配置类型，为空则清除所有缓存
     */
    public static function clearCache($type = null)
    {
        if ($type === null) {
            // 清除所有缓存
            self::$configCache = [];
            if (Yii::$app->cache) {
                foreach (array_keys(self::CONFIG_FILES) as $configType) {
                    Yii::$app->cache->delete("config_{$configType}");
                }
            }
        } else {
            // 清除指定类型缓存
            $cacheKey = "config_{$type}";
            unset(self::$configCache[$cacheKey]);
            if (Yii::$app->cache) {
                Yii::$app->cache->delete($cacheKey);
            }
        }
    }

    /**
     * 重新加载配置
     * 
     * @param string|null $type 配置类型，为空则重新加载所有配置
     */
    public static function reload($type = null)
    {
        self::clearCache($type);
        
        if ($type === null) {
            // 重新加载所有配置
            foreach (array_keys(self::CONFIG_FILES) as $configType) {
                self::getConfig($configType);
            }
        } else {
            // 重新加载指定配置
            self::getConfig($type);
        }
    }

    /**
     * 获取平台适配器类名
     * 
     * @param string $platform 平台名称
     * @return string
     * @throws Exception
     */
    public static function getPlatformAdapterClass($platform)
    {
        $platforms = self::getPlatformConfig('platforms');
        
        if (!isset($platforms[$platform])) {
            throw new Exception("未知的平台: {$platform}");
        }

        if (!$platforms[$platform]['enabled']) {
            throw new Exception("平台已禁用: {$platform}");
        }

        return $platforms[$platform]['adapter_class'];
    }

    /**
     * 获取平台限额配置
     * 
     * @param string $platform 平台名称
     * @param string|null $limitType 限额类型
     * @return mixed
     */
    public static function getPlatformLimit($platform, $limitType = null)
    {
        $limits = self::getPlatformConfig('platform_limits');
        
        if (!isset($limits[$platform])) {
            // 返回默认配置
            $limits = self::getPlatformConfig('default');
        } else {
            $limits = $limits[$platform];
        }

        if ($limitType === null) {
            return $limits;
        }

        return isset($limits[$limitType]) ? $limits[$limitType] : null;
    }

    /**
     * 检查时间限制
     * 
     * @param string|null $currentTime 当前时间，格式 H:i
     * @return bool
     */
    public static function isTimeRestricted($currentTime = null)
    {
        $restrictions = self::getTransferLimitsConfig('time_restrictions.forbidden_hours');
        
        if (!$restrictions['enabled']) {
            return false;
        }

        $currentTime = $currentTime ?: date('H:i');
        $startTime = $restrictions['start'];
        $endTime = $restrictions['end'];

        return $currentTime >= $startTime && $currentTime <= $endTime;
    }

    /**
     * 获取批量限制
     * 
     * @param string|null $limitType 限制类型
     * @return mixed
     */
    public static function getBatchLimit($limitType = null)
    {
        $limits = self::getTransferLimitsConfig('batch_limits');
        
        if ($limitType === null) {
            return $limits;
        }

        return isset($limits[$limitType]) ? $limits[$limitType] : null;
    }

    /**
     * 获取余额警告阈值
     * 
     * @return int
     */
    public static function getBalanceWarningThreshold()
    {
        return self::getBudgetRulesConfig('balance_warnings.warning_threshold', 3000);
    }

    /**
     * 检查是否启用预算控制
     * 
     * @return bool
     */
    public static function isBudgetControlEnabled()
    {
        return self::getBudgetRulesConfig('budget_control.enabled', false);
    }

    /**
     * 获取用户每日限额
     * 
     * @param string $userType 用户类型：default/vip/admin
     * @return int
     */
    public static function getUserDailyLimit($userType = 'default')
    {
        $limitKey = $userType === 'default' ? 'default_limit' : "{$userType}_limit";
        return self::getBudgetRulesConfig("user_daily_limits.{$limitKey}", 10000);
    }

    /**
     * 获取配置版本信息
     * 
     * @param string $type 配置类型
     * @return array
     */
    public static function getConfigVersion($type)
    {
        $config = self::getConfig($type);
        return [
            'version' => $config['version'] ?? 'unknown',
            'updated_at' => $config['updated_at'] ?? 'unknown',
        ];
    }

    /**
     * 获取所有配置的版本信息
     * 
     * @return array
     */
    public static function getAllConfigVersions()
    {
        $versions = [];
        foreach (array_keys(self::CONFIG_FILES) as $type) {
            $versions[$type] = self::getConfigVersion($type);
        }
        return $versions;
    }
}