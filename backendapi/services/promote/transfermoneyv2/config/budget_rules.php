<?php

/**
 * 预算规则配置文件
 * 
 * 定义预算限制、用户限额、余额警告等预算管理规则
 */

return [
    // 预算限制总开关
    'budget_control' => [
        'enabled' => true,                        // 是否启用预算限制
        'strict_mode' => false,                   // 严格模式（超预算直接拒绝）
        'warning_mode' => true,                   // 警告模式（超预算发送警告但允许）
        'auto_adjustment' => false,               // 自动调整预算
    ],

    // 用户每日限额配置
    'user_daily_limits' => [
        'enabled' => true,                        // 是否启用用户每日限额
        'default_limit' => 10000,                 // 默认每日限额（元）
        'vip_limit' => 50000,                     // VIP用户每日限额（元）
        'admin_limit' => 100000,                  // 管理员每日限额（元）
        'reset_time' => '00:00',                  // 限额重置时间
        'timezone' => 'Asia/Shanghai',            // 时区
    ],

    // 全局每日限额
    'global_daily_limits' => [
        'enabled' => true,                        // 是否启用全局每日限额
        'total_limit' => 1000000,                 // 全局每日总限额（元）
        'platform_limits' => [
            'tiktok' => 500000,                   // 抖音平台每日限额（元）
            'adq' => 500000,                      // ADQ平台每日限额（元）
        ],
        'emergency_reserve' => 100000,            // 紧急预留金额（元）
    ],

    // 余额警告阈值
    'balance_warnings' => [
        'enabled' => true,                        // 是否启用余额警告
        'warning_threshold' => 3000,              // 余额警告阈值（元）
        'critical_threshold' => 1000,             // 余额严重不足阈值（元）
        'auto_recharge_threshold' => 500,         // 自动充值触发阈值（元）
        'notification_interval' => 3600,          // 通知间隔（秒）
    ],

    // 预算分配规则
    'budget_allocation' => [
        'auto_allocation' => false,               // 自动分配预算
        'allocation_strategy' => 'proportional', // 分配策略：proportional/equal/priority
        'department_budgets' => [
            'marketing' => 400000,                // 市场部预算（元）
            'sales' => 300000,                    // 销售部预算（元）
            'operations' => 200000,               // 运营部预算（元）
            'others' => 100000,                   // 其他部门预算（元）
        ],
        'project_budgets' => [
            'project_a' => 200000,                // 项目A预算（元）
            'project_b' => 150000,                // 项目B预算（元）
            'project_c' => 100000,                // 项目C预算（元）
        ],
    ],

    // 预算监控规则
    'budget_monitoring' => [
        'real_time_tracking' => true,             // 实时跟踪
        'usage_alerts' => [
            'threshold_50' => true,               // 50%使用率警告
            'threshold_80' => true,               // 80%使用率警告
            'threshold_95' => true,               // 95%使用率警告
            'threshold_100' => true,              // 100%使用率警告
        ],
        'trend_analysis' => true,                 // 趋势分析
        'forecast_enabled' => true,               // 预测功能
    ],

    // 超预算处理规则
    'over_budget_handling' => [
        'action' => 'warn',                       // 处理动作：block/warn/approve
        'approval_required' => true,              // 是否需要审批
        'auto_approval_limit' => 1000,            // 自动审批限额（元）
        'escalation_levels' => [
            'level_1' => 5000,                    // 一级审批限额（元）
            'level_2' => 20000,                   // 二级审批限额（元）
            'level_3' => 50000,                   // 三级审批限额（元）
        ],
    ],

    // 预算报告配置
    'budget_reporting' => [
        'daily_report' => true,                   // 每日报告
        'weekly_report' => true,                  // 每周报告
        'monthly_report' => true,                 // 每月报告
        'report_recipients' => [
            '<EMAIL>',
            '<EMAIL>',
        ],
        'report_format' => 'excel',               // 报告格式：excel/pdf/json
    ],

    // 成本控制规则
    'cost_control' => [
        'cost_per_acquisition_limit' => 100,     // 单次获客成本限制（元）
        'roi_threshold' => 1.5,                  // ROI阈值
        'cost_efficiency_tracking' => true,      // 成本效率跟踪
        'optimization_suggestions' => true,      // 优化建议
    ],

    // 紧急情况处理
    'emergency_handling' => [
        'emergency_fund' => 50000,                // 紧急资金（元）
        'emergency_contacts' => [
            'primary' => '<EMAIL>',
            'secondary' => '<EMAIL>',
        ],
        'auto_suspend_threshold' => 0.95,         // 自动暂停阈值（预算使用率）
        'manual_override' => true,                // 手动覆盖
    ],

    // 默认配置
    'default' => [
        'user_daily_limit' => 10000,
        'warning_threshold' => 3000,
        'critical_threshold' => 1000,
        'notification_interval' => 3600,
    ],

    // 配置版本和更新时间
    'version' => '1.0.0',
    'updated_at' => '2025-01-23 16:50:00',
];