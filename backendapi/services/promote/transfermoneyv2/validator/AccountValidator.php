<?php

namespace backendapi\services\promote\transfermoneyv2\validator;

use Exception;

/**
 * 账户验证器
 * 
 * 基于现有TransferMoneyBatchService::verificationAccount()方法逻辑
 * 验证账户ID不能为空，验证同批次账户必须属于同一平台
 */
class AccountValidator extends AbstractValidator
{
    /**
     * @var callable|null 平台查询回调函数
     */
    private $platformQueryCallback;

    /**
     * 构造函数
     * 
     * @param callable|null $platformQueryCallback 平台查询回调函数
     */
    public function __construct(?callable $platformQueryCallback = null)
    {
        $this->platformQueryCallback = $platformQueryCallback;
    }

    /**
     * 获取验证器名称
     * 
     * @return string 验证器名称
     */
    public function getName(): string
    {
        return 'account';
    }

    /**
     * 执行账户验证
     * 
     * @param array $data 待验证的数据，必须包含 'target_advertiser_ids' 键
     * @return bool 验证是否通过
     * @throws Exception 验证失败时抛出异常
     */
    public function validate(array $data): bool
    {
        if (!isset($data['target_advertiser_ids'])) {
            $errorMessage = '缺少必要参数: target_advertiser_ids';
            $this->setErrorMessage($errorMessage);
            throw new Exception($errorMessage);
        }

        $targetAdvertiserIds = $data['target_advertiser_ids'];

        // 处理字符串格式的账户ID（用"、"分隔）
        if (is_string($targetAdvertiserIds)) {
            $targetAdvertiserIds = $this->parseAccountIds($targetAdvertiserIds);
        }

        // 验证账户ID不能为空
        if (empty($targetAdvertiserIds)) {
            $errorMessage = '账户ID不能为空';
            $this->setErrorMessage($errorMessage);
            throw new Exception($errorMessage);
        }

        // 去重处理
        $targetAdvertiserIds = array_unique($targetAdvertiserIds);

        // 如果有平台查询回调，验证同批次账户必须属于同一平台
        if ($this->platformQueryCallback !== null) {
            $this->validateSamePlatform($targetAdvertiserIds);
        }

        return true;
    }

    /**
     * 解析账户ID字符串
     * 
     * @param string $accountIdsString 账户ID字符串（用"、"分隔）
     * @return array 账户ID数组
     */
    private function parseAccountIds(string $accountIdsString): array
    {
        $targetAdvertiserIds = explode('、', $accountIdsString);
        
        // 过滤空值
        $targetAdvertiserIds = array_filter($targetAdvertiserIds, function ($value) {
            return $value !== null && $value !== '';
        });

        return array_values($targetAdvertiserIds);
    }

    /**
     * 验证同批次账户必须属于同一平台
     * 
     * @param array $targetAdvertiserIds 账户ID数组
     * @throws Exception 验证失败时抛出异常
     */
    private function validateSamePlatform(array $targetAdvertiserIds): void
    {
        $platforms = call_user_func($this->platformQueryCallback, $targetAdvertiserIds);

        if (count($platforms) > 1) {
            $errorMessage = '不允许多平台账户充值';
            $this->setErrorMessage($errorMessage);
            throw new Exception($errorMessage);
        }

        if (empty($platforms)) {
            $errorMessage = '无法确定账户所属平台';
            $this->setErrorMessage($errorMessage);
            throw new Exception($errorMessage);
        }
    }

    /**
     * 设置平台查询回调函数
     * 
     * @param callable $callback 平台查询回调函数
     */
    public function setPlatformQueryCallback(callable $callback): void
    {
        $this->platformQueryCallback = $callback;
    }

    /**
     * 获取平台查询回调函数
     * 
     * @return callable|null 平台查询回调函数
     */
    public function getPlatformQueryCallback(): ?callable
    {
        return $this->platformQueryCallback;
    }

    /**
     * 验证单个账户ID格式
     * 
     * @param mixed $accountId 账户ID
     * @return bool 是否为有效的账户ID格式
     */
    public static function isValidAccountIdFormat($accountId): bool
    {
        // 检查是否为字符串或数字
        if (!is_string($accountId) && !is_numeric($accountId)) {
            return false;
        }

        // 转换为字符串
        $stringAccountId = (string)$accountId;

        // 检查是否为空
        if (trim($stringAccountId) === '') {
            return false;
        }

        // 检查长度（一般账户ID长度在6-20位之间）
        $length = strlen($stringAccountId);
        if ($length < 6 || $length > 20) {
            return false;
        }

        // 检查是否只包含数字
        if (!ctype_digit($stringAccountId)) {
            return false;
        }

        return true;
    }

    /**
     * 格式化账户ID数组
     * 
     * @param array $accountIds 原始账户ID数组
     * @return array 格式化后的账户ID数组
     * @throws Exception 包含无效账户ID时抛出异常
     */
    public static function formatAccountIds(array $accountIds): array
    {
        $formattedIds = [];

        foreach ($accountIds as $accountId) {
            if (!self::isValidAccountIdFormat($accountId)) {
                throw new Exception('无效的账户ID格式: ' . $accountId);
            }

            $formattedIds[] = (string)$accountId;
        }

        return array_unique($formattedIds);
    }

    /**
     * 创建默认的平台查询回调函数（用于测试）
     * 
     * @param array $mockPlatformData 模拟平台数据
     * @return callable 平台查询回调函数
     */
    public static function createMockPlatformQueryCallback(array $mockPlatformData = []): callable
    {
        return function (array $accountIds) use ($mockPlatformData) {
            $platforms = [];
            
            foreach ($accountIds as $accountId) {
                if (isset($mockPlatformData[$accountId])) {
                    $platform = $mockPlatformData[$accountId];
                    if (!in_array($platform, $platforms)) {
                        $platforms[] = $platform;
                    }
                }
            }
            
            return $platforms;
        };
    }

    /**
     * 创建基于数据库查询的平台查询回调函数
     * 
     * @return callable 平台查询回调函数
     */
    public static function createDatabasePlatformQueryCallback(): callable
    {
        return function (array $accountIds) {
            // 这里应该实现真实的数据库查询逻辑
            // 基于现有的 AdsAccountSub 模型查询平台信息
            // 暂时返回空数组，在实际使用时需要实现
            return [];
        };
    }
}