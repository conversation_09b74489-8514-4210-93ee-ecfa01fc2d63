<?php

namespace backendapi\services\promote\transfermoneyv2\validator;

use Exception;

/**
 * 时间验证器
 * 
 * 基于现有TransferMoneyBatchService::timeLimit()方法逻辑
 * 支持凌晨2:00-6:30禁止充值，从配置文件读取时间限制规则
 */
class TimeValidator extends AbstractValidator
{
    /**
     * @var string 当前时间（用于测试）
     */
    private $currentTime;

    /**
     * @var string 禁止充值开始时间
     */
    private $startTime;

    /**
     * @var string 禁止充值结束时间
     */
    private $endTime;

    /**
     * 构造函数
     * 
     * @param string|null $currentTime 当前时间（用于测试，格式：H:i）
     * @param string $startTime 禁止充值开始时间（默认：02:00）
     * @param string $endTime 禁止充值结束时间（默认：06:30）
     */
    public function __construct(?string $currentTime = null, string $startTime = '02:00', string $endTime = '06:30')
    {
        $this->currentTime = $currentTime;
        $this->startTime = $startTime;
        $this->endTime = $endTime;
    }

    /**
     * 获取验证器名称
     * 
     * @return string 验证器名称
     */
    public function getName(): string
    {
        return 'time';
    }

    /**
     * 执行时间验证
     * 
     * @param array $data 待验证的数据
     * @return bool 验证是否通过
     * @throws Exception 验证失败时抛出异常
     */
    public function validate(array $data): bool
    {
        $currentTime = $this->getCurrentTime();

        // 判断当前时间是否在禁止充值的时间范围内
        if ($currentTime >= $this->startTime && $currentTime <= $this->endTime) {
            $errorMessage = $this->formatErrorMessage();
            $this->setErrorMessage($errorMessage);
            throw new Exception($errorMessage);
        }

        return true;
    }

    /**
     * 获取当前时间
     * 
     * @return string 当前时间（格式：H:i）
     */
    private function getCurrentTime(): string
    {
        if ($this->currentTime !== null) {
            return $this->currentTime;
        }

        return date('H:i');
    }

    /**
     * 格式化错误信息
     * 
     * @return string 错误信息
     */
    private function formatErrorMessage(): string
    {
        // 默认的错误信息格式
        if ($this->startTime === '02:00' && $this->endTime === '06:30') {
            return '"凌晨2点到6点30分"时间段不可充值';
        }

        // 自定义时间范围的错误信息
        $startHour = (int)substr($this->startTime, 0, 2);
        $endHour = (int)substr($this->endTime, 0, 2);
        
        $startPeriod = $startHour < 12 ? '上午' : '下午';
        $endPeriod = $endHour < 12 ? '上午' : '下午';
        
        $startDisplayHour = $startHour > 12 ? $startHour - 12 : $startHour;
        $endDisplayHour = $endHour > 12 ? $endHour - 12 : $endHour;
        
        return sprintf('"%s%d点到%s%d点"时间段不可充值', 
            $startPeriod, $startDisplayHour, $endPeriod, $endDisplayHour);
    }

    /**
     * 从配置文件读取时间限制规则
     * 
     * @return array 时间限制配置
     */
    public static function getTimeConfigFromFile(): array
    {
        // 这里可以从配置文件读取时间限制规则
        // 暂时返回默认配置
        return [
            'start_time' => '02:00',
            'end_time' => '06:30',
            'enabled' => true
        ];
    }

    /**
     * 创建基于配置文件的时间验证器
     * 
     * @return TimeValidator
     */
    public static function createFromConfig(): TimeValidator
    {
        $config = self::getTimeConfigFromFile();
        return new self(null, $config['start_time'], $config['end_time']);
    }
}