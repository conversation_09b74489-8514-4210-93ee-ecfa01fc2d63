<?php

namespace backendapi\services\promote\transfermoneyv2\validator;

use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use Exception;

/**
 * 充值验证器
 * 
 * 组合所有验证器进行链式验证
 * 支持动态添加自定义验证器
 */
class TransferValidator extends AbstractValidator
{
    /**
     * @var ValidatorInterface[] 验证器链
     */
    private $validators = [];

    /**
     * @var PlatformAdapterInterface|null 平台适配器
     */
    private $platformAdapter;

    /**
     * 构造函数
     * 
     * @param PlatformAdapterInterface|null $platformAdapter 平台适配器
     */
    public function __construct(?PlatformAdapterInterface $platformAdapter = null)
    {
        $this->platformAdapter = $platformAdapter;
        $this->initializeDefaultValidators();
    }

    /**
     * 获取验证器名称
     * 
     * @return string 验证器名称
     */
    public function getName(): string
    {
        return 'transfer';
    }

    /**
     * 执行充值验证
     * 
     * @param array $data 待验证的数据
     * @return bool 验证是否通过
     * @throws Exception 验证失败时抛出异常
     */
    public function validate(array $data): bool
    {
        if (empty($this->validators)) {
            return true;
        }

        // 执行链式验证
        $firstValidator = reset($this->validators);
        return $firstValidator->handle($data);
    }

    /**
     * 初始化默认验证器
     */
    private function initializeDefaultValidators(): void
    {
        // 1. 时间验证器
        $timeValidator = TimeValidator::createFromConfig();
        
        // 2. 金额验证器
        $amountValidator = new AmountValidator($this->platformAdapter);
        
        // 3. 账户验证器
        $accountValidator = new AccountValidator();
        if ($this->platformAdapter !== null) {
            // 如果有平台适配器，设置平台查询回调
            $accountValidator->setPlatformQueryCallback(
                $this->createPlatformQueryCallback()
            );
        }

        // 构建验证器链
        $this->addValidator($timeValidator);
        $this->addValidator($amountValidator);
        $this->addValidator($accountValidator);
    }

    /**
     * 添加验证器到链中
     * 
     * @param ValidatorInterface $validator 验证器
     * @return TransferValidator 返回自身支持链式调用
     */
    public function addValidator(ValidatorInterface $validator): TransferValidator
    {
        if (!empty($this->validators)) {
            // 将新验证器添加到链的末尾
            $lastValidator = end($this->validators);
            $lastValidator->setNext($validator);
        }
        
        $this->validators[] = $validator;
        return $this;
    }

    /**
     * 移除指定名称的验证器
     * 
     * @param string $validatorName 验证器名称
     * @return TransferValidator 返回自身支持链式调用
     */
    public function removeValidator(string $validatorName): TransferValidator
    {
        $this->validators = array_filter($this->validators, function($validator) use ($validatorName) {
            return $validator->getName() !== $validatorName;
        });
        
        // 重新构建验证器链
        $this->rebuildValidatorChain();
        return $this;
    }

    /**
     * 获取指定名称的验证器
     * 
     * @param string $validatorName 验证器名称
     * @return ValidatorInterface|null 验证器实例
     */
    public function getValidator(string $validatorName): ?ValidatorInterface
    {
        foreach ($this->validators as $validator) {
            if ($validator->getName() === $validatorName) {
                return $validator;
            }
        }
        
        return null;
    }

    /**
     * 获取所有验证器
     * 
     * @return ValidatorInterface[] 验证器数组
     */
    public function getValidators(): array
    {
        return $this->validators;
    }

    /**
     * 清空所有验证器
     * 
     * @return TransferValidator 返回自身支持链式调用
     */
    public function clearValidators(): TransferValidator
    {
        $this->validators = [];
        return $this;
    }

    /**
     * 重新构建验证器链
     */
    private function rebuildValidatorChain(): void
    {
        $validators = array_values($this->validators);
        
        for ($i = 0; $i < count($validators) - 1; $i++) {
            $validators[$i]->setNext($validators[$i + 1]);
        }
    }

    /**
     * 设置平台适配器
     * 
     * @param PlatformAdapterInterface $platformAdapter 平台适配器
     */
    public function setPlatformAdapter(PlatformAdapterInterface $platformAdapter): void
    {
        $this->platformAdapter = $platformAdapter;
        
        // 更新金额验证器的平台适配器
        $amountValidator = $this->getValidator('amount');
        if ($amountValidator instanceof AmountValidator) {
            $amountValidator->setPlatformAdapter($platformAdapter);
        }
        
        // 更新账户验证器的平台查询回调
        $accountValidator = $this->getValidator('account');
        if ($accountValidator instanceof AccountValidator) {
            $accountValidator->setPlatformQueryCallback(
                $this->createPlatformQueryCallback()
            );
        }
    }

    /**
     * 获取平台适配器
     * 
     * @return PlatformAdapterInterface|null 平台适配器
     */
    public function getPlatformAdapter(): ?PlatformAdapterInterface
    {
        return $this->platformAdapter;
    }

    /**
     * 创建平台查询回调函数
     * 
     * @return callable 平台查询回调函数
     */
    private function createPlatformQueryCallback(): callable
    {
        return function (array $accountIds) {
            // 这里应该实现真实的数据库查询逻辑
            // 基于现有的 AdsAccountSub 模型查询平台信息
            // 暂时返回模拟数据，在实际使用时需要实现
            return ['mock_platform'];
        };
    }

    /**
     * 创建基于平台的充值验证器
     * 
     * @param PlatformAdapterInterface $platformAdapter 平台适配器
     * @return TransferValidator
     */
    public static function createForPlatform(PlatformAdapterInterface $platformAdapter): TransferValidator
    {
        return new self($platformAdapter);
    }

    /**
     * 创建默认的充值验证器（不带平台适配器）
     * 
     * @return TransferValidator
     */
    public static function createDefault(): TransferValidator
    {
        return new self();
    }

    /**
     * 创建自定义的充值验证器
     * 
     * @param ValidatorInterface[] $validators 自定义验证器数组
     * @return TransferValidator
     */
    public static function createCustom(array $validators): TransferValidator
    {
        $transferValidator = new self();
        $transferValidator->clearValidators();
        
        foreach ($validators as $validator) {
            $transferValidator->addValidator($validator);
        }
        
        return $transferValidator;
    }

    /**
     * 获取验证器链的详细信息
     * 
     * @return array 验证器链信息
     */
    public function getValidatorChainInfo(): array
    {
        $info = [];
        
        foreach ($this->validators as $index => $validator) {
            $info[] = [
                'index' => $index,
                'name' => $validator->getName(),
                'class' => get_class($validator)
            ];
        }
        
        return $info;
    }

    /**
     * 验证数据并返回详细的验证结果
     * 
     * @param array $data 待验证的数据
     * @return array 验证结果详情
     */
    public function validateWithDetails(array $data): array
    {
        $results = [];
        
        foreach ($this->validators as $validator) {
            try {
                $result = $validator->validate($data);
                $results[] = [
                    'validator' => $validator->getName(),
                    'success' => $result,
                    'error' => null
                ];
            } catch (Exception $e) {
                $results[] = [
                    'validator' => $validator->getName(),
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                // 验证失败，停止后续验证
                break;
            }
        }
        
        return $results;
    }
}