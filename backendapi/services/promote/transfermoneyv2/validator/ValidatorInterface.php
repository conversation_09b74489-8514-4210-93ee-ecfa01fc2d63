<?php

namespace backendapi\services\promote\transfermoneyv2\validator;

/**
 * 验证器接口
 * 
 * 定义所有验证器必须实现的基本方法
 * 支持链式验证调用和动态添加自定义验证器
 */
interface ValidatorInterface
{
    /**
     * 执行验证
     * 
     * @param array $data 待验证的数据
     * @return bool 验证是否通过
     * @throws \Exception 验证失败时抛出异常
     */
    public function validate(array $data): bool;

    /**
     * 获取验证器名称
     * 
     * @return string 验证器名称
     */
    public function getName(): string;

    /**
     * 获取验证错误信息
     * 
     * @return string 错误信息
     */
    public function getErrorMessage(): string;

    /**
     * 设置下一个验证器（链式验证）
     * 
     * @param ValidatorInterface $validator 下一个验证器
     * @return ValidatorInterface 返回自身支持链式调用
     */
    public function setNext(ValidatorInterface $validator): ValidatorInterface;

    /**
     * 执行链式验证
     * 
     * @param array $data 待验证的数据
     * @return bool 验证是否通过
     * @throws \Exception 验证失败时抛出异常
     */
    public function handle(array $data): bool;
}