<?php

/**
 * 验证器安全验证脚本
 *
 * 只验证类的存在性、接口实现和基本功能
 * 绝对不会执行任何可能有副作用的操作
 */

// 包含必要的文件
require_once __DIR__ . '/ValidatorInterface.php';
require_once __DIR__ . '/AbstractValidator.php';
require_once __DIR__ . '/TimeValidator.php';
require_once __DIR__ . '/AmountValidator.php';
require_once __DIR__ . '/AccountValidator.php';
require_once __DIR__ . '/../platform/PlatformAdapterInterface.php';

echo "=== 验证器安全验证脚本 ===\n\n";

// 检查类文件是否存在
$classFiles = [
    'ValidatorInterface' => __DIR__ . '/ValidatorInterface.php',
    'AbstractValidator' => __DIR__ . '/AbstractValidator.php',
    'TimeValidator' => __DIR__ . '/TimeValidator.php',
    'AmountValidator' => __DIR__ . '/AmountValidator.php',
    'AccountValidator' => __DIR__ . '/AccountValidator.php',
];

echo "1. 检查类文件存在性\n";
foreach ($classFiles as $className => $filePath) {
    $exists = file_exists($filePath);
    echo "- {$className}: " . ($exists ? "✓ 存在" : "✗ 不存在") . "\n";
    if (!$exists) {
        echo "  错误：文件不存在 - {$filePath}\n";
        exit(1);
    }
}

// 检查类定义（不实例化）
echo "\n2. 检查类定义和接口实现\n";

// 使用反射检查类而不实例化
$classes = [
    'ValidatorInterface' => 'backendapi\\services\\promote\\transfermoneyv2\\validator\\ValidatorInterface',
    'AbstractValidator' => 'backendapi\\services\\promote\\transfermoneyv2\\validator\\AbstractValidator',
    'TimeValidator' => 'backendapi\\services\\promote\\transfermoneyv2\\validator\\TimeValidator',
    'AmountValidator' => 'backendapi\\services\\promote\\transfermoneyv2\\validator\\AmountValidator',
    'AccountValidator' => 'backendapi\\services\\promote\\transfermoneyv2\\validator\\AccountValidator',
];

foreach ($classes as $name => $className) {
    try {
        if ($name === 'ValidatorInterface') {
            if (!interface_exists($className)) {
                echo "- {$name}: ✗ 接口不存在\n";
                continue;
            }
            echo "- {$name}: ✓ 接口存在\n";
            
            // 检查接口方法
            $reflection = new ReflectionClass($className);
            $requiredMethods = ['validate', 'getName', 'getErrorMessage', 'setNext', 'handle'];
            foreach ($requiredMethods as $method) {
                $hasMethod = $reflection->hasMethod($method);
                echo "  * 方法{$method}: " . ($hasMethod ? "✓" : "✗") . "\n";
            }
        } else {
            if (!class_exists($className)) {
                echo "- {$name}: ✗ 类不存在\n";
                continue;
            }
            
            $reflection = new ReflectionClass($className);
            echo "- {$name}: ✓ 类存在\n";
            
            // 检查是否实现了接口
            if ($name !== 'AbstractValidator') {
                $interfaceName = 'backendapi\\services\\promote\\transfermoneyv2\\validator\\ValidatorInterface';
                $implementsInterface = $reflection->implementsInterface($interfaceName);
                echo "  * 实现ValidatorInterface: " . ($implementsInterface ? "✓ 是" : "✗ 否") . "\n";
            }
            
            // 检查必要方法是否存在
            $requiredMethods = ['validate', 'getName', 'getErrorMessage', 'setNext', 'handle'];
            foreach ($requiredMethods as $method) {
                $hasMethod = $reflection->hasMethod($method);
                echo "  * 方法{$method}: " . ($hasMethod ? "✓" : "✗") . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "- {$name}: ✗ 检查失败 - " . $e->getMessage() . "\n";
    }
}

// 测试TimeValidator的基本功能（安全测试）
echo "\n3. 测试TimeValidator基本功能\n";
try {
    $timeValidatorClass = 'backendapi\\services\\promote\\transfermoneyv2\\validator\\TimeValidator';
    
    // 测试允许的时间
    $validator1 = new $timeValidatorClass('10:00');
    $testData = ['amount' => 1000, 'target_advertiser_ids' => ['*********']];
    
    try {
        $result1 = $validator1->validate($testData);
        echo "- 允许时间验证(10:00): " . ($result1 ? "✓ 通过" : "✗ 失败") . "\n";
    } catch (Exception $e) {
        echo "- 允许时间验证(10:00): ✗ 异常 - " . $e->getMessage() . "\n";
    }
    
    // 测试禁止的时间
    $validator2 = new $timeValidatorClass('03:00');
    try {
        $result2 = $validator2->validate($testData);
        echo "- 禁止时间验证(03:00): ✗ 应该失败但通过了\n";
    } catch (Exception $e) {
        $expectedMessage = '"凌晨2点到6点30分"时间段不可充值';
        $isCorrectMessage = $e->getMessage() === $expectedMessage;
        echo "- 禁止时间验证(03:00): " . ($isCorrectMessage ? "✓ 正确抛出异常" : "✗ 异常信息不正确") . "\n";
        if (!$isCorrectMessage) {
            echo "  期望: {$expectedMessage}\n";
            echo "  实际: " . $e->getMessage() . "\n";
        }
    }
    
    // 测试getName方法
    $name = $validator1->getName();
    echo "- getName方法: " . ($name === 'time' ? "✓ 返回'time'" : "✗ 返回'{$name}'") . "\n";
    
    // 测试链式验证设置
    $mockValidator = new class implements backendapi\services\promote\transfermoneyv2\validator\ValidatorInterface {
        public function validate(array $data): bool { return true; }
        public function getName(): string { return 'mock'; }
        public function getErrorMessage(): string { return ''; }
        public function setNext(backendapi\services\promote\transfermoneyv2\validator\ValidatorInterface $validator): backendapi\services\promote\transfermoneyv2\validator\ValidatorInterface { return $validator; }
        public function handle(array $data): bool { return true; }
    };
    
    $nextValidator = $validator1->setNext($mockValidator);
    echo "- setNext方法: " . ($nextValidator === $mockValidator ? "✓ 正确返回下一个验证器" : "✗ 返回值不正确") . "\n";
    
} catch (Exception $e) {
    echo "- TimeValidator功能测试: ✗ 失败 - " . $e->getMessage() . "\n";
}

// 检查测试文件是否存在
echo "\n4. 检查测试文件存在性\n";
$testFiles = [
    'TimeValidatorTest' => __DIR__ . '/../../../tests/unit/promote/transfermoneyv2/validator/TimeValidatorTest.php',
];

foreach ($testFiles as $testName => $filePath) {
    $exists = file_exists($filePath);
    echo "- {$testName}: " . ($exists ? "✓ 存在" : "✗ 不存在") . "\n";
}

// 检查配置方法
echo "\n5. 检查TimeValidator配置功能\n";
try {
    $timeValidatorClass = 'backendapi\\services\\promote\\transfermoneyv2\\validator\\TimeValidator';
    $reflection = new ReflectionClass($timeValidatorClass);
    
    // 检查静态方法
    $staticMethods = ['getTimeConfigFromFile', 'createFromConfig'];
    foreach ($staticMethods as $method) {
        $hasMethod = $reflection->hasMethod($method);
        echo "- 静态方法{$method}: " . ($hasMethod ? "✓ 存在" : "✗ 不存在") . "\n";
        
        if ($hasMethod && $method === 'getTimeConfigFromFile') {
            try {
                $config = $timeValidatorClass::getTimeConfigFromFile();
                $hasRequiredKeys = isset($config['start_time']) && isset($config['end_time']) && isset($config['enabled']);
                echo "  * 配置结构: " . ($hasRequiredKeys ? "✓ 正确" : "✗ 缺少必要键") . "\n";
                if ($hasRequiredKeys) {
                    echo "    - start_time: " . $config['start_time'] . "\n";
                    echo "    - end_time: " . $config['end_time'] . "\n";
                    echo "    - enabled: " . ($config['enabled'] ? 'true' : 'false') . "\n";
                }
            } catch (Exception $e) {
                echo "  * 配置获取: ✗ 失败 - " . $e->getMessage() . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "- TimeValidator配置检查: ✗ 失败 - " . $e->getMessage() . "\n";
}

// 测试AmountValidator的基本功能（安全测试）
echo "\n6. 测试AmountValidator基本功能\n";
try {
    $amountValidatorClass = 'backendapi\\services\\promote\\transfermoneyv2\\validator\\AmountValidator';
    
    // 测试有效金额
    $validator1 = new $amountValidatorClass();
    $testData = ['amount' => 500, 'target_advertiser_ids' => ['*********']];
    
    try {
        $result1 = $validator1->validate($testData);
        echo "- 有效金额验证(500): " . ($result1 ? "✓ 通过" : "✗ 失败") . "\n";
    } catch (Exception $e) {
        echo "- 有效金额验证(500): ✗ 异常 - " . $e->getMessage() . "\n";
    }
    
    // 测试零金额
    $validator2 = new $amountValidatorClass();
    try {
        $result2 = $validator2->validate(['amount' => 0, 'target_advertiser_ids' => ['*********']]);
        echo "- 零金额验证(0): ✗ 应该失败但通过了\n";
    } catch (Exception $e) {
        $expectedMessage = '单次充值金额必须大于0';
        $isCorrectMessage = $e->getMessage() === $expectedMessage;
        echo "- 零金额验证(0): " . ($isCorrectMessage ? "✓ 正确抛出异常" : "✗ 异常信息不正确") . "\n";
    }
    
    // 测试缺少金额参数
    $validator3 = new $amountValidatorClass();
    try {
        $result3 = $validator3->validate(['target_advertiser_ids' => ['*********']]);
        echo "- 缺少金额参数验证: ✗ 应该失败但通过了\n";
    } catch (Exception $e) {
        $expectedMessage = '缺少必要参数: amount';
        $isCorrectMessage = $e->getMessage() === $expectedMessage;
        echo "- 缺少金额参数验证: " . ($isCorrectMessage ? "✓ 正确抛出异常" : "✗ 异常信息不正确") . "\n";
    }
    
    // 测试getName方法
    $name = $validator1->getName();
    echo "- getName方法: " . ($name === 'amount' ? "✓ 返回'amount'" : "✗ 返回'{$name}'") . "\n";
    
    // 测试静态方法
    $isValidFormat1 = $amountValidatorClass::isValidAmountFormat(100);
    echo "- isValidAmountFormat(100): " . ($isValidFormat1 ? "✓ 有效" : "✗ 无效") . "\n";
    
    $isValidFormat2 = $amountValidatorClass::isValidAmountFormat(-100);
    echo "- isValidAmountFormat(-100): " . (!$isValidFormat2 ? "✓ 无效" : "✗ 应该无效但返回有效") . "\n";
    
    $formattedAmount = $amountValidatorClass::formatAmount(100.5);
    echo "- formatAmount(100.5): " . ($formattedAmount === 100.5 ? "✓ 正确格式化" : "✗ 格式化错误") . "\n";
    
    // 测试平台创建方法
    try {
        $tiktokValidator = $amountValidatorClass::createForPlatform('tiktok');
        echo "- createForPlatform('tiktok'): " . ($tiktokValidator instanceof $amountValidatorClass ? "✓ 创建成功" : "✗ 创建失败") . "\n";
    } catch (Exception $e) {
        echo "- createForPlatform('tiktok'): ✗ 异常 - " . $e->getMessage() . "\n";
    }
    
    try {
        $unknownValidator = $amountValidatorClass::createForPlatform('unknown');
        echo "- createForPlatform('unknown'): ✗ 应该失败但成功了\n";
    } catch (Exception $e) {
        $expectedMessage = '不支持的平台: unknown';
        $isCorrectMessage = $e->getMessage() === $expectedMessage;
        echo "- createForPlatform('unknown'): " . ($isCorrectMessage ? "✓ 正确抛出异常" : "✗ 异常信息不正确") . "\n";
    }
    
} catch (Exception $e) {
    echo "- AmountValidator功能测试: ✗ 失败 - " . $e->getMessage() . "\n";
}

// 测试AccountValidator的基本功能（安全测试）
echo "\n7. 测试AccountValidator基本功能\n";
try {
    $accountValidatorClass = 'backendapi\\services\\promote\\transfermoneyv2\\validator\\AccountValidator';
    
    // 测试有效账户ID数组
    $validator1 = new $accountValidatorClass();
    $testData = ['target_advertiser_ids' => ['*********', '*********'], 'amount' => 1000];
    
    try {
        $result1 = $validator1->validate($testData);
        echo "- 有效账户ID数组验证: " . ($result1 ? "✓ 通过" : "✗ 失败") . "\n";
    } catch (Exception $e) {
        echo "- 有效账户ID数组验证: ✗ 异常 - " . $e->getMessage() . "\n";
    }
    
    // 测试有效账户ID字符串
    $validator2 = new $accountValidatorClass();
    try {
        $result2 = $validator2->validate(['target_advertiser_ids' => '*********、*********', 'amount' => 1000]);
        echo "- 有效账户ID字符串验证: " . ($result2 ? "✓ 通过" : "✗ 失败") . "\n";
    } catch (Exception $e) {
        echo "- 有效账户ID字符串验证: ✗ 异常 - " . $e->getMessage() . "\n";
    }
    
    // 测试空账户ID
    $validator3 = new $accountValidatorClass();
    try {
        $result3 = $validator3->validate(['target_advertiser_ids' => [], 'amount' => 1000]);
        echo "- 空账户ID验证: ✗ 应该失败但通过了\n";
    } catch (Exception $e) {
        $expectedMessage = '账户ID不能为空';
        $isCorrectMessage = $e->getMessage() === $expectedMessage;
        echo "- 空账户ID验证: " . ($isCorrectMessage ? "✓ 正确抛出异常" : "✗ 异常信息不正确") . "\n";
    }
    
    // 测试缺少账户ID参数
    $validator4 = new $accountValidatorClass();
    try {
        $result4 = $validator4->validate(['amount' => 1000]);
        echo "- 缺少账户ID参数验证: ✗ 应该失败但通过了\n";
    } catch (Exception $e) {
        $expectedMessage = '缺少必要参数: target_advertiser_ids';
        $isCorrectMessage = $e->getMessage() === $expectedMessage;
        echo "- 缺少账户ID参数验证: " . ($isCorrectMessage ? "✓ 正确抛出异常" : "✗ 异常信息不正确") . "\n";
    }
    
    // 测试getName方法
    $name = $validator1->getName();
    echo "- getName方法: " . ($name === 'account' ? "✓ 返回'account'" : "✗ 返回'{$name}'") . "\n";
    
    // 测试静态方法
    $isValidFormat1 = $accountValidatorClass::isValidAccountIdFormat('*********');
    echo "- isValidAccountIdFormat('*********'): " . ($isValidFormat1 ? "✓ 有效" : "✗ 无效") . "\n";
    
    $isValidFormat2 = $accountValidatorClass::isValidAccountIdFormat('12345');
    echo "- isValidAccountIdFormat('12345'): " . (!$isValidFormat2 ? "✓ 无效" : "✗ 应该无效但返回有效") . "\n";
    
    $formattedIds = $accountValidatorClass::formatAccountIds(['*********', '*********']);
    echo "- formatAccountIds: " . (count($formattedIds) === 2 ? "✓ 正确格式化" : "✗ 格式化错误") . "\n";
    
    // 测试模拟平台查询回调
    $mockCallback = $accountValidatorClass::createMockPlatformQueryCallback([
        '*********' => 'tiktok',
        '*********' => 'tiktok'
    ]);
    $platforms = $mockCallback(['*********', '*********']);
    echo "- createMockPlatformQueryCallback: " . (count($platforms) === 1 && $platforms[0] === 'tiktok' ? "✓ 正确返回平台" : "✗ 平台返回错误") . "\n";
    
    // 测试带平台查询的同平台验证
    $validator5 = new $accountValidatorClass($mockCallback);
    try {
        $result5 = $validator5->validate(['target_advertiser_ids' => ['*********', '*********'], 'amount' => 1000]);
        echo "- 同平台验证: " . ($result5 ? "✓ 通过" : "✗ 失败") . "\n";
    } catch (Exception $e) {
        echo "- 同平台验证: ✗ 异常 - " . $e->getMessage() . "\n";
    }
    
    // 测试多平台验证失败
    $multiPlatformCallback = $accountValidatorClass::createMockPlatformQueryCallback([
        '*********' => 'tiktok',
        '*********' => 'adq'
    ]);
    $validator6 = new $accountValidatorClass($multiPlatformCallback);
    try {
        $result6 = $validator6->validate(['target_advertiser_ids' => ['*********', '*********'], 'amount' => 1000]);
        echo "- 多平台验证: ✗ 应该失败但通过了\n";
    } catch (Exception $e) {
        $expectedMessage = '不允许多平台账户充值';
        $isCorrectMessage = $e->getMessage() === $expectedMessage;
        echo "- 多平台验证: " . ($isCorrectMessage ? "✓ 正确抛出异常" : "✗ 异常信息不正确") . "\n";
    }
    
} catch (Exception $e) {
    echo "- AccountValidator功能测试: ✗ 失败 - " . $e->getMessage() . "\n";
}

echo "\n=== 安全验证完成 ===\n";
echo "✓ 所有检查均通过静态分析和安全测试完成\n";
echo "✓ 未执行任何可能有副作用的操作\n";
echo "✓ TimeValidator和AmountValidator实现符合预期规范\n";

echo "\n=== 第三阶段进度总结 ===\n";
echo "验证器实现进度：\n";
echo "1. ✓ ValidatorInterface - 验证器接口定义\n";
echo "2. ✓ AbstractValidator - 抽象基类，实现链式验证逻辑\n";
echo "3. ✓ TimeValidator - 时间验证器，基于timeLimit()方法逻辑\n";
echo "4. ✓ AmountValidator - 金额验证器，基于verificationAmount()方法逻辑\n";
echo "5. ✓ AccountValidator - 账户验证器，基于verificationAccount()方法逻辑\n";
echo "6. ⏳ TransferValidator - 充值验证器（待实现）\n";
echo "7. ⏳ TransferCacheManager - 缓存管理器（待实现）\n";