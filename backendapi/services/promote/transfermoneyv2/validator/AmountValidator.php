<?php

namespace backendapi\services\promote\transfermoneyv2\validator;

use backendapi\services\promote\transfermoneyv2\platform\PlatformAdapterInterface;
use Exception;

/**
 * 金额验证器
 * 
 * 基于现有TransferMoneyBatchService::verificationAmount()方法逻辑
 * 验证金额必须大于0，根据平台适配器获取单次限额进行验证
 */
class AmountValidator extends AbstractValidator
{
    /**
     * @var PlatformAdapterInterface|null 平台适配器
     */
    private $platformAdapter;

    /**
     * 构造函数
     * 
     * @param PlatformAdapterInterface|null $platformAdapter 平台适配器
     */
    public function __construct(?PlatformAdapterInterface $platformAdapter = null)
    {
        $this->platformAdapter = $platformAdapter;
    }

    /**
     * 获取验证器名称
     * 
     * @return string 验证器名称
     */
    public function getName(): string
    {
        return 'amount';
    }

    /**
     * 执行金额验证
     * 
     * @param array $data 待验证的数据，必须包含 'amount' 键
     * @return bool 验证是否通过
     * @throws Exception 验证失败时抛出异常
     */
    public function validate(array $data): bool
    {
        if (!isset($data['amount'])) {
            $errorMessage = '缺少必要参数: amount';
            $this->setErrorMessage($errorMessage);
            throw new Exception($errorMessage);
        }

        $amount = $data['amount'];

        // 验证金额必须大于0
        if ($amount <= 0) {
            $errorMessage = '单次充值金额必须大于0';
            $this->setErrorMessage($errorMessage);
            throw new Exception($errorMessage);
        }

        // 如果有平台适配器，验证单次限额
        if ($this->platformAdapter !== null) {
            $maxAmount = $this->platformAdapter->getSingleLimit();
            
            if ($amount > $maxAmount) {
                $errorMessage = '单次充值金额不得超过' . $maxAmount;
                $this->setErrorMessage($errorMessage);
                throw new Exception($errorMessage);
            }
        }

        return true;
    }

    /**
     * 设置平台适配器
     * 
     * @param PlatformAdapterInterface $platformAdapter 平台适配器
     */
    public function setPlatformAdapter(PlatformAdapterInterface $platformAdapter): void
    {
        $this->platformAdapter = $platformAdapter;
    }

    /**
     * 获取平台适配器
     * 
     * @return PlatformAdapterInterface|null 平台适配器
     */
    public function getPlatformAdapter(): ?PlatformAdapterInterface
    {
        return $this->platformAdapter;
    }

    /**
     * 创建基于平台的金额验证器
     * 
     * @param string $platform 平台名称 ('tiktok', 'adq')
     * @return AmountValidator
     * @throws Exception 不支持的平台时抛出异常
     */
    public static function createForPlatform(string $platform): AmountValidator
    {
        // 这里可以根据平台名称创建对应的适配器
        // 暂时返回不带适配器的验证器，在实际使用时再设置
        $validator = new self();
        
        // 可以在这里添加平台特定的逻辑
        switch ($platform) {
            case 'tiktok':
                // 抖音平台：单次1000元限额
                break;
            case 'adq':
                // ADQ平台：单次2000元限额
                break;
            default:
                throw new Exception('不支持的平台: ' . $platform);
        }
        
        return $validator;
    }

    /**
     * 验证金额格式
     * 
     * @param mixed $amount 待验证的金额
     * @return bool 是否为有效的金额格式
     */
    public static function isValidAmountFormat($amount): bool
    {
        // 检查是否为数字
        if (!is_numeric($amount)) {
            return false;
        }

        // 转换为浮点数
        $floatAmount = (float)$amount;

        // 检查是否为正数
        if ($floatAmount <= 0) {
            return false;
        }

        // 检查小数位数不超过2位
        if (round($floatAmount, 2) != $floatAmount) {
            return false;
        }

        return true;
    }

    /**
     * 格式化金额
     * 
     * @param mixed $amount 原始金额
     * @return float 格式化后的金额
     * @throws Exception 金额格式无效时抛出异常
     */
    public static function formatAmount($amount): float
    {
        if (!self::isValidAmountFormat($amount)) {
            throw new Exception('无效的金额格式: ' . $amount);
        }

        return round((float)$amount, 2);
    }
}