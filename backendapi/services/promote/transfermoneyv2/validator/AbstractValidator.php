<?php

namespace backendapi\services\promote\transfermoneyv2\validator;

use Exception;

/**
 * 抽象验证器基类
 * 
 * 实现链式验证的通用逻辑
 * 所有具体验证器都应该继承此类
 */
abstract class AbstractValidator implements ValidatorInterface
{
    /**
     * @var ValidatorInterface|null 下一个验证器
     */
    private $nextValidator;

    /**
     * @var string 错误信息
     */
    protected $errorMessage = '';

    /**
     * 设置下一个验证器（链式验证）
     * 
     * @param ValidatorInterface $validator 下一个验证器
     * @return ValidatorInterface 返回自身支持链式调用
     */
    public function setNext(ValidatorInterface $validator): ValidatorInterface
    {
        $this->nextValidator = $validator;
        return $validator;
    }

    /**
     * 执行链式验证
     * 
     * @param array $data 待验证的数据
     * @return bool 验证是否通过
     * @throws Exception 验证失败时抛出异常
     */
    public function handle(array $data): bool
    {
        // 先执行当前验证器
        $this->validate($data);
        
        // 如果有下一个验证器，继续执行
        if ($this->nextValidator !== null) {
            return $this->nextValidator->handle($data);
        }
        
        return true;
    }

    /**
     * 获取验证错误信息
     * 
     * @return string 错误信息
     */
    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }

    /**
     * 设置错误信息
     * 
     * @param string $message 错误信息
     */
    protected function setErrorMessage(string $message): void
    {
        $this->errorMessage = $message;
    }

    /**
     * 抽象方法：执行具体的验证逻辑
     * 
     * @param array $data 待验证的数据
     * @return bool 验证是否通过
     * @throws Exception 验证失败时抛出异常
     */
    abstract public function validate(array $data): bool;

    /**
     * 抽象方法：获取验证器名称
     * 
     * @return string 验证器名称
     */
    abstract public function getName(): string;
}