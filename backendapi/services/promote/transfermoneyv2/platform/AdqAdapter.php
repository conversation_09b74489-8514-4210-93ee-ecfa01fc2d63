<?php

namespace backendapi\services\promote\transfermoneyv2\platform;

use common\components\promoteData\Adq;
use Yii;

/**
 * ADQ平台适配器
 * 
 * 基于现有Adq类实现的ADQ平台充值适配器
 * 调用Adq::subcustomerTransfer()和Adq::getBalance()方法
 */
class AdqAdapter implements PlatformAdapterInterface
{
    /**
     * @var Adq|null Adq实例，用于依赖注入测试
     */
    private $adqInstance;

    /**
     * @var int 单次充值限额（元）
     */
    private const SINGLE_LIMIT = 2000;

    /**
     * @var int 小时充值限额（元）
     */
    private const HOURLY_LIMIT = 20000;

    /**
     * 获取平台名称
     */
    public function getName(): string
    {
        return 'adq';
    }

    /**
     * 执行转账充值
     * 
     * 基于Adq::subcustomerTransfer()方法实现
     * 
     * @param string $accessToken 访问令牌
     * @param string $organizationId 组织ID (ADQ平台不需要此参数)
     * @param string $fromAccountId 源账户ID (备用金账户)
     * @param string $toAccountId 目标账户ID (充值目标账户)
     * @param int $amount 充值金额 (单位：元)
     * @return array 统一格式的返回结果
     * @throws \Exception 充值失败时抛出异常
     */
    public function transferMoney(
        string $accessToken,
        string $organizationId,
        string $fromAccountId,
        string $toAccountId,
        int $amount
    ): array {
        // 参数验证
        if ($amount <= 0) {
            throw new \Exception('充值金额必须大于0');
        }

        if ($amount > self::SINGLE_LIMIT) {
            throw new \Exception('充值金额超过单次限额' . self::SINGLE_LIMIT . '元');
        }

        // 记录操作日志
        Yii::info("ADQ平台充值开始 - 从账户: {$fromAccountId} 向账户: {$toAccountId} 充值: {$amount}元", 'adq_adapter');

        try {
            // 调用Adq::subcustomerTransfer方法
            $adq = $this->getAdqInstance();
            $result = $adq::subcustomerTransfer(
                $accessToken,
                $fromAccountId,
                $toAccountId,
                $amount
            );

            // 检查返回结果
            if (!isset($result['code']) || $result['code'] !== 0) {
                $errorMsg = $this->formatError($result);
                Yii::error("ADQ平台充值失败: {$errorMsg}", 'adq_adapter');
                throw new \Exception($errorMsg);
            }

            // 记录成功日志
            Yii::info("ADQ平台充值成功 - 交易号: " . ($result['data']['transfer_serial'] ?? $result['data']['external_bill_no'] ?? 'unknown'), 'adq_adapter');

            return $result;

        } catch (\Exception $e) {
            Yii::error("ADQ平台充值异常: " . $e->getMessage(), 'adq_adapter');
            throw $e;
        }
    }

    /**
     * 获取账户余额
     * 
     * 基于Adq::getBalance()方法实现
     * 
     * @param string $accessToken 访问令牌
     * @param string $accountId 账户ID
     * @return float 账户余额 (单位：元)
     * @throws \Exception 查询失败时抛出异常
     */
    public function getBalance(string $accessToken, string $accountId): float
    {
        try {
            // 调用Adq::getBalance方法
            $adq = $this->getAdqInstance();
            $result = $adq::getBalance($accessToken, $accountId, '', '');

            // ADQ的getBalance返回的是数组，包含多种资金类型
            $totalBalance = 0;
            
            if (is_array($result)) {
                foreach ($result as $fundInfo) {
                    if (isset($fundInfo['balance']) && isset($fundInfo['fund_status']) 
                        && $fundInfo['fund_status'] === 'FUND_STATUS_VALID') {
                        // ADQ接口返回的余额单位是分
                        $totalBalance += $fundInfo['balance'];
                    }
                }
            }

            // 转换为元
            $balanceInYuan = $totalBalance / 100;

            Yii::info("查询ADQ账户余额成功 - 账户: {$accountId}, 余额: {$balanceInYuan}元", 'adq_adapter');

            return (float)$balanceInYuan;

        } catch (\Exception $e) {
            $errorMsg = "查询ADQ账户余额失败: " . $e->getMessage();
            Yii::error($errorMsg, 'adq_adapter');
            throw new \Exception($errorMsg);
        }
    }

    /**
     * 获取单次充值限额
     */
    public function getSingleLimit(): int
    {
        return self::SINGLE_LIMIT;
    }

    /**
     * 获取小时充值限额
     */
    public function getHourlyLimit(): int
    {
        return self::HOURLY_LIMIT;
    }

    /**
     * 验证平台特定的参数
     * 
     * @param array $params 参数数组
     * @return bool 验证是否通过
     * @throws \Exception 验证失败时抛出异常
     */
    public function validateParams(array $params): bool
    {
        // ADQ平台不需要organization_id参数
        $required = ['access_token', 'from_account', 'to_account', 'amount'];
        
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '' || $params[$field] === null) {
                throw new \Exception('缺少必要参数: ' . $field);
            }
        }

        // 验证金额
        if (!is_numeric($params['amount']) || $params['amount'] <= 0) {
            throw new \Exception('充值金额必须是大于0的数字');
        }

        return true;
    }

    /**
     * 格式化错误信息
     * 
     * @param array $response 平台原始响应
     * @return string 格式化后的错误信息
     */
    public function formatError(array $response): string
    {
        $code = $response['code'] ?? 'unknown';
        $message = $response['message'] ?? '未知错误';
        $details = $response['details'] ?? '';

        $prefix = strpos($message, 'ADQ') === false ? 'ADQ错误' : '';
        $errorMsg = $prefix ? "{$prefix}[{$code}]: {$message}" : "ADQ转账失败[{$code}]: {$message}";

        return $errorMsg . ($details ? " - {$details}" : '');
    }

    /**
     * 设置Adq实例（用于测试依赖注入）
     * 
     * @param Adq $instance
     */
    public function setAdqInstance(Adq $instance): void
    {
        $this->adqInstance = $instance;
    }

    /**
     * 获取Adq实例
     * 
     * @return Adq
     */
    private function getAdqInstance(): Adq
    {
        if ($this->adqInstance !== null) {
            return $this->adqInstance;
        }

        return new Adq();
    }
}