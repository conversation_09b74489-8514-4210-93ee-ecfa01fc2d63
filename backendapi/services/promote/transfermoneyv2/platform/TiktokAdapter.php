<?php

namespace backendapi\services\promote\transfermoneyv2\platform;

use common\components\promoteData\Oceanengine;
use Yii;

/**
 * 抖音平台适配器
 * 
 * 基于现有Oceanengine类实现的抖音平台充值适配器
 * 调用Oceanengine::transferCreate()和Oceanengine::getFund()方法
 */
class TiktokAdapter implements PlatformAdapterInterface
{
    /**
     * @var Oceanengine|null Oceanengine实例，用于依赖注入测试
     */
    private $oceanengineInstance;

    /**
     * @var int 单次充值限额（元）
     */
    private const SINGLE_LIMIT = 1000;

    /**
     * @var int 小时充值限额（元）
     */
    private const HOURLY_LIMIT = 3000;

    /**
     * 获取平台名称
     */
    public function getName(): string
    {
        return 'tiktok';
    }

    /**
     * 执行转账充值
     * 
     * 基于Oceanengine::transferCreate()方法实现
     * 
     * @param string $accessToken 访问令牌
     * @param string $organizationId 组织ID
     * @param string $fromAccountId 源账户ID (备用金账户)
     * @param string $toAccountId 目标账户ID (充值目标账户)
     * @param int $amount 充值金额 (单位：元)
     * @return array 统一格式的返回结果
     * @throws \Exception 充值失败时抛出异常
     */
    public function transferMoney(
        string $accessToken,
        string $organizationId,
        string $fromAccountId,
        string $toAccountId,
        int $amount
    ): array {
        // 参数验证
        if ($amount <= 0) {
            throw new \Exception('充值金额必须大于0');
        }

        if ($amount > self::SINGLE_LIMIT) {
            throw new \Exception('充值金额超过单次限额' . self::SINGLE_LIMIT . '元');
        }

        // 记录操作日志
        Yii::info("抖音平台充值开始 - 从账户: {$fromAccountId} 向账户: {$toAccountId} 充值: {$amount}元", 'tiktok_adapter');

        try {
            // 调用Oceanengine::transferCreate方法
            $oceanengine = $this->getOceanengineInstance();
            $result = $oceanengine::transferCreate(
                $accessToken,
                $organizationId,
                $fromAccountId,
                $toAccountId,
                $amount
            );

            // 检查返回结果
            if (!isset($result['code']) || $result['code'] !== 0) {
                $errorMsg = $this->formatError($result);
                Yii::error("抖音平台充值失败: {$errorMsg}", 'tiktok_adapter');
                throw new \Exception($errorMsg);
            }

            // 记录成功日志
            Yii::info("抖音平台充值成功 - 交易号: " . ($result['data']['transfer_serial'] ?? 'unknown'), 'tiktok_adapter');

            return $result;

        } catch (\Exception $e) {
            Yii::error("抖音平台充值异常: " . $e->getMessage(), 'tiktok_adapter');
            throw $e;
        }
    }

    /**
     * 获取账户余额
     * 
     * 基于Oceanengine::getFund()方法实现
     * 
     * @param string $accessToken 访问令牌
     * @param string $accountId 账户ID
     * @return float 账户余额 (单位：元)
     * @throws \Exception 查询失败时抛出异常
     */
    public function getBalance(string $accessToken, string $accountId): float
    {
        try {
            // 调用Oceanengine::getFund方法
            $oceanengine = $this->getOceanengineInstance();
            $result = $oceanengine::getFund($accessToken, $accountId);

            // 检查返回结果
            if (!isset($result['code']) || $result['code'] !== 0) {
                $errorMsg = $this->formatError($result);
                Yii::error("查询抖音账户余额失败: {$errorMsg}", 'tiktok_adapter');
                throw new \Exception($errorMsg);
            }

            // 从响应中提取余额（抖音接口返回的余额单位是分）
            $balance = $result['data']['balance'] ?? 0;
            $balanceInYuan = $balance / 100; // 转换为元

            Yii::info("查询抖音账户余额成功 - 账户: {$accountId}, 余额: {$balanceInYuan}元", 'tiktok_adapter');

            return (float)$balanceInYuan;

        } catch (\Exception $e) {
            Yii::error("查询抖音账户余额异常: " . $e->getMessage(), 'tiktok_adapter');
            throw $e;
        }
    }

    /**
     * 获取单次充值限额
     */
    public function getSingleLimit(): int
    {
        return self::SINGLE_LIMIT;
    }

    /**
     * 获取小时充值限额
     */
    public function getHourlyLimit(): int
    {
        return self::HOURLY_LIMIT;
    }

    /**
     * 验证平台特定的参数
     * 
     * @param array $params 参数数组
     * @return bool 验证是否通过
     * @throws \Exception 验证失败时抛出异常
     */
    public function validateParams(array $params): bool
    {
        $required = ['access_token', 'organization_id', 'from_account', 'to_account', 'amount'];
        
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '' || $params[$field] === null) {
                throw new \Exception('缺少必要参数: ' . $field);
            }
        }

        // 验证金额
        if (!is_numeric($params['amount']) || $params['amount'] <= 0) {
            throw new \Exception('充值金额必须是大于0的数字');
        }

        return true;
    }

    /**
     * 格式化错误信息
     * 
     * @param array $response 平台原始响应
     * @return string 格式化后的错误信息
     */
    public function formatError(array $response): string
    {
        $code = $response['code'] ?? 'unknown';
        $message = $response['message'] ?? '未知错误';
        $details = $response['details'] ?? '';

        $prefix = strpos($message, '抖音') === false ? '抖音错误' : '';
        $errorMsg = $prefix ? "{$prefix}[{$code}]: {$message}" : "抖音转账失败[{$code}]: {$message}";

        return $errorMsg . ($details ? " - {$details}" : '');
    }

    /**
     * 设置Oceanengine实例（用于测试依赖注入）
     * 
     * @param Oceanengine $instance
     */
    public function setOceanengineInstance(Oceanengine $instance): void
    {
        $this->oceanengineInstance = $instance;
    }

    /**
     * 获取Oceanengine实例
     * 
     * @return Oceanengine
     */
    private function getOceanengineInstance(): Oceanengine
    {
        if ($this->oceanengineInstance !== null) {
            return $this->oceanengineInstance;
        }

        return new Oceanengine();
    }
}