<?php

namespace backendapi\services\promote\transfermoneyv2\platform;

/**
 * 平台适配器接口
 * 
 * 统一不同平台的充值接口调用，提供标准化的平台操作接口
 * 基于现有 Oceanengine 和 Adq 类的接口设计
 */
interface PlatformAdapterInterface
{
    /**
     * 获取平台名称
     * 
     * @return string 平台名称 (如: 'tiktok', 'adq')
     */
    public function getName(): string;

    /**
     * 执行转账充值
     * 
     * @param string $accessToken 访问令牌
     * @param string $organizationId 组织ID (抖音平台需要)
     * @param string $fromAccountId 源账户ID (备用金账户)
     * @param string $toAccountId 目标账户ID (充值目标账户)
     * @param int $amount 充值金额 (单位：元)
     * @return array 统一格式的返回结果
     * @throws \Exception 充值失败时抛出异常
     */
    public function transferMoney(
        string $accessToken,
        string $organizationId,
        string $fromAccountId,
        string $toAccountId,
        int $amount
    ): array;

    /**
     * 获取账户余额
     * 
     * @param string $accessToken 访问令牌
     * @param string $accountId 账户ID
     * @return float 账户余额 (单位：元)
     * @throws \Exception 查询失败时抛出异常
     */
    public function getBalance(string $accessToken, string $accountId): float;

    /**
     * 获取单次充值限额
     * 
     * @return int 单次充值限额 (单位：元)
     */
    public function getSingleLimit(): int;

    /**
     * 获取小时充值限额
     * 
     * @return int 小时充值限额 (单位：元)
     */
    public function getHourlyLimit(): int;

    /**
     * 验证平台特定的参数
     * 
     * @param array $params 参数数组
     * @return bool 验证是否通过
     * @throws \Exception 验证失败时抛出异常
     */
    public function validateParams(array $params): bool;

    /**
     * 格式化错误信息
     * 
     * @param array $response 平台原始响应
     * @return string 格式化后的错误信息
     */
    public function formatError(array $response): string;
}