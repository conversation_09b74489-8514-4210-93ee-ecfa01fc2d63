<?php

/**
 * 平台适配器验证脚本
 * 
 * 用于验证TiktokAdapter和AdqAdapter的基本功能
 * 这是一个安全的验证脚本，不会执行真实的充值操作
 */

require_once __DIR__ . '/../../../../vendor/autoload.php';

use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\platform\TiktokAdapter;
use backendapi\services\promote\transfermoneyv2\platform\AdqAdapter;
use backendapi\services\promote\transfermoneyv2\platform\MockAdapter;

echo "=== 平台适配器验证脚本 ===\n\n";

try {
    // 创建平台工厂
    $factory = new PlatformFactory();
    
    echo "1. 测试平台工厂基本功能\n";
    echo "支持的平台: " . implode(', ', $factory->getSupportedPlatforms()) . "\n";
    
    // 测试各个平台适配器
    $platforms = ['mock', 'tiktok', 'adq'];
    
    foreach ($platforms as $platform) {
        echo "\n2. 测试 {$platform} 平台适配器\n";
        
        // 创建适配器
        $adapter = $factory->create($platform);
        echo "- 平台名称: " . $adapter->getName() . "\n";
        echo "- 单次限额: " . $adapter->getSingleLimit() . "元\n";
        echo "- 小时限额: " . $adapter->getHourlyLimit() . "元\n";
        
        // 测试参数验证
        $testParams = [
            'access_token' => 'test_token_' . $platform,
            'organization_id' => '*********',
            'from_account' => '*********',
            'to_account' => '*********',
            'amount' => 500
        ];
        
        try {
            $isValid = $adapter->validateParams($testParams);
            echo "- 参数验证: " . ($isValid ? "通过" : "失败") . "\n";
        } catch (Exception $e) {
            echo "- 参数验证异常: " . $e->getMessage() . "\n";
        }
        
        // 测试错误格式化
        $errorResponse = [
            'code' => 1001,
            'message' => '测试错误消息',
            'details' => '详细错误信息'
        ];
        
        $formattedError = $adapter->formatError($errorResponse);
        echo "- 错误格式化: " . $formattedError . "\n";
        
        // 仅对Mock适配器进行实际操作测试（安全）
        if ($platform === 'mock') {
            echo "- 执行Mock充值测试...\n";
            
            /** @var MockAdapter $mockAdapter */
            $mockAdapter = $adapter;
            
            // 设置Mock余额
            $mockAdapter->setMockBalance('*********', 2000.0);
            
            try {
                // 测试获取余额
                $balance = $mockAdapter->getBalance('test_token', '*********');
                echo "  * 账户余额: {$balance}元\n";
                
                // 测试转账
                $result = $mockAdapter->transferMoney(
                    'test_token',
                    '*********',
                    '*********',
                    '*********',
                    500
                );
                
                echo "  * 充值结果: " . ($result['code'] === 0 ? "成功" : "失败") . "\n";
                echo "  * 交易ID: " . ($result['data']['transaction_id'] ?? 'N/A') . "\n";
                
                // 再次查询余额
                $newBalance = $mockAdapter->getBalance('test_token', '*********');
                echo "  * 充值后余额: {$newBalance}元\n";
                
            } catch (Exception $e) {
                echo "  * Mock测试异常: " . $e->getMessage() . "\n";
            }
        } else {
            echo "- 跳过真实平台操作测试（安全考虑）\n";
        }
    }
    
    echo "\n3. 测试工厂统计功能\n";
    $stats = $factory->getStatistics();
    echo "- 创建实例数: " . $stats['created_instances'] . "\n";
    echo "- 缓存命中数: " . $stats['cache_hits'] . "\n";
    echo "- 缓存实例数: " . $stats['cached_instances'] . "\n";
    
    echo "\n4. 测试批量创建\n";
    $adapters = $factory->batchCreate(['mock', 'tiktok', 'adq']);
    echo "- 批量创建成功，共创建 " . count($adapters) . " 个适配器\n";
    
    echo "\n5. 测试适配器类型验证\n";
    foreach ($adapters as $platform => $adapter) {
        $expectedClass = '';
        switch ($platform) {
            case 'mock':
                $expectedClass = MockAdapter::class;
                break;
            case 'tiktok':
                $expectedClass = TiktokAdapter::class;
                break;
            case 'adq':
                $expectedClass = AdqAdapter::class;
                break;
        }
        
        $isCorrectType = $adapter instanceof $expectedClass;
        echo "- {$platform}: " . ($isCorrectType ? "类型正确" : "类型错误") . "\n";
    }
    
    echo "\n=== 验证完成 ===\n";
    echo "所有基本功能验证通过！\n";
    
} catch (Exception $e) {
    echo "验证过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
}