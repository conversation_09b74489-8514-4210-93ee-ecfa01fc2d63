<?php

namespace backendapi\services\promote\transfermoneyv2\platform;

use Yii;

/**
 * Mock平台适配器
 * 
 * 用于测试环境的安全Mock适配器，模拟真实平台接口但绝对不会触发真实充值操作
 * 提供完整的接口模拟、日志记录和各种场景测试支持
 */
class MockAdapter implements PlatformAdapterInterface
{
    /**
     * @var array Mock账户余额配置
     */
    private $mockBalances = [];

    /**
     * @var array Mock错误配置
     */
    private $mockErrors = [];

    /**
     * @var array Mock超时配置
     */
    private $mockTimeouts = [];

    /**
     * @var array 调用日志
     */
    private $callLogs = [];

    /**
     * @var int 默认Mock余额
     */
    private $defaultBalance = 10000;

    /**
     * @var int 单次充值限额
     */
    private $singleLimit = 10000;

    /**
     * @var int 小时充值限额
     */
    private $hourlyLimit = 50000;

    /**
     * 获取平台名称
     */
    public function getName(): string
    {
        return 'mock';
    }

    /**
     * 执行转账充值 - Mock实现，绝对安全
     */
    public function transferMoney(
        string $accessToken,
        string $organizationId,
        string $fromAccountId,
        string $toAccountId,
        int $amount
    ): array {
        // 记录调用日志
        $this->logCall('transferMoney', [
            'accessToken' => $this->maskToken($accessToken),
            'organizationId' => $organizationId,
            'fromAccountId' => $fromAccountId,
            'toAccountId' => $toAccountId,
            'amount' => $amount
        ]);

        // 参数验证
        if ($amount <= 0) {
            throw new \Exception('Mock模拟：充值金额必须大于0');
        }

        // 检查是否设置了Mock错误
        if (isset($this->mockErrors[$fromAccountId])) {
            throw new \Exception($this->mockErrors[$fromAccountId]);
        }

        // 检查是否设置了Mock超时
        if (isset($this->mockTimeouts[$fromAccountId])) {
            throw new \Exception('Mock模拟：网络请求超时');
        }

        // 检查余额是否足够
        $balance = $this->getMockBalance($fromAccountId);
        if ($amount > $balance) {
            throw new \Exception('Mock模拟：账户余额不足');
        }

        // 模拟成功响应
        $transactionId = 'mock_transaction_' . time() . '_' . rand(1000, 9999);
        
        $result = [
            'code' => 0,
            'message' => 'Mock充值成功',
            'data' => [
                'transaction_id' => $transactionId,
                'target_account_id' => $toAccountId,
                'amount' => $amount,
                'status' => 'success',
                'timestamp' => time()
            ]
        ];

        // 更新Mock余额
        $this->updateMockBalance($fromAccountId, $balance - $amount);

        // 记录成功结果
        $this->logResult('success', $result);

        // 记录安全日志
        Yii::info("Mock充值操作 - 从账户: {$fromAccountId} 向账户: {$toAccountId} 充值: {$amount}元 (Mock模拟，未执行真实操作)", 'mock_adapter');

        return $result;
    }

    /**
     * 获取账户余额 - Mock实现
     */
    public function getBalance(string $accessToken, string $accountId): float
    {
        // 记录调用日志
        $this->logCall('getBalance', [
            'accessToken' => $this->maskToken($accessToken),
            'accountId' => $accountId
        ]);

        // 检查是否设置了Mock错误
        if (isset($this->mockErrors[$accountId])) {
            throw new \Exception($this->mockErrors[$accountId]);
        }

        $balance = $this->getMockBalance($accountId);

        // 记录成功结果
        $this->logResult('success', ['balance' => $balance]);

        return (float)$balance;
    }

    /**
     * 获取单次充值限额
     */
    public function getSingleLimit(): int
    {
        return $this->singleLimit;
    }

    /**
     * 获取小时充值限额
     */
    public function getHourlyLimit(): int
    {
        return $this->hourlyLimit;
    }

    /**
     * 验证平台特定的参数
     */
    public function validateParams(array $params): bool
    {
        $required = ['access_token', 'from_account', 'to_account', 'amount'];
        
        foreach ($required as $field) {
            if (!isset($params[$field]) || empty($params[$field])) {
                throw new \Exception('Mock模拟：缺少必要参数: ' . $field);
            }
        }

        return true;
    }

    /**
     * 格式化错误信息
     */
    public function formatError(array $response): string
    {
        $code = $response['code'] ?? 'unknown';
        $message = $response['message'] ?? '未知错误';
        $details = $response['details'] ?? '';

        return "Mock错误[{$code}]: {$message}" . ($details ? " - {$details}" : '');
    }

    /**
     * 设置Mock账户余额
     */
    public function setMockBalance(string $accountId, float $balance): void
    {
        $this->mockBalances[$accountId] = $balance;
    }

    /**
     * 设置Mock错误
     */
    public function setMockError(string $accountId, string $errorMessage): void
    {
        $this->mockErrors[$accountId] = $errorMessage;
    }

    /**
     * 设置Mock超时
     */
    public function setMockTimeout(string $accountId): void
    {
        $this->mockTimeouts[$accountId] = true;
    }

    /**
     * 获取调用日志
     */
    public function getCallLogs(): array
    {
        return $this->callLogs;
    }

    /**
     * 重置Mock状态
     */
    public function resetMockState(): void
    {
        $this->mockBalances = [];
        $this->mockErrors = [];
        $this->mockTimeouts = [];
        $this->callLogs = [];
    }

    /**
     * 获取Mock余额
     */
    private function getMockBalance(string $accountId): float
    {
        return $this->mockBalances[$accountId] ?? $this->defaultBalance;
    }

    /**
     * 更新Mock余额
     */
    private function updateMockBalance(string $accountId, float $newBalance): void
    {
        $this->mockBalances[$accountId] = $newBalance;
    }

    /**
     * 记录调用日志
     */
    private function logCall(string $method, array $params): void
    {
        $this->callLogs[] = [
            'timestamp' => time(),
            'platform' => 'mock',
            'method' => $method,
            'params' => $params
        ];
    }

    /**
     * 记录结果日志
     */
    private function logResult(string $status, array $data = []): void
    {
        if (!empty($this->callLogs)) {
            $lastIndex = count($this->callLogs) - 1;
            $this->callLogs[$lastIndex]['result'] = [
                'status' => $status,
                'data' => $data
            ];
        }
    }

    /**
     * 掩码访问令牌
     */
    private function maskToken(string $token): string
    {
        if (strlen($token) <= 8) {
            return str_repeat('*', strlen($token));
        }
        
        return substr($token, 0, 4) . str_repeat('*', strlen($token) - 8) . substr($token, -4);
    }
}