<?php

namespace backendapi\services\promote\transfermoneyv2\platform;

use Yii;

/**
 * 平台工厂类
 * 
 * 负责创建和管理平台适配器实例
 * 支持平台注册、单例模式、批量创建等功能
 */
class PlatformFactory
{
    /**
     * @var array 已注册的平台适配器配置
     */
    private static $registeredPlatforms = [
        'mock' => [
            'class' => MockAdapter::class,
            'name' => 'Mock平台适配器',
            'description' => '用于测试的Mock适配器，绝对安全'
        ],
        'tiktok' => [
            'class' => TiktokAdapter::class,
            'name' => '抖音平台适配器',
            'description' => '基于Oceanengine类的抖音平台充值适配器'
        ],
        'adq' => [
            'class' => AdqAdapter::class,
            'name' => 'ADQ平台适配器',
            'description' => '基于Adq类的ADQ平台充值适配器'
        ]
    ];

    /**
     * @var array 已创建的适配器实例缓存
     */
    private $instances = [];

    /**
     * @var array 统计信息
     */
    private $statistics = [
        'created_instances' => 0,
        'cache_hits' => 0,
        'creation_history' => []
    ];

    /**
     * 创建平台适配器实例
     * 
     * @param string $platform 平台类型
     * @param bool $forceNew 是否强制创建新实例
     * @return PlatformAdapterInterface
     * @throws \Exception
     */
    public function create(string $platform, bool $forceNew = false): PlatformAdapterInterface
    {
        if (!$this->isSupported($platform)) {
            throw new \Exception("不支持的平台类型: {$platform}");
        }

        // 如果不强制创建新实例且已存在缓存实例，直接返回
        if (!$forceNew && isset($this->instances[$platform])) {
            $this->statistics['cache_hits']++;
            return $this->instances[$platform];
        }

        $config = self::$registeredPlatforms[$platform];
        $className = $config['class'];

        // 验证类是否存在
        if (!class_exists($className)) {
            throw new \Exception("适配器类不存在: {$className}");
        }

        // 创建实例
        $instance = new $className();

        // 验证实例是否实现了正确的接口
        if (!$instance instanceof PlatformAdapterInterface) {
            throw new \Exception("适配器类必须实现 PlatformAdapterInterface 接口: {$className}");
        }

        // 缓存实例（如果不是强制创建新实例）
        if (!$forceNew) {
            $this->instances[$platform] = $instance;
        }

        // 更新统计信息
        $this->statistics['created_instances']++;
        $this->statistics['creation_history'][] = [
            'platform' => $platform,
            'class' => $className,
            'timestamp' => time(),
            'force_new' => $forceNew
        ];

        // 记录日志
        Yii::info("创建平台适配器: {$platform} ({$className})", 'platform_factory');

        return $instance;
    }

    /**
     * 批量创建平台适配器
     * 
     * @param array $platforms 平台类型数组
     * @param bool $forceNew 是否强制创建新实例
     * @return array 平台适配器实例数组
     * @throws \Exception
     */
    public function batchCreate(array $platforms, bool $forceNew = false): array
    {
        $adapters = [];
        
        foreach ($platforms as $platform) {
            $adapters[$platform] = $this->create($platform, $forceNew);
        }

        return $adapters;
    }

    /**
     * 注册新的平台适配器
     * 
     * @param string $platform 平台类型
     * @param string $className 适配器类名
     * @param string $name 平台名称
     * @param string $description 平台描述
     * @throws \Exception
     */
    public function register(
        string $platform, 
        string $className, 
        string $name = '', 
        string $description = ''
    ): void {
        // 验证适配器类
        if (!$this->validateAdapterClass($className)) {
            throw new \Exception("适配器类必须实现 PlatformAdapterInterface 接口: {$className}");
        }

        self::$registeredPlatforms[$platform] = [
            'class' => $className,
            'name' => $name ?: $platform,
            'description' => $description
        ];

        Yii::info("注册平台适配器: {$platform} ({$className})", 'platform_factory');
    }

    /**
     * 检查平台是否支持
     * 
     * @param string $platform 平台类型
     * @return bool
     */
    public function isSupported(string $platform): bool
    {
        return isset(self::$registeredPlatforms[$platform]);
    }

    /**
     * 获取支持的平台列表
     * 
     * @return array
     */
    public function getSupportedPlatforms(): array
    {
        return array_keys(self::$registeredPlatforms);
    }

    /**
     * 获取平台适配器配置
     * 
     * @param string $platform 平台类型
     * @return array
     * @throws \Exception
     */
    public function getAdapterConfig(string $platform): array
    {
        if (!$this->isSupported($platform)) {
            throw new \Exception("不支持的平台类型: {$platform}");
        }

        return self::$registeredPlatforms[$platform];
    }

    /**
     * 验证适配器类是否实现了正确的接口
     * 
     * @param string $className 类名
     * @return bool
     */
    public function validateAdapterClass(string $className): bool
    {
        if (!class_exists($className)) {
            return false;
        }

        $reflection = new \ReflectionClass($className);
        return $reflection->implementsInterface(PlatformAdapterInterface::class);
    }

    /**
     * 重置工厂状态
     */
    public function reset(): void
    {
        $this->instances = [];
        $this->statistics = [
            'created_instances' => 0,
            'cache_hits' => 0,
            'creation_history' => []
        ];

        Yii::info("重置平台工厂状态", 'platform_factory');
    }

    /**
     * 获取统计信息
     * 
     * @return array
     */
    public function getStatistics(): array
    {
        return array_merge($this->statistics, [
            'total_platforms' => count(self::$registeredPlatforms),
            'cached_instances' => count($this->instances)
        ]);
    }

    /**
     * 获取调试信息
     * 
     * @return array
     */
    public function getDebugInfo(): array
    {
        return [
            'registered_platforms' => self::$registeredPlatforms,
            'cached_instances' => array_keys($this->instances),
            'creation_history' => $this->statistics['creation_history'],
            'statistics' => $this->getStatistics()
        ];
    }

    /**
     * 清除特定平台的缓存实例
     * 
     * @param string $platform 平台类型
     */
    public function clearCache(string $platform = null): void
    {
        if ($platform === null) {
            $this->instances = [];
            Yii::info("清除所有平台适配器缓存", 'platform_factory');
        } elseif (isset($this->instances[$platform])) {
            unset($this->instances[$platform]);
            Yii::info("清除平台适配器缓存: {$platform}", 'platform_factory');
        }
    }

    /**
     * 获取已缓存的实例
     * 
     * @param string $platform 平台类型
     * @return PlatformAdapterInterface|null
     */
    public function getCachedInstance(string $platform): ?PlatformAdapterInterface
    {
        return $this->instances[$platform] ?? null;
    }

    /**
     * 检查实例是否已缓存
     * 
     * @param string $platform 平台类型
     * @return bool
     */
    public function isCached(string $platform): bool
    {
        return isset($this->instances[$platform]);
    }
}