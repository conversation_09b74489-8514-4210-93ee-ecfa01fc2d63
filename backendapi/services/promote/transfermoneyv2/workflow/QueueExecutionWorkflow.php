<?php

namespace backendapi\services\promote\transfermoneyv2\workflow;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\platform\PlatformFactory;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use common\enums\reportEnum;
use Exception;
use Yii;

/**
 * 消费队列执行充值流程
 * 
 * 基于现有 TransferMoneyBatchService::execute() 方法逻辑
 * 实现队列任务的消费和执行，包括批量限制检查、频率控制、
 * 目标账户设置、金额限制检查、执行充值、成功处理等步骤
 */
class QueueExecutionWorkflow
{
    /**
     * @var TransferMoneyServiceV2 业务服务
     */
    private $service;

    /**
     * @var PlatformFactory 平台工厂
     */
    private $platformFactory;

    /**
     * @var TransferCacheManager 缓存管理器
     */
    private $cacheManager;

    /**
     * @var array 执行配置
     */
    private $config;

    /**
     * @var array 执行统计
     */
    private $executionStats = [
        'total_accounts' => 0,
        'success_accounts' => 0,
        'failed_accounts' => 0,
        'start_time' => 0,
        'end_time' => 0,
        'execution_details' => []
    ];

    /**
     * 构造函数
     * 
     * @param TransferMoneyServiceV2|null $service 业务服务
     * @param array $config 执行配置
     */
    public function __construct(TransferMoneyServiceV2 $service = null, array $config = [])
    {
        $this->service = $service ?: new TransferMoneyServiceV2();
        $this->platformFactory = $this->service->getPlatformFactory();
        $this->cacheManager = $this->service->getCacheManager();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * 执行队列充值任务 - 基于现有 execute() 方法逻辑
     * 
     * @param array $data 队列数据
     * @return array 执行结果
     * @throws Exception
     */
    public function execute(array $data): array
    {
        $this->initializeExecution($data);

        try {
            // 1. 批量限制检查
            $this->validateBatchLimit($data);

            // 2. 设置执行参数
            $this->service->setAmount($data['amount']);
            $this->service->setUserName($data['user_name'] ?? '系统自动');

            // 3. 按平台分组处理账户
            $groupedAccounts = $this->groupAccountsByPlatform($data['target_advertiser_ids']);

            // 4. 并行处理各平台账户
            $results = $this->processAccountGroups($groupedAccounts, $data['amount']);

            // 5. 汇总执行结果
            $this->finalizeExecution();

            return $this->formatExecutionResults($results);

        } catch (Exception $e) {
            $this->logExecutionError($e, $data);
            throw $e;
        }
    }

    /**
     * 初始化执行环境
     * 
     * @param array $data 队列数据
     */
    private function initializeExecution(array $data): void
    {
        $this->executionStats = [
            'total_accounts' => count($data['target_advertiser_ids']),
            'success_accounts' => 0,
            'failed_accounts' => 0,
            'start_time' => microtime(true),
            'end_time' => 0,
            'execution_details' => []
        ];

        Yii::info('开始执行队列充值任务', 'queue_execution_workflow');
    }

    /**
     * 验证批量限制
     * 
     * @param array $data 队列数据
     * @throws Exception
     */
    private function validateBatchLimit(array $data): void
    {
        $total = count($data['target_advertiser_ids']);
        
        if ($total > $this->config['max_batch_size']) {
            throw new Exception("充值一次最多只能{$this->config['max_batch_size']}个户");
        }
    }

    /**
     * 按平台分组账户
     * 
     * @param array $targetAdvertiserIds 目标账户ID列表
     * @return array 按平台分组的账户
     */
    private function groupAccountsByPlatform(array $targetAdvertiserIds): array
    {
        $grouped = [];
        
        foreach ($targetAdvertiserIds as $advertiserId) {
            try {
                // 获取账户平台信息
                $platform = $this->getAccountPlatform($advertiserId);
                
                if (!isset($grouped[$platform])) {
                    $grouped[$platform] = [];
                }
                
                $grouped[$platform][] = $advertiserId;
                
            } catch (Exception $e) {
                // 记录无法分组的账户
                $this->executionStats['execution_details'][] = [
                    'account_id' => $advertiserId,
                    'status' => 'failed',
                    'error' => '无法获取账户平台信息: ' . $e->getMessage(),
                    'platform' => 'unknown'
                ];
                $this->executionStats['failed_accounts']++;
            }
        }

        return $grouped;
    }

    /**
     * 获取账户平台信息
     * 
     * @param string $advertiserId 账户ID
     * @return string 平台类型
     * @throws Exception
     */
    private function getAccountPlatform(string $advertiserId): string
    {
        // 这里应该查询数据库获取账户平台信息
        // 暂时返回默认平台，实际使用时需要实现数据库查询
        return reportEnum::ADQ;
    }

    /**
     * 处理账户分组
     * 
     * @param array $groupedAccounts 分组后的账户
     * @param float $amount 充值金额
     * @return array 处理结果
     */
    private function processAccountGroups(array $groupedAccounts, float $amount): array
    {
        $allResults = [];
        
        foreach ($groupedAccounts as $platform => $accountIds) {
            try {
                // 设置平台
                $this->service->setPlatform($platform);
                
                // 处理该平台的所有账户
                $platformResults = $this->processAccountsForPlatform($accountIds, $amount, $platform);
                
                // 合并结果
                $allResults = $this->mergeResults($allResults, $platformResults);
                
            } catch (Exception $e) {
                Yii::error("处理平台 {$platform} 账户时发生错误: " . $e->getMessage(), 'queue_execution_workflow');
                
                // 将该平台所有账户标记为失败
                foreach ($accountIds as $accountId) {
                    $this->recordAccountFailure($accountId, $e->getMessage(), $platform);
                }
            }
        }

        return $allResults;
    }

    /**
     * 处理特定平台的账户
     * 
     * @param array $accountIds 账户ID列表
     * @param float $amount 充值金额
     * @param string $platform 平台类型
     * @return array 处理结果
     */
    private function processAccountsForPlatform(array $accountIds, float $amount, string $platform): array
    {
        $results = [];
        $processedCount = 0;

        foreach ($accountIds as $targetAdvertiserId) {
            try {
                // 初始化服务状态
                $this->service->initialize();
                
                // 频率控制 - 每充值10个户睡眠500毫秒
                if ($processedCount > 0 && $processedCount % $this->config['frequency_control_interval'] === 0) {
                    usleep($this->config['frequency_control_delay']);
                }

                // 执行单个账户充值
                $accountResult = $this->processSingleAccount($targetAdvertiserId, $amount, $platform);
                
                // 记录结果
                $code = $this->service->getCode();
                if (!isset($results[$code])) {
                    $results[$code] = [];
                }
                $results[$code][] = $accountResult;

                // 更新统计
                if ($code == 200 || $code == 201) {
                    $this->executionStats['success_accounts']++;
                } else {
                    $this->executionStats['failed_accounts']++;
                }

                $processedCount++;

            } catch (Exception $e) {
                $this->recordAccountFailure($targetAdvertiserId, $e->getMessage(), $platform);
            }
        }

        return $results;
    }

    /**
     * 处理单个账户充值
     * 
     * @param string $targetAdvertiserId 目标账户ID
     * @param float $amount 充值金额
     * @param string $platform 平台类型
     * @return array 处理结果
     * @throws Exception
     */
    private function processSingleAccount(string $targetAdvertiserId, float $amount, string $platform): array
    {
        $startTime = microtime(true);
        
        try {
            // 1. 目标账户设置
            $this->service->setTargetAdvertiserIds($targetAdvertiserId);
            
            // 2. 金额限制检查
            $this->service->amountLimit();
            
            // 3. 执行充值
            $this->service->transferMoney();
            
            // 4. 成功处理
            $this->service->success();
            
            $duration = microtime(true) - $startTime;
            $msg = '充值成功';

            // 记录执行详情
            $this->executionStats['execution_details'][] = [
                'account_id' => $targetAdvertiserId,
                'account_name' => $this->service->getTargetAdvertiserId(),
                'status' => 'success',
                'amount' => $amount,
                'platform' => $platform,
                'duration' => $duration,
                'remaining_balance' => $this->service->getInsufficientBalance()
            ];

        } catch (Exception $e) {
            $duration = microtime(true) - $startTime;
            $msg = $e->getMessage();
            
            // 记录失败详情
            $this->executionStats['execution_details'][] = [
                'account_id' => $targetAdvertiserId,
                'status' => 'failed',
                'error' => $msg,
                'platform' => $platform,
                'duration' => $duration
            ];
            
            throw $e;
        }

        return [
            'msg' => $msg,
            'target_advertiser_id' => $this->service->getTargetAdvertiserId(),
            'target_advertiser_name' => $this->service->getTargetAdvertiserId(),
            'main_body' => $this->service->getMainBody(),
            'advertiser_id' => $this->service->getAdvertiserId(),
            'insufficientNalance' => $this->service->getInsufficientBalance()
        ];
    }

    /**
     * 记录账户处理失败
     * 
     * @param string $accountId 账户ID
     * @param string $error 错误信息
     * @param string $platform 平台类型
     */
    private function recordAccountFailure(string $accountId, string $error, string $platform): void
    {
        $this->executionStats['execution_details'][] = [
            'account_id' => $accountId,
            'status' => 'failed',
            'error' => $error,
            'platform' => $platform
        ];
        
        $this->executionStats['failed_accounts']++;
    }

    /**
     * 合并处理结果
     * 
     * @param array $results1 结果1
     * @param array $results2 结果2
     * @return array 合并后的结果
     */
    private function mergeResults(array $results1, array $results2): array
    {
        foreach ($results2 as $code => $items) {
            if (!isset($results1[$code])) {
                $results1[$code] = [];
            }
            $results1[$code] = array_merge($results1[$code], $items);
        }
        
        return $results1;
    }

    /**
     * 完成执行统计
     */
    private function finalizeExecution(): void
    {
        $this->executionStats['end_time'] = microtime(true);
        $this->executionStats['total_duration'] = $this->executionStats['end_time'] - $this->executionStats['start_time'];
        
        Yii::info('队列充值任务执行完成', 'queue_execution_workflow');
    }

    /**
     * 格式化执行结果
     * 
     * @param array $results 原始结果
     * @return array 格式化后的结果
     */
    private function formatExecutionResults(array $results): array
    {
        return [
            'execution_results' => $results,
            'statistics' => $this->executionStats,
            'formatted_results' => $this->service->resRealData($results)
        ];
    }

    /**
     * 记录执行错误
     * 
     * @param Exception $e 异常对象
     * @param array $data 队列数据
     */
    private function logExecutionError(Exception $e, array $data): void
    {
        $errorData = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'queue_data' => $data,
            'execution_stats' => $this->executionStats
        ];

        Yii::error($errorData, 'queue_execution_workflow');
    }

    /**
     * 获取执行统计信息
     * 
     * @return array 统计信息
     */
    public function getExecutionStats(): array
    {
        return $this->executionStats;
    }

    /**
     * 获取成功率
     * 
     * @return float 成功率百分比
     */
    public function getSuccessRate(): float
    {
        if ($this->executionStats['total_accounts'] === 0) {
            return 0.0;
        }
        
        return ($this->executionStats['success_accounts'] / $this->executionStats['total_accounts']) * 100;
    }

    /**
     * 获取平台分布统计
     * 
     * @return array 平台分布
     */
    public function getPlatformDistribution(): array
    {
        $distribution = [];
        
        foreach ($this->executionStats['execution_details'] as $detail) {
            $platform = $detail['platform'] ?? 'unknown';
            
            if (!isset($distribution[$platform])) {
                $distribution[$platform] = [
                    'total' => 0,
                    'success' => 0,
                    'failed' => 0
                ];
            }
            
            $distribution[$platform]['total']++;
            
            if ($detail['status'] === 'success') {
                $distribution[$platform]['success']++;
            } else {
                $distribution[$platform]['failed']++;
            }
        }
        
        return $distribution;
    }

    /**
     * 获取默认配置
     * 
     * @return array 默认配置
     */
    private function getDefaultConfig(): array
    {
        return [
            'max_batch_size' => 50,
            'frequency_control_interval' => 10,
            'frequency_control_delay' => 500000, // 500毫秒
            'enable_parallel_processing' => true,
            'enable_detailed_logging' => true,
            'timeout_per_account' => 30
        ];
    }

    /**
     * 重置工作流状态
     */
    public function reset(): void
    {
        $this->executionStats = [
            'total_accounts' => 0,
            'success_accounts' => 0,
            'failed_accounts' => 0,
            'start_time' => 0,
            'end_time' => 0,
            'execution_details' => []
        ];
        
        $this->service->initialize();
    }

    /**
     * 创建工作流实例
     * 
     * @param array $config 配置参数
     * @return QueueExecutionWorkflow
     */
    public static function create(array $config = []): QueueExecutionWorkflow
    {
        return new self(null, $config);
    }

    /**
     * 创建带有自定义服务的工作流实例
     * 
     * @param TransferMoneyServiceV2 $service 自定义服务
     * @param array $config 配置参数
     * @return QueueExecutionWorkflow
     */
    public static function createWithService(TransferMoneyServiceV2 $service, array $config = []): QueueExecutionWorkflow
    {
        return new self($service, $config);
    }
}