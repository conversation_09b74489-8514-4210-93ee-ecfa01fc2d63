<?php

namespace backendapi\services\promote\transfermoneyv2\workflow;

use backendapi\services\promote\transfermoneyv2\TransferMoneyServiceV2;
use backendapi\services\promote\transfermoneyv2\cache\TransferCacheManager;
use common\models\Config;
use common\queues\TransferMoneyJob;
use common\helpers\DateHelper;
use services\common\FeishuExamineService;
use Exception;
use Yii;

/**
 * 加粉后账户自动充值流程
 * 
 * 基于现有 CusCustomerUser::afterSave() 和 TransferMoneyJob::addFansJob() 方法逻辑
 * 实现加粉后的自动充值处理，包括充值频次检查、配置验证、任务创建等
 */
class AddFansRechargeWorkflow
{
    /**
     * @var TransferMoneyServiceV2 业务服务
     */
    private $service;

    /**
     * @var TransferCacheManager 缓存管理器
     */
    private $cacheManager;

    /**
     * @var array 工作流配置
     */
    private $config;

    /**
     * @var array 执行日志
     */
    private $executionLog = [];

    /**
     * 默认充值金额
     */
    const DEFAULT_RECHARGE_AMOUNT = 50;

    /**
     * 最大充值次数限制（5分钟内）
     */
    const MAX_RECHARGE_COUNT = 5;

    /**
     * 构造函数
     * 
     * @param TransferMoneyServiceV2|null $service 业务服务
     * @param array $config 工作流配置
     */
    public function __construct(TransferMoneyServiceV2 $service = null, array $config = [])
    {
        $this->service = $service ?: new TransferMoneyServiceV2();
        $this->cacheManager = $this->service->getCacheManager();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * 执行加粉后自动充值 - 基于现有 addFansJob() 方法逻辑
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param array $context 上下文信息（如加粉时间、用户信息等）
     * @return array 执行结果
     */
    public function execute(string $subAdvertiserId, array $context = []): array
    {
        $this->initializeExecution($subAdvertiserId, $context);

        try {
            // 1. 基础验证
            $this->validateBasicConditions($subAdvertiserId);

            // 2. 配置检查
            $this->validateConfiguration($subAdvertiserId);

            // 3. 时间条件检查
            $this->validateTimeConditions($context);

            // 4. 充值频次检查
            $this->validateRechargeFrequency($subAdvertiserId);

            // 5. 创建充值任务
            $result = $this->createRechargeTask($subAdvertiserId, $context);

            // 6. 记录成功日志
            $this->logSuccess($subAdvertiserId, $result);

            return [
                'success' => true,
                'result' => $result,
                'sub_advertiser_id' => $subAdvertiserId,
                'execution_log' => $this->executionLog
            ];

        } catch (Exception $e) {
            $this->logError($subAdvertiserId, $e);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'sub_advertiser_id' => $subAdvertiserId,
                'execution_log' => $this->executionLog
            ];
        }
    }

    /**
     * 批量处理加粉充值
     * 
     * @param array $subAdvertiserIds 子账户ID列表
     * @param array $context 上下文信息
     * @return array 批量处理结果
     */
    public function executeBatch(array $subAdvertiserIds, array $context = []): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($subAdvertiserIds as $subAdvertiserId) {
            try {
                $result = $this->execute($subAdvertiserId, $context);
                $results[$subAdvertiserId] = $result;
                
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
                
                // 添加延迟避免频繁操作
                if ($this->config['enable_batch_delay']) {
                    usleep($this->config['batch_delay_microseconds']);
                }
                
            } catch (Exception $e) {
                $results[$subAdvertiserId] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'sub_advertiser_id' => $subAdvertiserId
                ];
                $failureCount++;
            }
        }

        return [
            'batch_results' => $results,
            'summary' => [
                'total' => count($subAdvertiserIds),
                'success' => $successCount,
                'failure' => $failureCount,
                'success_rate' => count($subAdvertiserIds) > 0 ? ($successCount / count($subAdvertiserIds)) * 100 : 0
            ]
        ];
    }

    /**
     * 初始化执行环境
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param array $context 上下文信息
     */
    private function initializeExecution(string $subAdvertiserId, array $context): void
    {
        $this->executionLog = [
            'start_time' => time(),
            'sub_advertiser_id' => $subAdvertiserId,
            'context' => $context,
            'steps' => []
        ];

        $this->addLogStep('initialize', '开始执行加粉后自动充值流程');
    }

    /**
     * 验证基础条件
     * 
     * @param string $subAdvertiserId 子账户ID
     * @throws Exception
     */
    private function validateBasicConditions(string $subAdvertiserId): void
    {
        if (empty($subAdvertiserId)) {
            throw new Exception('子账户ID不能为空');
        }

        $this->addLogStep('validate_basic', '基础条件验证通过');
    }

    /**
     * 验证配置条件 - 基于现有 addFansJob() 方法逻辑
     * 
     * @param string $subAdvertiserId 子账户ID
     * @throws Exception
     */
    private function validateConfiguration(string $subAdvertiserId): void
    {
        // 获取充值账户配置
        $transferAccount = Config::getByName('transferAccount');
        if (empty($transferAccount)) {
            throw new Exception('充值账户配置不存在');
        }

        $transferAccountList = explode("\n", $transferAccount);
        if (!in_array($subAdvertiserId, $transferAccountList)) {
            throw new Exception('账户不在充值配置列表中');
        }

        $this->addLogStep('validate_config', '配置验证通过，账户在充值列表中');
    }

    /**
     * 验证时间条件 - 基于现有 afterSave() 方法逻辑
     * 
     * @param array $context 上下文信息
     * @throws Exception
     */
    private function validateTimeConditions(array $context): void
    {
        if (!isset($context['add_time'])) {
            // 如果没有提供加粉时间，使用当前时间
            $context['add_time'] = time();
        }

        $addTime = $context['add_time'];
        $addDate = DateHelper::toDate($addTime, 'Y-m-d');
        $currentDate = date('Y-m-d');

        // 检查是否是当天加粉
        if ($addDate < $currentDate) {
            throw new Exception('只处理当天加粉的账户');
        }

        // 可选：检查加粉时间是否在指定时间段内（如凌晨0-8点）
        if ($this->config['enable_time_range_check']) {
            $addHour = (int)DateHelper::toDate($addTime, 'H');
            if ($addHour >= $this->config['time_range_end']) {
                throw new Exception('加粉时间超出自动充值时间范围');
            }
        }

        $this->addLogStep('validate_time', '时间条件验证通过');
    }

    /**
     * 验证充值频次 - 基于现有 checkTransferMoneyCount() 方法逻辑
     * 
     * @param string $subAdvertiserId 子账户ID
     * @throws Exception
     */
    private function validateRechargeFrequency(string $subAdvertiserId): void
    {
        $today = date('Y-m-d');
        
        // 检查当天是否已被限制
        if ($this->cacheManager->isAddFansRestricted($today, $subAdvertiserId)) {
            throw new Exception('账户今日充值已被限制');
        }

        // 检查5分钟内充值次数
        $count = $this->cacheManager->getAddFansTransferCount($subAdvertiserId);
        if ($count >= self::MAX_RECHARGE_COUNT) {
            // 添加到当天限制列表
            $this->cacheManager->addAddFansRestrictedAccount($today, $subAdvertiserId);
            
            // 发送通知
            $this->sendFrequencyLimitNotification($subAdvertiserId, $count);
            
            throw new Exception("账户在5分钟内充值超过{$count}次，已被限制充值");
        }

        // 增加计数
        $this->cacheManager->incrementAddFansTransferCount($subAdvertiserId);
        
        $this->addLogStep('validate_frequency', "充值频次验证通过，当前计数: {$count}");
    }

    /**
     * 创建充值任务
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param array $context 上下文信息
     * @return bool 是否创建成功
     */
    private function createRechargeTask(string $subAdvertiserId, array $context): bool
    {
        $transferData = [
            'target_advertiser_ids' => [$subAdvertiserId],
            'amount' => $this->config['recharge_amount'],
            'user_name' => $this->config['system_user_name'],
            'execute_time' => time(),
            'isTimeRecharge' => false
        ];

        // 创建队列任务
        $job = new TransferMoneyJob([
            'data' => $transferData,
            'isSendMessage' => $this->config['enable_notification']
        ]);

        $queue = Yii::$app->que;
        
        // 检查是否已存在相同任务
        if ($queue->has($job)) {
            $this->addLogStep('create_task', '任务已存在，跳过创建');
            return true;
        }

        // 添加任务到队列
        $queue->push($job);
        
        $this->addLogStep('create_task', '充值任务创建成功');
        
        return true;
    }

    /**
     * 发送频次限制通知
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param int $count 充值次数
     */
    private function sendFrequencyLimitNotification(string $subAdvertiserId, int $count): void
    {
        if (!$this->config['enable_notification']) {
            return;
        }

        try {
            $group = FeishuExamineService::arrGroup('GGGLGTQ');
            $error = '账户ID：' . $subAdvertiserId . PHP_EOL;
            $error .= "加粉异常,在5分钟内充值超过{$count}次，已被限制充值";
            
            Yii::$app->feishuNotice->text($error, $group['chat_id']);
            
            $this->addLogStep('notification', '频次限制通知已发送');
            
        } catch (Exception $e) {
            $this->addLogStep('notification_error', '发送通知失败: ' . $e->getMessage());
        }
    }

    /**
     * 记录成功日志
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param mixed $result 执行结果
     */
    private function logSuccess(string $subAdvertiserId, $result): void
    {
        $this->addLogStep('success', '加粉后自动充值流程执行成功');
        
        Yii::info([
            'message' => '加粉后自动充值成功',
            'sub_advertiser_id' => $subAdvertiserId,
            'result' => $result,
            'execution_log' => $this->executionLog
        ], 'add_fans_recharge_workflow');
    }

    /**
     * 记录错误日志
     * 
     * @param string $subAdvertiserId 子账户ID
     * @param Exception $e 异常对象
     */
    private function logError(string $subAdvertiserId, Exception $e): void
    {
        $this->addLogStep('error', '执行失败: ' . $e->getMessage());
        
        Yii::error([
            'message' => '加粉后自动充值失败',
            'sub_advertiser_id' => $subAdvertiserId,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'execution_log' => $this->executionLog
        ], 'add_fans_recharge_workflow');
    }

    /**
     * 添加日志步骤
     * 
     * @param string $step 步骤名称
     * @param string $message 日志消息
     */
    private function addLogStep(string $step, string $message): void
    {
        $this->executionLog['steps'][] = [
            'step' => $step,
            'message' => $message,
            'timestamp' => time(),
            'microtime' => microtime(true)
        ];
    }

    /**
     * 获取执行统计信息
     * 
     * @return array 统计信息
     */
    public function getExecutionStats(): array
    {
        if (empty($this->executionLog)) {
            return [];
        }

        $steps = $this->executionLog['steps'];
        $totalSteps = count($steps);
        
        if ($totalSteps === 0) {
            return ['total_steps' => 0];
        }

        $startTime = $steps[0]['microtime'];
        $endTime = end($steps)['microtime'];
        $totalDuration = $endTime - $startTime;

        return [
            'total_steps' => $totalSteps,
            'total_duration' => $totalDuration,
            'average_step_duration' => $totalDuration / $totalSteps,
            'start_time' => $this->executionLog['start_time'],
            'sub_advertiser_id' => $this->executionLog['sub_advertiser_id']
        ];
    }

    /**
     * 检查账户是否可以进行加粉充值
     * 
     * @param string $subAdvertiserId 子账户ID
     * @return array 检查结果
     */
    public function checkRechargeEligibility(string $subAdvertiserId): array
    {
        $checks = [];

        try {
            $this->validateBasicConditions($subAdvertiserId);
            $checks['basic'] = ['status' => 'passed', 'message' => '基础条件检查通过'];
        } catch (Exception $e) {
            $checks['basic'] = ['status' => 'failed', 'message' => $e->getMessage()];
        }

        try {
            $this->validateConfiguration($subAdvertiserId);
            $checks['config'] = ['status' => 'passed', 'message' => '配置检查通过'];
        } catch (Exception $e) {
            $checks['config'] = ['status' => 'failed', 'message' => $e->getMessage()];
        }

        try {
            $this->validateRechargeFrequency($subAdvertiserId);
            $checks['frequency'] = ['status' => 'passed', 'message' => '频次检查通过'];
        } catch (Exception $e) {
            $checks['frequency'] = ['status' => 'failed', 'message' => $e->getMessage()];
        }

        $allPassed = array_reduce($checks, function($carry, $check) {
            return $carry && $check['status'] === 'passed';
        }, true);

        return [
            'eligible' => $allPassed,
            'checks' => $checks
        ];
    }

    /**
     * 获取默认配置
     * 
     * @return array 默认配置
     */
    private function getDefaultConfig(): array
    {
        return [
            'recharge_amount' => self::DEFAULT_RECHARGE_AMOUNT,
            'system_user_name' => '系统自动充值',
            'enable_notification' => false,
            'enable_time_range_check' => false,
            'time_range_end' => 8, // 8点之前
            'enable_batch_delay' => true,
            'batch_delay_microseconds' => 100000, // 100毫秒
            'max_retry_times' => 3
        ];
    }

    /**
     * 重置工作流状态
     */
    public function reset(): void
    {
        $this->executionLog = [];
    }

    /**
     * 创建工作流实例
     * 
     * @param array $config 配置参数
     * @return AddFansRechargeWorkflow
     */
    public static function create(array $config = []): AddFansRechargeWorkflow
    {
        return new self(null, $config);
    }

    /**
     * 创建带有自定义服务的工作流实例
     * 
     * @param TransferMoneyServiceV2 $service 自定义服务
     * @param array $config 配置参数
     * @return AddFansRechargeWorkflow
     */
    public static function createWithService(TransferMoneyServiceV2 $service, array $config = []): AddFansRechargeWorkflow
    {
        return new self($service, $config);
    }
}