<?php

namespace backendapi\services\wxcom;

use common\helpers\DateHelper;
use Yii;
use common\models\wxcom\User;
use common\models\wxcom\Department;
use console\models\WxcomCusCustomer;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use common\models\wxcom\Department as WxcomDepartment;
use EasyWeChat\Factory;
use common\models\wxcom\Com;
use common\models\wxcom\User as WxcomUser;
use common\models\backend\Store;
use Exception;

class UserService extends User
{
    // 扩展操作信息展示
    use \common\traits\ModelOperateInfoDisplay;
    public function attributeExtends()
    {
        return [
            'created_by_text',
            'created_at_text',
            'updated_by_text',
            'updated_at_text',
            'cus_count',
        ];
    }
    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws Exception
     */
    public function search($params = [], $is_export = false)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = self::find();
        $query->with(['createdPerson', 'updatedPerson','user']);
        $query->andFilterWhere(['=', 'entity_id', Yii::$app->user->identity->current_entity_id]);
        $query->andWhere(['deleted_at' => 0]);
        if ($params['dept_id']) {
            $where = new Expression("FIND_IN_SET(:deptId, department)", [':deptId' => $params['dept_id']]);
            $query->andWhere($where);
        }

        $this->load($params, '');
        $this->validate();
        $query->andFilterWhere(['like', 'name', $this->name]);

        if ($is_export) { //导出
            if (!empty($params['getTotal'])) return [[], $query->count()]; //获取总条数
        }

        $totalCount = $query->count();
        $list = $query->offset($offset)->limit($limit)->orderBy('created_at DESC,id DESC')->all();
        $newList = [];
        foreach ($list as $user) {
            $memberInfo = $user->user ?: null;
            $user = ArrayHelper::toArray($user);
            $user['departments'] = Department::getDeptNamesByIds($user['department']);
            $user['head_portrait'] = $memberInfo ? $memberInfo->head_portrait : '';
            $newList[] = $user;
        }

        return [$newList, $totalCount];
    }

    public static function getInfoById($id)
    {
        $query = self::find();
        $query->andFilterWhere(['=', 'entity_id', Yii::$app->user->identity->current_entity_id]);
        $query->andFilterWhere(['=', 'id', $id]);

        return $query->one();
    }

    public static function getListForSelect($params)
    {
        $query = self::find();
        $query->select('id,name,avatar');
        $query->andFilterWhere(['=', 'entity_id', Yii::$app->user->identity->current_entity_id]);
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere(['or', ['like', 'name', $params['keyword']], ['like', 'wxcom_user_id', $params['keyword']]]);
        }
        $query->andFilterWhere(['id' => $params['id']]);

        $query->asArray();
        $list = $query->all();
        return $list;
    }

    public static function getCustomerService($keyword = null, $comId = 0, $isCheckStatus = false)
    {
        if (!$keyword) {
            return [];
        }

        $deptIds = DepartmentService::getServiceDeptIds($comId);
        if (!count($deptIds)) {
            return [];
        }

        $where = ['or'];
        foreach ($deptIds as $deptId) {
            $where[] = new Expression("FIND_IN_SET({$deptId}, department)");
        }

        $query = self::find()
            ->where($where)
            ->with(['com', 'user' => function ($query) {
                $query->with(['departmentAssignment']);
            }])
            ->andWhere([
                'status' => self::STATUS_ENABLE,
                'entity_id' => Yii::$app->user->identity->current_entity_id
            ])
            ->andWhere(['or', ['like', 'name', $keyword], ['=', 'mobile', $keyword]])
            ->limit(10);

        if ($comId) {
            $query->andWhere(['com_id' => $comId]);
        }

        /** @var array<User> */
        $userList = $query->all();

        $newUserList = [];
        $scope = Yii::$app->services->scopeDataService->getScope();
        $todayDate = date('Ymd',time());
        $monthDate = DateHelper::thisMonth();
        $monthStartDate = DateHelper::toDate($monthDate['start'],'Ymd');
        $monthEndDate = DateHelper::toDate($monthDate['end'],'Ymd');
        foreach ($userList as $user) {
            $newUser = [
                'id' => $user->id,
                'name' => $user->name,
                'avatar' => $user->avatar,
                'alias' => $user->alias ?: '-',
                'customerServiceName' => $user->user->username ?: '-',
                'status' => 1,
                'promote_status' => $user->promote_status,
                'com_name' => $user->com->name,
                'has_qrcode' => $user->hasQrcode(),
                'todayDepositTranRate' => CusQrcodeService::getDepositTranRate($user->user_id, $todayDate, $todayDate),
                'monthDepositTranRate' => CusQrcodeService::getDepositTranRate($user->user_id, $monthStartDate, $monthEndDate),
            ];
            if ($isCheckStatus && count($scope)) {
                if ($user->user->status && !in_array($user->user->departmentAssignment->dept_id, $scope)) {
                    $newUser['status'] = 0;
                }
            }
            $newUserList[] = $newUser;
        }

        return $newUserList;
    }

    /**
     * 获取好友列表
     */
    public static function getFriendList()
    {
        $id = Yii::$app->request->get('id', 0);
        if (empty($id)) return [[], 0];

        $sql = "(   
                    SELECT
                        fs.id,
                        '' as `name`,
                        if(fs.wxcom_user_id = {$id},
                        fs.user_id,fs.wxcom_user_id ) as user_id,
                        fs.source,
                        fs.last_chat_time
                    FROM
                        erp_wxcom_friend_ships fs
                    WHERE
                        fs.wxcom_user_id = {$id} or (fs.user_id = {$id} AND fs.source = 1) 
                )
                UNION ALL
                (
                    SELECT 
                        g.id,
                        g.group_name as `name`,
                        '0' as user_id,
                        '99' as source ,
                        g.last_chat_time 
                    FROM 
                        erp_wxcom_group g 
                    LEFT JOIN erp_wxcom_group_member gm ON gm.group_id = g.id
                    WHERE 
                        gm.user_id = {$id} AND gm.source = 1 GROUP BY gm.id
                )
            
                ORDER BY last_chat_time DESC";

        // $totalCount = Yii::$app->db->createCommand($sql)->query()->count();
        // if (empty($totalCount)) {
        //     return [[], 0];
        // }
        $totalCount = 0;
        // $page = Yii::$app->request->get('page', 1);
        // $limit = Yii::$app->request->get('limit', 10);

        // $list = Yii::$app->db->createCommand($sql . ' LIMIT ' . $limit . ' OFFSET ' . (($page - 1) * $limit))->queryAll();
        $list = Yii::$app->erp_message->createCommand($sql)->queryAll();

        foreach ($list as &$item) {
            $item['avatar'] = '';
            $item['last_chat_time'] = date('Y-m-d H:i:s', $item['last_chat_time']);
            if ($item['source'] == 99) {
                continue;
            }

            if ($item['user_id'] == 0) {
                $item['name'] = '未知用户';
            }

            //客户信息
            if ($item['source'] == 0) {
                $wxcomCustomeInfo = WxcomCusCustomer::find()->where(['id' => $item['user_id']])->cache(60)->one();
                if (empty($wxcomCustomeInfo)) {
                    $item['name'] = '未知用户';
                    continue;
                }

                $item['name'] = $wxcomCustomeInfo->customer->name ?: $wxcomCustomeInfo->name;
                $item['avatar'] = $wxcomCustomeInfo->avatar;
            }

            if ($item['source'] == 1) {
                $item['name'] = self::find()->select('name')->where(['id' => $item['user_id']])->scalar();
            }
        }

        return [$list, $totalCount];
    }

    /**
     * 调店企微回调
     * @throws Exception
     */
    public static function updateUserDepartmentByCallback()
    {
        $state = Yii::$app->request->get('state', '');
        $stateArray = json_decode(urldecode($state), true);
        $storeId = $stateArray['store_id'] ?? '';

        $wxcomCom = Com::find()->where(['code' => $stateArray['code'] ?? ''])->cache(300)->one();
        if (!$wxcomCom) {
            throw new Exception('企微配置不存在，相关信息 code ：' . $stateArray['code']);
        }

        $config = [
            'corp_id' => $wxcomCom->corp_id,
            'secret'  => $wxcomCom->app_secret,
        ];
        $app = Factory::work($config);

        $user = $app->oauth->detailed()->user();
        $wxcomUserId = $user->getId();
        $wxcomUser = WxcomUser::find()->where(['wxcom_user_id' => $wxcomUserId])->one();
        if (!$wxcomUser) {
            throw new Exception('企微用户不存在，相关信息 wxcom_user_id ：' . $wxcomUserId);
        }

        $wxcomUser->user_id = $stateArray['user_id'];
        if (empty($stateArray['user_id']) || !$wxcomUser->save()) {
            throw new Exception('企微用户绑定失败，相关信息 user_id ：' . $stateArray['user_id'] . '，报错信息：' . current($wxcomUser->getFirstErrors()));
        }

        $store = Store::find()->select(['dept_id', 'store_name'])->where(['id' => $storeId])->one();
        if (!$store) {
            throw new Exception('门店不存在，相关信息 store_id ：' . $storeId);
        }

        self::updateUserDepartment($wxcomUser, $store);
    }

    /**
     * 更新用户企微部门
     * @param $wxcomUser
     * @param $store
     * @throws Exception
     */
    public static function updateUserDepartment($wxcomUser, $store)
    {
        $transaction = Yii::$app->db->beginTransaction();

        $config = ComService::getWxcomConfigByIdForCon($wxcomUser->com_id);
        $app = Factory::work($config);

        try {
            $wxcomDepartmentId = WxcomDepartment::find()->select(['wxcom_id'])->where(['name' => $store->store_name])->scalar();
            if (empty($wxcomDepartmentId)) {
                throw new Exception('企微部门不存在，相关信息 store_name ：' . $store->store_name);
            }

            // 更新用户企微所属部门
            $result = $app->user->update($wxcomUser->wxcom_user_id, [
                'department' => $wxcomDepartmentId,
            ]);
            if ($result['errcode'] != 0) {
                throw new Exception('更新用户企微部门失败：' . $result['errmsg']);
            }

            // 更新本地数据
            $wxcomUser->wxcom_department = (string)$wxcomDepartmentId;
            $wxcomUser->department = (string)$store->dept_id;
            if (!$wxcomUser->save()) {
                throw new Exception('更新本地用户部门数据失败：' . current($wxcomUser->getFirstErrors()));
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            Yii::error('更新用户企微部门失败:' . $e->getMessage(), 'updateUserDepartment');
            throw $e;
        }
    }
}
