<?php

namespace backendapi\services\ai;

use common\helpers\ArrayHelper;
use common\models\Config;
use common\services\ai\AiServiceFactory;
use backendapi\services\wxcom\CusMessageService;
use common\enums\wxcom\CusMessageOriginEnum;

/**
 * 企微客服AI业务服务类
 */
class WxcomCustomerSupportAiService
{
    public static function autoReply($params)
    {
        $content = ArrayHelper::getValue($params, 'content');
        $openKfId = ArrayHelper::getValue($params, 'open_kfid');
        $externalUserId = ArrayHelper::getValue($params, 'external_userid');

        $chatService = (new AiServiceFactory('qwen', 'qwen-turbo-2025-04-28'))->createWxcomCusSupportService();
        $history = CusMessageService::getTextMessageHistory($externalUserId, $openKfId, [CusMessageOriginEnum::CUSTOMER, CusMessageOriginEnum::LLM]);

        $messages = [];
        foreach ($history as $item) {
            $messages[] = [
                'is_reply' => $item['origin'] == CusMessageOriginEnum::CUSTOMER ? 0 : 1,
                'content' => $item['content']
            ];
        }
        $messages[] = [
            'is_reply' => 0,
            'content' => $content
        ];

        $systemPrompt = Config::getByName('wxcomCusSupportSystemPrompt');
        return $chatService->chat($messages, $systemPrompt);
    }
}