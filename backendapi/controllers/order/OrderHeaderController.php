<?php

namespace backendapi\controllers\order;

use backendapi\controllers\NewBaseController;
use backendapi\services\order\OrderHeaderService;
use common\enums\order\OrderHeaderSourceTypeEnum;
use common\helpers\ResultHelper;
use common\models\backend\order\OrderHeader;
use common\models\Customer;
use auth\services\customer\MobileViewLogService;
use Exception;
use Yii;

class OrderHeaderController extends NewBaseController
{
    /** @var OrderHeaderService */
    public $serviceClass = OrderHeaderService::class;

    /**
     * 创建订单 - 客服下单
     *
     * @return array|mixed
     */
    public function actionCreate()
    {
        $trans = Yii::$app->db->beginTransaction();
        try {
            $model = $this->serviceClass::create(Yii::$app->request->post());
            $trans->commit();
        } catch (Exception $e) {
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '新增成功', $model);
    }

    /**
     * 创建订单 - 其他端口下单
     *
     * @return array|mixed
     */
    public function actionOtherCreate()
    {
        $trans = Yii::$app->db->beginTransaction();
        try {
            $params = Yii::$app->request->post();
            $params['is_pc'] = true;
            $params['source_type'] = OrderHeaderSourceTypeEnum::SOURCE_TYPE_STORE;
            $model = $this->serviceClass::newCreate($params);
            $trans->commit();
        } catch (Exception $e) {
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '新增成功', $model);
    }

    /**
     * 获取订单详情
     *
     * @param $id
     * @return array|mixed
     */
    public function actionView($id)
    {
        try {
            $list = $this->serviceClass::getView($id);
            return ['info' => $list];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 预约到店列表
     *
     * @return array|mixed
     */
    public function actionStoreList()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount, $complete_list, $complete_totalCount, $order) = (new $this->serviceClass)->storeList($params);
            return ['list' => $list, 'totalCount' => $totalCount, 'complete_list' => $complete_list, 'complete_totalCount' => $complete_totalCount, 'order' => $order];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店订单查询列表
     *
     * @return array|mixed
     */
    public function actionStoreOrderList()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = (new $this->serviceClass)->search($params, false, 'store');
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店待结算统计
     *
     * @return array|mixed
     */
    public function actionStoreSettlementCount()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($totalOrdersCount, $totalTransactionsCount) = (new $this->serviceClass)->settlementCountByStore($params);
            return ['totalOrdersCount' => $totalOrdersCount, 'totalTransactionsCount' => $totalTransactionsCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店客户可使用订单
     * @return array|mixed
     */
    public function actionStoreCusList()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = (new $this->serviceClass)->storeCusList($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取门店订单详情
     *
     * @param $id
     * @return array|mixed
     */
    public function actionStoreView($id)
    {
        try {
            $list = $this->serviceClass::getView($id);
            return ['info' => $list];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取门店订单详情-用于编辑
     *
     * @return array|mixed
     */
    public function actionStoreViewForEdit()
    {
        try {
            $params = Yii::$app->request->get();
            if (!$params['id']) {
                throw new Exception('订单id不能为空');
            }

            return $this->serviceClass::getViewForEdit($params['id']);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取门店订单详情-用于修改预约
     *
     * @return array|mixed
     */
    public function actionStoreViewForPlan()
    {
        try {
            $params = Yii::$app->request->get();
            $params['source_type'] = OrderHeader::SOURCE_TYPE_STORE;
            return $this->serviceClass::getViewForPlan($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店修改预约
     *
     * @return array|mixed
     */
    public function actionStoreUpdateForPlan()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            $params = Yii::$app->request->post();
            $params['source_type'] = OrderHeader::SOURCE_TYPE_STORE;
            $this->serviceClass::updateForPlan($params);
            $tran->commit();
        } catch (Exception $e) {
            $tran->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '修改成功', $params['id']);
    }

    /**
     * 获取门店订单详情-用于打印
     *
     * @return array|mixed
     */
    public function actionStoreViewForPrint()
    {
        try {
            $params = Yii::$app->request->get();
            if (!$params['id']) {
                throw new Exception('订单id不能为空');
            }

            return $this->serviceClass::getViewForPrint($params['id']);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionStoreSettlement()
    {
        try {
            $params = Yii::$app->request->post();
            return $this->serviceClass::storeSettlement($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionStoreCreate()
    {
        try {
            $params = Yii::$app->request->post();
            return $this->serviceClass::storeCreate($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionStoreFinish()
    {
        try {
            $params = Yii::$app->request->post();
            return $this->serviceClass::storeFinish($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 修改为到店状态
     * @return array|mixed
     */
    public function actionUpdateOrderStatus()
    {
        try {
            $id = Yii::$app->request->post('id', 0);
            $this->serviceClass::updateOrderStatus($id, Yii::$app->request->post());
            return ResultHelper::json(200, '操作成功', $id);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 订单订金核销
     *
     * @return array|mixed
     */
    public function actionOrderPrepay()
    {
        $trans = Yii::$app->db->beginTransaction();
        try {
            $model = $this->serviceClass::orderPrepay(Yii::$app->request->post());
            $trans->commit();
        } catch (Exception $e) {
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '支付成功', ['list' => $model]);
    }

    /**
     * 刷新流水
     *
     * @return array|mixed
     */
    public function actionRefreshWriteList()
    {
        try {
            $this->serviceClass::refreshWriteList();
            return ResultHelper::json(200, '刷新流水成功！');
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 订金流水列表
     *
     * @return array|mixed
     */
    public function actionWriteList()
    {
        try {
            $params = Yii::$app->request->get();
            list($list, $totalCount) = (new $this->serviceClass)->getWriteList($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取客服订单详情-用于修改预约
     *
     * @return array|mixed
     */
    public function actionServicerViewForPlan()
    {
        try {
            $params = Yii::$app->request->get();
            $params['source_type'] = OrderHeader::SOURCE_TYPE_SERVICER;
            return $this->serviceClass::getViewForPlan($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 客服修改预约
     *
     * @return array|mixed
     */
    public function actionServicerUpdateForPlan()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            $params = Yii::$app->request->post();
            $params['source_type'] = OrderHeader::SOURCE_TYPE_SERVICER;
            $this->serviceClass::updateForPlan($params);
            $tran->commit();
        } catch (Exception $e) {
            $tran->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '修改成功', $params['id']);
    }

    /**
     * 客服-取消订单
     *
     * @return array|mixed
     */
    public function actionCancel()
    {
        try {
            $id = Yii::$app->request->post('id', 0);
            $this->serviceClass::cancel($id);
            return ResultHelper::json(200, '取消成功', $id);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店-取消订单
     *
     * @return array|mixed
     */
    public function actionStoreCancel()
    {
        try {
            $id = Yii::$app->request->post('id', 0);
            $this->serviceClass::storeCancel($id);
            return ResultHelper::json(200, '取消成功', $id);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店-待结算撤回状态到已预约
     *
     * @return array|mixed
     */
    public function actionStoreRetract()
    {
        try {
            $id = Yii::$app->request->post('id', 0);
            $this->serviceClass::storeRetract($id);
            return ResultHelper::json(200, '操作成功', $id);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 查看手机号
     *
     * @return array|mixed
     */
    public function actionMobileCheck()
    {
        try {
            $params = Yii::$app->request->get();
            $id = $params['cus_id'] ?? 0;
            if (empty($id)) {
                return ResultHelper::json(422, 'cus_id不能为空');
            }

            $customer = Customer::findOne($id);
            $params['mobile'] = $customer->mobile;

            // 记录查看手机号日志并监测
            if ($params['source'] == 'order_list') {
                MobileViewLogService::create($params);
                MobileViewLogService::viewWarning($params['order_id'] ?? 0);
            }

            return ResultHelper::json(200, '获取成功', [
                'mobile' => $customer->mobile
            ]);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionLklList()
    {
        try {
            $params = Yii::$app->request->get();
            list($list, $totalCount) = (new $this->serviceClass)->getLklList($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 作废订单
     *
     * @return array|mixed
     */
    public function actionCancellation()
    {
        try {
            $id = Yii::$app->request->post('id', 0);
            $this->serviceClass::cancellation($id);
            return ResultHelper::json(200, '作废成功', $id);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 到店表数据
     *
     * @return array|mixed
     */
    public function actionReachStore()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = (new $this->serviceClass)->reachStoreData($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 到店表数据-导出
     *
     * @return array|mixed
     */
    public function actionReachStoreExport()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = (new $this->serviceClass)->reachStoreData($params, true);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionRevertSettlement()
    {
        try {
            $params = Yii::$app->request->post();
            return $this->serviceClass::revertSettlement($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 修改预约门店
     */
    public function actionChangePlanStore()
    {
        try {
            $params = Yii::$app->request->post();
            return $this->serviceClass::changePlanStore($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 客服业绩
     */
    public function actionCusServicePerformance()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = (new $this->serviceClass)->cusServicePerformanceOrder($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }
}
