<?php

namespace backendapi\controllers\order;

use backendapi\services\order\RefundApplicationService;
use common\enums\order\RefundApplicationStatusEnum;
use common\helpers\ResultHelper;
use Exception;
use Yii;

/**
 * 管理-后台Api控制器
 */
class RefundApplicationController extends \auth\controllers\order\RefundApplicationController
{
    /**
     * @var RefundApplicationService
     */
    public $serviceClass = RefundApplicationService::class;

    /**
     * 获取可申请退款的订单列表
     */
    public function actionRefundableOrders()
    {
        try {
            $params = Yii::$app->request->get();
            [$data, $totalCount] = $this->serviceClass::getRefundableOrders($params);

            // 数据查询接口直接返回数组格式
            return [
                'list' => $data,
                'totalCount' => $totalCount
            ];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionStatusSelectList()
    {
        return RefundApplicationStatusEnum::getSelectList();
    }


}
