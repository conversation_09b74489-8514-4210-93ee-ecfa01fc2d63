<?php
/**
 * 退款功能完整测试脚本
 * 
 * 测试范围：
 * 1. 退款审批流程
 * 2. 队列任务处理
 * 3. 门店业绩分析数据同步
 * 4. 退款金额和退款率计算验证
 * 5. 门店业绩分析API接口数据准确性验证
 * 6. 每日业绩分析API接口数据准确性验证
 * 
 * 用法: php test-refund-feature.php
 */

// 引入Yii框架
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/vendor/yiisoft/yii2/Yii.php';
require_once __DIR__ . '/common/config/bootstrap.php';
require_once __DIR__ . '/console/config/bootstrap.php';

// 初始化Yii应用
$config = require __DIR__ . '/console/config/main.php';
$application = new yii\console\Application($config);

// 创建测试用户登录
$testUser = new class implements \yii\web\IdentityInterface {
    public $id = 214;   // 测试用户ID
    public $entity_id = 1;
    public $current_entity_id = 1;
    
    public static function findIdentity($id) { return null; }
    public static function findIdentityByAccessToken($token, $type = null) { return null; }
    public function getId() { return $this->id; }
    public function getAuthKey() { return 'test'; }
    public function validateAuthKey($authKey) { return true; }
};
Yii::$app->user->setIdentity($testUser);

use common\components\feishu\process\RefundApprovalProcess;
use common\enums\order\RefundApplicationStatusEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\models\order\RefundApplication;
use common\models\order\RefundApplicationDetail;
use common\models\backend\order\OrderHeader;
use common\models\data\StorePerformanceAnalysis;
use common\models\backend\Store;
use common\queues\StorePerformanceAnalysisJob;
use auth\services\data\StorePerformanceAnalysisService;
use common\helpers\BcHelper;

// 全局测试统计
$testStats = [
    'total' => 0,
    'passed' => 0,
    'failed' => 0,
    'errors' => []
];

/**
 * 主测试函数
 */
function runRefundFeatureTests() {
    global $testStats;
    
    echo "\n";
    echo "=================================================\n";
    echo "          退款功能完整测试开始\n";
    echo "=================================================\n";
    
    try {
        // 1. 初始化测试环境
        echo "\n【步骤1】初始化测试环境...\n";
        initializeTestEnvironment();
        
        // 2. 创建测试数据
        echo "\n【步骤2】创建测试数据...\n";
        $testData = createTestData();
        
        // 3. 执行退款审批流程测试
        echo "\n【步骤3】退款审批流程测试...\n";
        testRefundApprovalFlow($testData);
        
        // 4. 队列任务处理测试
        echo "\n【步骤4】队列任务处理测试...\n";
        testQueueProcessing($testData);
        
        // 5. 数据同步验证
        echo "\n【步骤5】数据同步验证...\n";
        testDataSynchronization($testData);
        
        // 6. 退款率计算验证
        echo "\n【步骤6】退款率计算验证...\n";
        testRefundRateCalculation($testData);
        
        // 7. API接口验证
        echo "\n【步骤7】门店业绩分析API接口验证...\n";
        testApiResponse($testData);
        
        // 8. 每日业绩分析API接口验证
        echo "\n【步骤8】每日业绩分析API接口验证...\n";
        testDailyAnalysisApiResponse($testData);
        
        // 9. 边界情况测试
        echo "\n【步骤9】边界情况测试...\n";
        testEdgeCases();
        
        // 10. 清理测试数据
        echo "\n【步骤10】清理测试数据...\n";
        cleanupTestData($testData);
        
    } catch (Exception $e) {
        echo "测试执行异常: " . $e->getMessage() . "\n";
        echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        $testStats['errors'][] = $e->getMessage();
    }
    
    // 输出测试报告
    printTestReport();
}

/**
 * 初始化测试环境
 */
function initializeTestEnvironment() {
    echo "  - 清理可能存在的测试数据...\n";
    
    // 清理测试订单
    $testOrderNos = OrderHeader::find()
        ->select('order_no')
        ->where(['like', 'order_no', 'TEST_REFUND_%', false])
        ->column();
    
    if (!empty($testOrderNos)) {
        OrderHeader::deleteAll(['order_no' => $testOrderNos]);
        echo "  - 清理了 " . count($testOrderNos) . " 个测试订单\n";
    }
    
    // 清理测试退款申请
    $testApplicationNos = RefundApplication::find()
        ->select('application_no')
        ->where(['like', 'application_no', 'TEST_REFUND_%', false])
        ->column();
    
    if (!empty($testApplicationNos)) {
        RefundApplication::deleteAll(['application_no' => $testApplicationNos]);
        echo "  - 清理了 " . count($testApplicationNos) . " 个测试退款申请\n";
    }
    
    // 清理测试日期的业绩分析数据
    $testDate = strtotime(date('Y-m-d'));
    StorePerformanceAnalysis::deleteAll([
        'date' => $testDate,
        'entity_id' => 1
    ]);
    
    echo "  - 测试环境初始化完成\n";
}

/**
 * 创建测试数据
 */
function createTestData() {
    echo "  - 创建多门店多订单测试场景...\n";
    
    $testData = [
        'date' => date('Y-m-d'),
        'timestamp' => strtotime(date('Y-m-d')),
        'stores' => [],
        'orders' => [],
        'refund_applications' => []
    ];
    
    // 测试场景设计：
    // 门店1: 3个订单，退款金额分别为 100, 200, 0，实收金额分别为 1000, 1500, 800
    // 门店2: 2个订单，退款金额分别为 150, 300，实收金额分别为 1200, 2000
    $storeScenarios = [
        [
            'store_id' => 1,
            'orders' => [
                ['received_amount' => 1000, 'refund_amount' => 100],
                ['received_amount' => 1500, 'refund_amount' => 200],
                ['received_amount' => 800, 'refund_amount' => 0]
            ]
        ],
        [
            'store_id' => 2,
            'orders' => [
                ['received_amount' => 1200, 'refund_amount' => 150],
                ['received_amount' => 2000, 'refund_amount' => 300]
            ]
        ]
    ];
    
    foreach ($storeScenarios as $scenario) {
        $storeData = [
            'store_id' => $scenario['store_id'],
            'orders' => [],
            'total_received' => 0,
            'total_refund' => 0
        ];
        
        foreach ($scenario['orders'] as $orderData) {
            $order = createTestOrder($scenario['store_id'], $orderData['received_amount'], $orderData['refund_amount']);
            $storeData['orders'][] = $order;
            $storeData['total_received'] += $orderData['received_amount'];
            $storeData['total_refund'] += $orderData['refund_amount'];
            
            $testData['orders'][] = $order;
        }
        
        $testData['stores'][] = $storeData;
    }
    
    echo "  - 创建了 " . count($testData['orders']) . " 个测试订单\n";
    echo "  - 涉及 " . count($testData['stores']) . " 个门店\n";
    
    return $testData;
}

/**
 * 创建测试订单
 */
function createTestOrder($storeId, $receivedAmount, $refundAmount) {
    $order = new OrderHeader();
    $order->order_no = 'TEST_REFUND_' . date('YmdHis') . '_' . $storeId . '_' . rand(1000, 9999);
    $order->store_id = $storeId;
    $order->cus_id = 1;
    $order->channel_id = 1;
    $order->order_status = OrderHeaderStatusEnum::STATUS_COMPLETED;
    $order->received_amount = $receivedAmount;
    $order->refund_amount = $refundAmount;
    $order->amount = $receivedAmount;
    $order->pay_amount = $receivedAmount;
    $order->plan_time = strtotime(date('Y-m-d H:i:s'));
    $order->source_type = OrderHeader::SOURCE_TYPE_SERVICER;
    $order->entity_id = 1;
    $order->created_by = 1;
    $order->created_at = time();
    $order->updated_at = time();
    
    if (!$order->save()) {
        throw new Exception('测试订单创建失败: ' . json_encode($order->errors));
    }
    
    return $order;
}

/**
 * 测试退款审批流程
 */
function testRefundApprovalFlow($testData) {
    global $testStats;
    
    // 为每个有退款的订单创建退款申请
    foreach ($testData['stores'] as $storeData) {
        foreach ($storeData['orders'] as $order) {
            if ($order->refund_amount > 0) {
                echo "  - 测试订单 {$order->order_no} 的退款审批流程...\n";
                
                // 创建退款申请
                $refundApplication = createTestRefundApplication($order);
                $testData['refund_applications'][] = $refundApplication;
                
                // 执行审批通过流程
                $originalRefundAmount = $order->refund_amount;
                $order->refund_amount = 0; // 重置为0，模拟审批前状态
                $order->save();
                
                // 执行审批
                $process = createMockRefundApprovalProcess($refundApplication, 'APPROVED');
                $process->onAgree();
                
                // 验证订单退款金额是否正确设置
                $order->refresh();
                $testName = "订单退款金额设置验证";
                assertEquals($originalRefundAmount, $order->refund_amount, $testName);
                
                echo "    ✓ 订单 {$order->order_no} 退款金额正确设置为 {$order->refund_amount}\n";
            }
        }
    }
}

/**
 * 创建测试退款申请
 */
function createTestRefundApplication($order) {
    $refundApplication = new RefundApplication();
    $refundApplication->application_no = 'TEST_REFUND_APP_' . date('YmdHis') . '_' . rand(1000, 9999);
    $refundApplication->process_instance_id = 'test_instance_' . time() . '_' . rand(1000, 9999);
    $refundApplication->status = RefundApplicationStatusEnum::IN_REVIEW;
    $refundApplication->cus_id = $order->cus_id;
    $refundApplication->store_id = $order->store_id;
    $refundApplication->total_order_amount = $order->received_amount;
    $refundApplication->total_refund_amount = $order->refund_amount;
    $refundApplication->refund_reason = '测试退款';
    $refundApplication->teacher_id = 1;
    $refundApplication->bank_account_type = 1;
    $refundApplication->bank_account_info = json_encode([
        'account_name' => '测试账户',
        'account_no' => '***************',
        'bank_name' => '测试银行',
        'bank_location' => '测试地区',
        'bank_branch' => '测试支行'
    ]);
    $refundApplication->entity_id = 1;
    $refundApplication->created_by = 1;
    $refundApplication->created_at = time();
    $refundApplication->updated_at = time();
    
    if (!$refundApplication->save()) {
        throw new Exception('退款申请创建失败: ' . json_encode($refundApplication->errors));
    }
    
    // 创建退款明细
    $refundDetail = new RefundApplicationDetail();
    $refundDetail->application_id = $refundApplication->id;
    $refundDetail->order_id = $order->id;
    $refundDetail->order_amount = $order->received_amount;
    $refundDetail->refund_amount = $order->refund_amount;
    $refundDetail->created_at = time();
    $refundDetail->updated_at = time();
    
    if (!$refundDetail->save()) {
        throw new Exception('退款明细创建失败: ' . json_encode($refundDetail->errors));
    }
    
    return $refundApplication;
}

/**
 * 创建Mock的RefundApprovalProcess实例
 */
function createMockRefundApprovalProcess($refundApplication, $status) {
    $message = [
        'ComCode' => 'test_company',
        'event' => [
            'approval_code' => 'test_approval',
            'instance_code' => $refundApplication->process_instance_id,
            'status' => $status,
            'operate_time' => time() * 1000
        ]
    ];
    
    return new class($message) extends RefundApprovalProcess {
        public function __construct($message) {
            // 跳过父类构造函数中的飞书API调用
            $this->approval_code = $message['event']['approval_code'] ?? '';
            $this->instance_code = $message['event']['instance_code'] ?? '';
            $this->status = $message['event']['status'] ?? '';
            $this->operate_time = $message['event']['operate_time'] ?? '';
            
            if ($this->operate_time) {
                $this->operate_time = (int)($this->operate_time / 1000);
            }
            
            // 查找退款申请
            $this->refundApplication = RefundApplication::find()
                ->where(['process_instance_id' => $this->instance_code])
                ->one();
            
            if (empty($this->refundApplication)) {
                throw new Exception("找不到审批单号【instance_code:{$this->instance_code}】，对应的退款申请");
            }
            
            // Mock飞书API返回数据
            $this->instanceDetail = [
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'form' => json_encode([
                        ['name' => '出纳确认', 'value' => '已确认']
                    ]),
                    'timeline' => [
                        [
                            'type' => 'START',
                            'user_id' => 'test_user',
                            'create_time' => time() * 1000
                        ]
                    ],
                    'task_list' => []
                ]
            ];
        }
    };
}

/**
 * 测试队列任务处理
 */
function testQueueProcessing($testData) {
    global $testStats;
    
    echo "  - 手动触发队列任务处理...\n";
    
    // 创建队列任务并直接执行
    $job = new StorePerformanceAnalysisJob([
        'date' => $testData['date'],
        'entityId' => 1
    ]);
    
    // 直接调用run方法，避免Redis依赖
    $result = $job->run(null);
    
    $testName = "队列任务执行结果";
    assertTrue($result, $testName);
    
    echo "  ✓ 队列任务执行完成\n";
}

/**
 * 测试数据同步验证
 */
function testDataSynchronization($testData) {
    global $testStats;
    
    echo "  - 验证门店业绩分析数据同步...\n";
    
    foreach ($testData['stores'] as $storeData) {
        $storeId = $storeData['store_id'];
        $expectedRefundAmount = $storeData['total_refund'];
        $expectedReceivedAmount = $storeData['total_received'];
        
        // 查询同步后的数据
        $analysisData = StorePerformanceAnalysis::findOne([
            'store_id' => $storeId,
            'date' => $testData['timestamp'],
            'entity_id' => 1
        ]);
        
        if ($analysisData) {
            $testName = "门店{$storeId}退款金额同步验证";
            assertEquals($expectedRefundAmount, (float)$analysisData->refund_amount, $testName);
            
            $testName = "门店{$storeId}实收金额同步验证"; 
            assertEquals($expectedReceivedAmount, (float)$analysisData->received_amount, $testName);
            
            echo "  ✓ 门店{$storeId}: 退款金额 {$analysisData->refund_amount}, 实收金额 {$analysisData->received_amount}\n";
        } else {
            recordTestFailure("门店{$storeId}数据同步失败", "未找到业绩分析数据");
        }
    }
}

/**
 * 测试退款率计算验证
 */
function testRefundRateCalculation($testData) {
    global $testStats;
    
    echo "  - 验证退款率计算逻辑...\n";
    
    foreach ($testData['stores'] as $storeData) {
        $storeId = $storeData['store_id'];
        $refundAmount = $storeData['total_refund'];
        $receivedAmount = $storeData['total_received'];
        
        // 计算预期退款率
        $expectedRefundRate = BcHelper::percentage($refundAmount, $receivedAmount);
        
        // 查询数据库中的退款率
        $analysisData = StorePerformanceAnalysis::findOne([
            'store_id' => $storeId,
            'date' => $testData['timestamp'],
            'entity_id' => 1
        ]);
        
        if ($analysisData) {
            // 手动计算退款率（模拟service层逻辑）
            $calculatedRate = BcHelper::percentage($analysisData->refund_amount, $analysisData->received_amount);
            
            $testName = "门店{$storeId}退款率计算验证";
            assertEquals($expectedRefundRate, $calculatedRate, $testName);
            
            echo "  ✓ 门店{$storeId}: 退款率 {$calculatedRate} (退款金额: {$refundAmount}, 实收金额: {$receivedAmount})\n";
        }
    }
}

/**
 * 测试API接口验证
 */
function testApiResponse($testData) {
    global $testStats;
    
    echo "  - 验证API接口返回数据...\n";
    
    try {
        $params = [
            'start_time' => $testData['timestamp'],
            'end_time' => $testData['timestamp'] + 86399
        ];
        
        // 调用门店业绩分析服务
        [$listData, $totalCount] = StorePerformanceAnalysisService::search($params);
        
        $testName = "API返回数据数量验证";
        assertTrue($totalCount > 0, $testName);
        
        // 验证返回数据包含退款相关字段
        foreach ($listData as $item) {
            if (isset($item['store_id']) && $item['store_id'] !== '-') {
                $hasRefundAmount = array_key_exists('refund_amount', $item);
                $hasRefundRate = array_key_exists('refund_rate', $item);
                
                $testName = "门店{$item['store_id']}API数据包含退款金额字段";
                assertTrue($hasRefundAmount, $testName);
                
                $testName = "门店{$item['store_id']}API数据包含退款率字段";  
                assertTrue($hasRefundRate, $testName);
                
                if ($hasRefundAmount && $hasRefundRate) {
                    echo "  ✓ 门店{$item['store_id']}: API返回退款金额 {$item['refund_amount']}, 退款率 {$item['refund_rate']}\n";
                }
            }
        }
    } catch (Exception $e) {
        echo "  - API接口测试跳过: " . $e->getMessage() . "\n";
        echo "  - 这是因为在console环境中无法完全模拟web请求上下文\n";
        echo "  - 建议在实际web环境中手动验证API接口\n";
        
        // 直接验证数据库中是否有我们期望的数据结构
        echo "  - 改为验证数据库中的数据结构...\n";
        
        foreach ($testData['stores'] as $storeData) {
            $storeId = $storeData['store_id'];
            
            $analysisData = StorePerformanceAnalysis::findOne([
                'store_id' => $storeId,
                'date' => $testData['timestamp'],
                'entity_id' => 1
            ]);
            
            if ($analysisData) {
                $testName = "门店{$storeId}数据库字段refund_amount存在性验证";
                assertTrue($analysisData->hasAttribute('refund_amount'), $testName);
                
                echo "  ✓ 门店{$storeId}: 数据库中退款金额字段存在，值为 {$analysisData->refund_amount}\n";
            }
        }
    }
}

/**
 * 测试每日业绩分析API接口验证
 */
function testDailyAnalysisApiResponse($testData) {
    global $testStats;
    
    echo "  - 验证每日业绩分析API接口返回数据...\n";
    
    try {
        $params = [
            'start_time' => $testData['timestamp'],
            'end_time' => $testData['timestamp'] + 86399,
            'page' => 1,
            'rows' => 20
        ];
        
        // 调用每日业绩分析服务
        [$listData, $totalCount] = StorePerformanceAnalysisService::dailyAnalysis($params);
        
        $testName = "每日业绩分析API返回数据数量验证";
        assertTrue($totalCount > 0, $testName);
        
        // 验证返回数据包含退款相关字段
        foreach ($listData as $item) {
            if (isset($item['store_id']) && $item['store_id'] !== '-') {
                $hasRefundAmount = array_key_exists('refund_amount', $item);
                $hasRefundRate = array_key_exists('refund_rate', $item);
                $hasDate = array_key_exists('date', $item);
                
                $testName = "每日分析门店{$item['store_id']}数据包含日期字段";
                assertTrue($hasDate, $testName);
                
                $testName = "每日分析门店{$item['store_id']}数据包含退款金额字段";
                assertTrue($hasRefundAmount, $testName);
                
                $testName = "每日分析门店{$item['store_id']}数据包含退款率字段";  
                assertTrue($hasRefundRate, $testName);
                
                if ($hasRefundAmount && $hasRefundRate && $hasDate) {
                    echo "  ✓ 每日分析门店{$item['store_id']} 日期{$item['date']}: 退款金额 {$item['refund_amount']}, 退款率 {$item['refund_rate']}\n";
                }
            }
        }
        
        // 验证数据是否按日期+门店维度返回
        $dateStoreKeys = [];
        foreach ($listData as $item) {
            if (isset($item['store_id']) && isset($item['date']) && $item['store_id'] !== '-') {
                $key = $item['date'] . '_' . $item['store_id'];
                $dateStoreKeys[] = $key;
            }
        }
        
        $testName = "每日业绩分析数据维度验证(按日期+门店)";
        assertTrue(count($dateStoreKeys) > 0, $testName);
        
        // 验证是否有重复的日期+门店组合
        $uniqueKeys = array_unique($dateStoreKeys);
        $testName = "每日业绩分析数据无重复验证";
        assertEquals(count($dateStoreKeys), count($uniqueKeys), $testName);
        
        echo "  ✓ 每日业绩分析API接口验证通过，返回 " . count($listData) . " 条记录\n";
        
    } catch (Exception $e) {
        echo "  - 每日业绩分析API接口测试跳过: " . $e->getMessage() . "\n";
        echo "  - 这是因为在console环境中无法完全模拟web请求上下文\n";
        echo "  - 建议在实际web环境中手动验证每日业绩分析API接口\n";
        
        // 回退到数据库验证
        echo "  - 改为验证数据库中的每日数据结构...\n";
        
        // 验证数据库中是否存在按日期维度的数据
        $allAnalysisData = StorePerformanceAnalysis::find()
            ->where(['date' => $testData['timestamp'], 'entity_id' => 1])
            ->andWhere(['in', 'store_id', array_column($testData['stores'], 'store_id')])
            ->all();
            
        foreach ($allAnalysisData as $analysisData) {
            $testName = "每日分析门店{$analysisData->store_id}数据库退款字段存在性验证";
            assertTrue($analysisData->hasAttribute('refund_amount'), $testName);
            
            // 验证日期格式化
            $formattedDate = date('Y-m-d', $analysisData->date);
            echo "  ✓ 每日分析门店{$analysisData->store_id} 日期{$formattedDate}: 数据库退款金额 {$analysisData->refund_amount}\n";
        }
    }
}

/**
 * 测试边界情况
 */
function testEdgeCases() {
    global $testStats;
    
    echo "  - 测试边界情况...\n";
    
    // 测试退款金额为0的情况
    $testName = "退款金额为0时退款率计算";
    $refundRate = BcHelper::percentage(0, 1000);
    assertEquals("0.00%", $refundRate, $testName);
    
    // 测试实收金额为0时退款率计算
    $testName = "实收金额为0时退款率计算";
    $refundRate = BcHelper::percentage(100, 0);
    assertEquals("0.00%", $refundRate, $testName);
    
    // 测试大金额精度
    $testName = "大金额精度测试";
    $refundRate = BcHelper::percentage(123456.789, 987654.321);
    assertTrue(strpos($refundRate, '%') !== false, $testName);
    
    echo "  ✓ 边界情况测试通过\n";
}

/**
 * 清理测试数据
 */
function cleanupTestData($testData) {
    echo "  - 清理测试数据...\n";
    
    // 清理退款申请
    foreach ($testData['refund_applications'] as $refundApp) {
        RefundApplicationDetail::deleteAll(['application_id' => $refundApp->id]);
        $refundApp->delete();
    }
    
    // 清理订单
    foreach ($testData['orders'] as $order) {
        $order->delete();
    }
    
    // 清理业绩分析数据
    StorePerformanceAnalysis::deleteAll([
        'date' => $testData['timestamp'],
        'entity_id' => 1
    ]);
    
    echo "  - 测试数据清理完成\n";
}

/**
 * 断言函数 - 相等性检查
 */
function assertEquals($expected, $actual, $testName) {
    global $testStats;
    $testStats['total']++;
    
    if ($expected == $actual) {
        $testStats['passed']++;
        return true;
    } else {
        $testStats['failed']++;
        $error = "测试失败: {$testName} - 期望值: {$expected}, 实际值: {$actual}";
        $testStats['errors'][] = $error;
        echo "  ✗ {$error}\n";
        return false;
    }
}

/**
 * 断言函数 - 布尔值检查
 */
function assertTrue($condition, $testName) {
    global $testStats;
    $testStats['total']++;
    
    if ($condition) {
        $testStats['passed']++;
        return true;
    } else {
        $testStats['failed']++;
        $error = "测试失败: {$testName} - 期望为true，实际为false";
        $testStats['errors'][] = $error;
        echo "  ✗ {$error}\n";
        return false;
    }
}

/**
 * 记录测试失败
 */
function recordTestFailure($testName, $message) {
    global $testStats;
    $testStats['total']++;
    $testStats['failed']++;
    $error = "测试失败: {$testName} - {$message}";
    $testStats['errors'][] = $error;
    echo "  ✗ {$error}\n";
}

/**
 * 输出测试报告
 */
function printTestReport() {
    global $testStats;
    
    echo "\n";
    echo "=================================================\n";
    echo "                测试报告\n";
    echo "=================================================\n";
    echo "总测试数: {$testStats['total']}\n";
    echo "通过数: {$testStats['passed']}\n";
    echo "失败数: {$testStats['failed']}\n";
    
    if ($testStats['failed'] > 0) {
        echo "\n失败详情:\n";
        foreach ($testStats['errors'] as $error) {
            echo "  - {$error}\n";
        }
    }
    
    $successRate = $testStats['total'] > 0 ? round(($testStats['passed'] / $testStats['total']) * 100, 2) : 0;
    echo "\n成功率: {$successRate}%\n";
    
    if ($testStats['failed'] == 0) {
        echo "\n🎉 所有测试通过！退款功能工作正常。\n";
    } else {
        echo "\n❌ 部分测试失败，请检查相关功能。\n";
    }
    
    echo "=================================================\n";
}

// 运行测试
if (php_sapi_name() === 'cli') {
    runRefundFeatureTests();
}