<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2023-03-16 10:10:21
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-05-03 17:34:57
 * @FilePath: \manageSystem\src\components\selfComponents\self-detail-info\index.vue
 * @Description: 
-->
<template>
  <!-- 详情 -->
  <self-detail-modal :show-confirm="false" placement="right" v-model="propvisible" :confirmLoading="confirmLoading"
    :drawerWidth="'70%'" @change="showChange">
    <template slot="centent">
      <self-modal-title title="客户信息">
        <div class="orderContext mt20">
          <div class="context-title mb20">
            <img :src="info.avatar || ''" class="avtal" v-if="info.avatar" />
            <div class="avtal" v-else></div>
            <div>
              <div class="name">
                {{ info.cus_name || "-" }}
                <a-tag color="orange" class="vipTag">VIP</a-tag>
              </div>
              <div class="nick">微信昵称：{{ info.nick_name || "-" }}</div>
            </div>
          </div>
          <div class="information">
            <div class="information-block">
              <div>
                <span>预约门店：</span>
                {{ info.store_name || "-" }}
              </div>
              <div>
                <span>订单编号：</span>
                {{ info.order_no || "-" }}
              </div>
              <div>
                <span>预约老师：</span>
                {{ info.plan_teacher_name || "-" }}
              </div>
              <div>
                <span>预约时间：</span>
                {{ info.plan_time || "-" }}
              </div>
              <div>
                <span>创建人：</span>
                {{ info.created_by_text || "-" }}
              </div>
              <div>
                <span>预约备注：</span>
                {{ info.plan_remark || "-" }}
              </div>
              <div>
                <span>预约状态：</span>
                {{ info.order_status_name || "-" }}
              </div>
              <div>
                <span>结单备注：</span>
                {{ info.settlement_remark || "-" }}
              </div>
              <!-- <div>
                <span>转单系统：</span>
                {{ info.system_code || "-" }}
              </div> -->
              <!-- <div>
                <span>转单单号：</span>
                {{ info.third_order_no || "-" }}
              </div> -->
            </div>
          </div>
        </div>
      </self-modal-title>
      <self-modal-title title="商品信息">
        <a-table v-if="showTable" ref="table" bordered size="middle" :columns="modalColumns"
          :dataSource="info.goods_list" childrenColumnName="details" expandRowByClick defaultExpandAllRows
          :pagination="false" row-key="id" class="mt20">
          <span slot="theRemaining" slot-scope="text, record">
            {{ getRemainingItems(record) }}
          </span>
        </a-table>
      </self-modal-title>
      <self-modal-title title="支付信息">
        <self-modal-cell-detail :labelCol="90" title="订单原价" :detail="info.original_amount" />
        <self-modal-cell-detail :labelCol="90" title="预收金支付" :detail="info.deposit" />
        <self-modal-cell-detail :labelCol="90" v-if="info.group_amount > 0" title="团购抵扣" :detail="info.group_amount" />
        <self-modal-cell-detail :labelCol="90" title="应付金额" :detail="info.pay_amount" />
        <self-modal-cell-detail :labelCol="90" title="实付金额" :detail="info.received_amount" />
        <self-modal-cell-detail :labelCol="90" v-if="info.refund_amount > 0" title="退款金额" :detail="info.refund_amount" />
      </self-modal-title>
    </template>
  </self-detail-modal>
</template>

<script>
import { theOrderManagementByorder } from "@/api/api.js";
export default {
  model: {
    props: "value",
    event: "change",
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      propvisible: false,
      confirmLoading: false,
      showTable: true,
      modalColumns: [],
      info: {},
    };
  },
  watch: {
    value: function (newVal) {
      this.setBoolean();
    },
  },
  created() {
    this.setBoolean();
  },
  methods: {
    handleView(id) {
      this.confirmLoading = true;
      this.propvisible = true;
      theOrderManagementByorder
        .view({ id })
        .then((res) => {
          if (res.code == 200) {
            let goods_list = res.data.info.goods_list;
            let table_head = res.data.info.table_head
              .map((l) => l.dataIndex)
              .filter((l) => this.$validate.isChinese(l));
            goods_list = goods_list.map((item) => {
              if (!item.details || item.details.length == 0) {
                delete item.details;
              }
              table_head.forEach((key) => {
                if (!(key in item)) item[key] = "";
              });
              return item;
            });
            res.data.info.goods_list = this.$utils.TablefieldCompletion({
              list: goods_list,
              child: "details",
            });
            this.modalColumns = res.data.info.table_head;
            this.info = res.data.info;
          }
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    getRemainingItems({ num, use_num }) {
      if ((num || use_num) && use_num != "-" && num != "-") {
        return num - use_num;
      } else return "-";
    },
    setBoolean() {
      this.propvisible = this.value;
    },
    showChange() {
      this.$emit("change", this.propvisible);
    },
  },
};
</script>

<style lang="less" scoped>
.orderContext {
  .vipTag {
    transform: scale(0.7);
  }

  .context-title {
    display: flex;

    // margin-bottom: 10px;
    .avtal {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin-right: 10px;
      background: @bg-color-container-lightGrey;
    }

    >div {
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      padding: 3px 0;
    }

    .name {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: @font-color-black;

      img {
        width: 16px;
        height: 16px;
        margin-left: 5px;
      }
    }

    .nick {
      color: @font-color-gray;
      font-size: 12px;
    }
  }

  .information {
    &-block {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;

      div {
        width: 50%;
        color: #000000;
        margin-bottom: 10px;

        span {
          display: inline-block;
          width: 70px;
          text-align: right;
          color: @font-color-gray;
        }
      }
    }
  }
}
</style>