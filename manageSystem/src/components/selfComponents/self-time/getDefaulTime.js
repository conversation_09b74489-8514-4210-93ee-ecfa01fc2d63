import moment from "moment";
export default {
    current: e => moment()
        .startOf("day")
        .format(e),
    // 当天
    day: function () {
        const start = moment()
            .startOf("day")
            .format(this.dateFormat)
        this.set([start, this.current(this.dateFormat)])
    },
    // 本月开始时间点
    month: function () {
        try {
            let startTimeStamp = moment()
                .startOf("month")
                .format('YYYY-MM-DD')
            let cunrreTimeStamp = moment()
                .format('YYYY-MM-DD')
            if (moment(startTimeStamp).unix() == moment(cunrreTimeStamp).unix()) {
                const start = moment(cunrreTimeStamp).add(-1, "month")
                    .format(this.dateFormat)
                const end = moment()
                    .endOf("day")
                    .add(-1, "d")
                    .format(this.dateFormat)
                this.set([start, end])
            } else {
                const start = moment()
                    .startOf("month")
                    .format(this.dateFormat)
                this.set([start, this.current(this.dateFormat)])
            }
        } catch (error) {
            console.log(error)
        }
    },
    // 本月开始时间点
    monthPeriodOfTime: function () {
        const start = moment().add(-1, "month")
            .format(this.dateFormat)
        this.set([start, this.current(this.dateFormat)])
    },
    // 本年开始时间点
    year: function () {
        const start = moment()
            .startOf("year")
            .format(this.dateFormat)
        this.set([start, this.current(this.dateFormat)])
    },
    // N天前
    timeDayNum: function (num) {
        const start = moment()
            .add(-(num), "d")
            .format(this.dateFormat)
        this.set([start, this.current(this.dateFormat)])
    },
    // 本月第一天到今天
    monthToNow: function () {
        const start = moment()
            .startOf("month")
            .format(this.dateFormat)
        this.set([start, this.current(this.dateFormat)])
    }
}