<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2022-09-14 16:05:36
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-05-04 11:43:34
 * @FilePath: \manageSystem\src\views\storeWorkbench\billingCashier\billingCashier.vue
 * @Description: 开单收银 999999.99
-->
<template>
  <div>
    <a-form layout="inline" :form="form">
      <orderLayout has="order-order-header:store-create" @layoutEvent="layoutEvent" @clear="clear" :record="record" :order_id="order_id" :consumptionProjectLength="
          storeViewForEditData.projects
            ? storeViewForEditData.projects.length
            : 0
        " divider>
        <template slot="left">
          <list v-if="listShow" @onSearch="onSearch" @changeTag="changeTag" @callback="callback" @choosePackage="choosePackage" :list="dataSourceFormat" :alreadyBoughtList="alreadyBoughtList" placeholder="请输入商品名称或编号进行搜索" :tagList="tagList" :loading="loading" :disabled="listDisabled" :product_num="currentUserInfo.product_num || 0" :tab="['商品列表', '已购项目']" />
        </template>
        <!-- 消费信息 -->
        <template slot="right-detail" v-if="shouList">
          <div class="example" v-if="shouListSpin">
            <a-spin tip="正在加载..." />
          </div>
          <a-list :data-source="storeViewForEditData.projects" />
          <card v-for="(item, projectsindex) in storeViewForEditData.projects" :index="projectsindex + 1" :item="item" :key="'card' + projectsindex" :cards="false" @deleteItem="deleteItem(projectsindex)" class="shadow">
            <div slot="right">
              <template v-if="
                  !item.customer_product_id || item.customer_product_id === 0
                ">
                <div class="goodNum">
                  <self-counter class="billingCashierCounter" :theThreshold="1" @change="(e) => counterChange(e, projectsindex)" v-model="storeViewForEditData.projects[projectsindex].num" />
                  <a-form-item label="" class="inputNumberBox">
                    <!-- <span class="priceSymbol">¥</span> -->
                    <a-input-number class="input" :max="999999.99" :min="0" @change="
                        (value) =>
                          inputChange({
                            uuid: item.uuid,
                            value,
                            key: 'goods_price',
                            decimal: true,
                          })
                      " v-decorator="[
                        `storeViewForEditData.projects[${projectsindex}]`,
                        validatorRules.price(item),
                      ]" />
                  </a-form-item>
                </div>
                <div class="theScopeOf" v-if="item.price_type == 1">
                  价格范围：{{
                    priceScopeOf(
                      item,
                      storeViewForEditData.projects[projectsindex].num,
                      1
                    )
                  }}
                </div>
                <div class="theScopeOf" v-else>
                  指定价格：{{
                    priceScopeOf(
                      item,
                      storeViewForEditData.projects[projectsindex].num,
                      2
                    )
                  }}
                </div>
              </template>
              <div v-else class="goodNum goodNumagain">
                <div>
                  <span class="bold m10 f16">{{ item.times }}</span>
                  <span>可用次数</span>
                </div>
              </div>
            </div>
            <div slot="center" class="center" v-if="item.goods_type != 3">
              <a-form-item label="">
                <a-input-number size="small" @change="
                    (value) =>
                      inputChange({
                        uuid: item.uuid,
                        value,
                      })
                  " v-decorator="[
                    `singleUse.projects[${projectsindex}]`,
                    validatorRules.use_num(item),
                  ]" />
              </a-form-item>
              <div class="theScopeOf">本单使用</div>
            </div>
            <div slot="remarks" class="remarks">
              <template v-if="item && item.selectable_num && item.selectable_num > 0">
                <goodsListComponents :form="form" :item="item" :key="index" :index="index" :projectsindexNum="
                    storeViewForEditData.projects[projectsindex].num
                  " @change="changeSmall" @inputChange="inputChange" :componentsIndex="uuid" v-for="(item_selectable_num, index) in item.selectable_num">
                </goodsListComponents>
              </template>
              <template v-else>
                <goodsListComponents :form="form" :item="item_selectable_num" :key="index" :index="projectsindex" :projectsindexNum="
                    storeViewForEditData.projects[projectsindex].num
                  " @change="changeSmall" :componentsIndex="uuid" @inputChange="inputChange" v-for="(item_selectable_num, index) in item.goods_list">
                </goodsListComponents>
              </template>
            </div>
          </card>
          <!-- <h4 class="remarksFortheGiftsTitle">赠送商品</h4>
          <card :top="false">
            <div slot="remarks" class="remarks remarksFortheGifts">
              <remarksTemplate
                v-for="item_ in 2"
                :item="item_"
                :key="item_"
                :freeGoods="false"
              ></remarksTemplate>
            </div>
          </card>
          <a-button
            type="link"
            class="addRemarksFortheGifts"
            @click="addRemarksFortheGifts"
            >+添加商品</a-button
          > -->
        </template>
        <!-- 结单信息 -->
        <template slot="right-statementInformation">
          <div class="statementInformation">
            <h5 @click="open" class="pb8">
              结算信息
              <a-icon type="down" class="buildingNo" :class="{ showStatementInformation: statementInformation }" />
            </h5>
            <div class="originalstatementInformation" :class="{
                'statementInformation-detail': statementInformation,
              }">
              <div class="statementInformation-box">
                <!-- <div class="cell">
                  <span class="title">预收金抵扣</span>
                  <span class="price">-{{ storeViewForEditData.deposit }}</span>
                </div> -->
                <!-- <div class="cell">
                <span class="title">积分抵扣</span>
                <span class="price">-30.00</span>
              </div>
              <div class="cell">
                <span class="title">购物金抵扣</span>
                <span class="price">-30.00</span>
              </div>
              <div class="cell">
                <span class="title">优惠券</span>
                <span class="link">
                  <popover value="已选1张优惠券" title="优惠券">
                    <div class="couponsList">
                      <div
                        v-for="item in 5"
                        :key="'theCards' + item"
                        class="coupons"
                      >
                        <div :class="{ actionCard: item == 1 }">¥100</div>
                        <div>
                          <div>新人10元入会礼券</div>
                          <div>满2000可用|指定商品</div>
                          <div>有效期至2022-09-22</div>
                        </div>
                      </div>
                    </div>
                  </popover>
                </span>
              </div> -->
                <div class="cell">
                  <span class="title">团购收款</span>
                  <span class="link">
                    <!-- {{group_exportData}} -->
                    <div class="aBulkBox">
                      <a-button class="showBtn" :disabled="informationDisabled" type="link" @click="couponPayment">
                        {{
                          group_exportData
                            ? group_exportData.name
                              ? group_exportData.name
                              : "请录入团购信息"
                            : "请录入团购信息"
                        }}
                      </a-button>
                      <a-icon type="close-circle" theme="filled" class="circle" @click="deleteBulk" v-if="group_exportData && group_exportData.name" />
                    </div>
                  </span>
                </div>
                <div class="cell">
                  <span class="title">储值卡抵扣</span>
                  <span class="link">
                    <popover :value="
                        useableListData.deduction_amount > 0
                          ? '已抵扣' + useableListData.deduction_amount
                          : '请选择储值卡'
                      " title="储值卡抵扣" :validation="false" @open="openPopover" @confirm="confirmPopover" @deleteBulk="resetUseableListData" :disabled="getUseableListDisable()" :circle="
                        JSON.stringify(useableListData) != '{}' &&
                        useableListData.id > 0
                      ">
                      <div class="popoverCardList">
                        <commone-self-principal v-model="temporaryUseableListData.id" :requestFun="storedValueCard.useableList" style="width: 100%" unValue="不使用" :query="useableListQuery" placeholder="请选择储值卡" valuekey="name" :isSearchrRequest="false" :isLoading="true" :isRequest="false" ref="useableList" @exportData="exportData" :showAll="false" class="mt10 mb15" />
                        <div class="popoverCardDetail">
                          <a-divider>抵扣详情</a-divider>
                          <div class="popoverCardDetail-list">
                            <template v-if="
                                temporaryUseableListData.detail &&
                                temporaryUseableListData.detail.length > 0
                              ">
                              <div class="popoverCardDetail-cell" v-for="(
                                  item, index
                                ) in temporaryUseableListData.detail" :key="index">
                                <span>{{ item.goods_name || "-" }}</span>
                                <span class="link">
                                  {{ item.deduction_amount || "-" }}
                                </span>
                              </div>
                            </template>
                            <a-list v-else />
                          </div>
                          <template v-if="temporaryUseableListData.left_amount">
                            <a-divider />
                            <div class="popoverCardDetail-footer" v-if="temporaryUseableListData.left_amount">
                              剩余：
                              {{ temporaryUseableListData.left_amount || "-" }}
                            </div>
                            <a-divider />
                          </template>
                        </div>
                      </div>
                    </popover>
                  </span>
                </div>
                <div class="cell">
                  <span class="title">预约备注</span>
                  <span>
                    <self-tooltip :text="storeViewForEditData.plan_remark || '-'" :explainLength="20" />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 收费信息 -->
        <price slot="right-price" :form="form" @confirmPrice="confirmPrice" :price="storeViewForEditData.pay_amount" :goods_list="storeViewForEditData.projects" :group_amount="storeViewForEditData.group_amount"></price>
      </orderLayout>
      <couponPayment ref="couponPayment" :storeViewForEditData="storeViewForEditData" @groupPurchaseInformation="groupPurchaseInformation" />
      <!-- 赠品 -->
      <giftListForModal ref="giftListForModal" />
      <!-- 门店老师 -->
      <storesTheTeacher
        ref="storesTheTeacher"
        :visible="isTeacherModalVisible"
        @close="isTeacherModalVisible = false"
        :storeViewForSubmitData="storeViewForSubmitData"
        :salesOfTheTeacher="salesOfTheTeacher"
        :operatingTheTeacher="operatingTheTeacher"
        :orderType="orderType"
        :teacherList_="teacherList"
        :teacher_selact_key="teacher_selact_key"
        :loading="storesTheTeacherLoading"
        @teacherOrderChange="teacherOrderChange"
        @setTeacher="(e) => inputChange(e, true)"
        @allSetTeacher="allSetTeacher"
        @teacherConfirm="teacherConfirm"
        @search="search"
        v-if="showStoresTheTeacher"
      />
    </a-form>
    <!-- 新增的结单备注模态框 -->
    <a-modal
      :visible="isRemarkModalVisible"
      @ok="handleRemarkModalOk"
      @cancel="handleRemarkModalCancel"
      :maskClosable="true"
      centered
      width="600px"
    >
      <div style="margin-bottom: 16px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">年龄<span style="color: red;">*</span></label>
        <a-select
          v-model="age_bracket_modal_input"
          placeholder="请选择年龄段"
          style="width: 100%;"
          :allowClear="true"
        >
          <a-select-option 
            v-for="item in ageBracketOptions" 
            :key="item.id" 
            :value="item.id"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
        <div v-if="ageBracketError" style="color: red; margin-top: 5px;">{{ ageBracketError }}</div>
      </div>
      
      <div style="margin-bottom: 16px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">性别<span style="color: red;">*</span></label>
        <a-select
          v-model="gender_modal_input"
          placeholder="请选择性别"
          style="width: 100%;"
        >
          <a-select-option :value="1">男性</a-select-option>
          <a-select-option :value="2">女性</a-select-option>
        </a-select>
        <div v-if="genderError" style="color: red; margin-top: 5px;">{{ genderError }}</div>
      </div>

      <template slot="title">
        结单备注（<span style="color: red;">记录客户购买了什么，做了什么，留下什么等内容</span>）
      </template>
      <div style="margin-bottom: 16px;">
        <label style="display: block; margin-bottom: 5px; font-weight: bold;">结单备注<span style="color: red;">*</span></label>
        <textarea
          cols="30"
          rows="5"
          placeholder="请输入结单备注"
          style="width: 100%; resize: none; border: 1px solid #d9d9d9; border-radius: 4px; padding: 5px;"
          v-model="settlement_remark_modal_input"
        ></textarea>
        <div v-if="remarkError" style="color: red; margin-top: 5px;">{{ remarkError }}</div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import {
  goodsUnion,
  orderStore,
  cardsSales,
  orderType,
  storedValueCard,
  customer,
} from "@/api/api";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import orderLayout from "../components/orderLayout/orderLayout.vue";
import price from "../components/orderLayout/price.vue";
import list from "../components/orderLayout/list.vue";
import card from "../components/orderLayout/remarks/card.vue";
import popover from "./popover.vue";
import goodsListComponents from "../components/orderLayout/remarks/goodsList.vue";
import couponPayment from "./couponPayment.vue";
import giftListForModal from "./giftListForModal.vue";
import storesTheTeacher from "../components/orderLayout/remarks/storesTheTeacher.vue";
import testData from "./testData.js";
import dataTemplate from "./dataTemplate.js";
const goodsList = "/view/goods-union/index";
const haveToBuy = "/customer-product/enable-list";
import { uuid } from "vue-uuid";
import moment from "moment";
import md5 from "js-md5";

export default {
  name: "billingCashier",
  mixins: [JeecgListMixin],
  components: {
    orderLayout,
    price,
    list,
    card,
    popover,
    goodsListComponents,
    couponPayment,
    giftListForModal,
    storesTheTeacher,
  },
  data() {
    return {
      storedValueCard,
      teacher_selact_key: "_STORETEACHER_",
      order_type_id: null,
      test: false,
      shouListSpin: false,
      shouList: true,
      listShow: true,
      uuid: uuid.v1(),
      form: this.$form.createForm(this),
      aBulk: {},
      value: "",
      order_id: "",
      settlement_remark: "",
      remarks: "",
      cardValue: "",
      verificationCodeValue: "",
      loading: true,
      listDisabled: true,
      storesTheTeacherLoading: false,
      userinfo: {},
      currentUserInfo: {},
      storeViewForEditData: {},
      storeViewForSubmitData: {},
      tagList: [
        {
          id: "",
          name: "常用商品",
        },
      ],
      // end
      statementInformation: true,
      informationDisabled: true,
      columns: [],
      salesOfTheTeacher: [],
      operatingTheTeacher: [],
      orderType: [],
      teacherList: [],
      selfDefaultTime: {},
      record: void 0,
      group_exportData: void 0,
      disableMixinCreated: true,
      queryParam: {
        cate_id: "",
        cus_id: "",
        keyword: "",
        store_id: this.$store.getters.store.id,
      },
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function (total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        },
      },
      priceType: ["price_one", "price_two", "price_three"],
      url: {
        list: goodsList,
      },
      changeSmallData: [],
      oldValue: null,
      useableListQuery: {},
      useableListData: {},
      temporaryUseableListData: {},
      createOrderId: "",
      showStoresTheTeacher: true,
      isEditPrice: false,
      isTeacherModalVisible: false,
      isRemarkModalVisible: false,
      settlement_remark_modal_input: "",
      remarkError: "",
      ageBracketOptions: [],
      age_bracket_modal_input: null,
      ageBracketError: "",
      gender_modal_input: 2,
      genderError: "",
    };
  },
  computed: {
    permissionLists: function () {
      return this.$store.getters.permissionLists;
    },
    dataSourceFormat: function () {
      if (this.dataSource.nodeDomType == 1) {
        let d = Object.assign([], this.dataSource.list);
        this.ipagination.total = parseInt(this.dataSource.totalCount);
        console.log(this.dataSource);
        return d;
      } else {
        return [];
      }
    },
    alreadyBoughtList: function () {
      if (this.dataSource.nodeDomType == 2) {
        let d = Object.assign([], this.dataSource.list);
        this.ipagination.total = parseInt(this.dataSource.totalCount);
        return d;
      } else {
        return [];
      }
    },
    validatorRules: function () {
      return {
        price: (e) => {
          return {
            rules: [
              {
                required: true,
                validator: (rule, value, cbfn) => {
                  let possibility = ["0", "0.00", "0.0"];
                  const item = JSON.parse(JSON.stringify(e));
                  const priceType = JSON.parse(JSON.stringify(this.priceType));
                  let afterData = [];
                  afterData = priceType.reduce((group, curP, index) => {
                    if (group.length == 0) group.push(item[curP]);
                    else {
                      let lastIndex = group.length - 1;
                      group[lastIndex] < item[curP] && group.push(item[curP]);
                    }
                    return group;
                  }, []);
                  if (!value && possibility.some((item) => item == value)) {
                    cbfn("请输入商品价格");
                  } else if (
                    item.price_type == 2 &&
                    !afterData
                      .map((l) => {
                        return this.$operation.mulPrecision(l, item.num);
                      })
                      .some((item) => item == value)
                  ) {
                    cbfn("请输入指定价格");
                  } else if (
                    item.price_type == 1 &&
                    (value < item.price_one * item.num ||
                      value > item.price_two * item.num)
                  ) {
                    cbfn("请输入区间内价格");
                  }
                  cbfn();
                },
              },
            ],
          };
        },
        use_num: (e) => {
          return {
            rules: [
              {
                required: true,
                validator: (rule, value, cbfn) => {
                  const item = JSON.parse(JSON.stringify(e));
                  if (!value && value !== 0) {
                    cbfn("请输入使用数量");
                  } else if (
                    item.hasOwnProperty("max_times") &&
                    item.max_times < value
                  ) {
                    cbfn("不能大于可用数量");
                  } else if (item.num < value) {
                    cbfn("不能大于可用数量");
                  }
                  cbfn();
                },
              },
            ],
          };
        },
      };
    },
  },
  async activated() {
    if (this.storeViewForSubmitData.id) {
      await this.storeViewForEdit({ id: this.storeViewForSubmitData.id });
      this.showStoresTheTeacher = false;
      await this.$asyncNextTick();
      this.showStoresTheTeacher = true;
      this.$forceUpdate();
    }
    this.$store.commit("SET_TEMPORARYROUTERCACHE", []); // 缓存当前页面
  },
  async created() {
    this.goodsCategorySelect();
    this.loadData(1, { nodeDomType: 1 });
    let routerQuery = this.$route.params.record;
    let settlementType = this.$route.params.settlementType;
    console.log(routerQuery);
    if (this.test) {
      routerQuery = {
        id: 880,
      };
    }
    if (routerQuery && routerQuery.id) {
      let { cus_avatar, id, cus_id, cus_mobile, cus_name, product_num } =
        routerQuery;
      this.record = {
        avatar: cus_avatar,
        id: cus_id,
        // cus_id: cus_id,
        mobile: cus_mobile,
        // mobile_code: "86",
        name: cus_name,
        order_id: id,
        product_num,
      };
      this.order_id = id;
      this.queryParam.cus_id = cus_id;
      
      // 获取完整的客户信息（包括年龄段和性别）
      await this.getCustomerCompleteInfo(this.record);
      
      this.layoutEvent("exportData", this.record);
      if (settlementType == "settlementOrder") {
        await this.storeViewForEdit({ id });
      }
      this.getOrderType(this.storeViewForSubmitData.order_type_id);
      this.listDisabled = false;
    } else this.getOrderType();
    this.loadAgeBracketOptions();
  },
  mounted() {
    window.onfocus = () => {
      this.oldValue = null;
    };
  },
  methods: {
    search(e){
      this.$newDebounce(() => {
        this.getTeacherList({id: this.order_type_id}, e)
        this.$forceUpdate()
      })
    },
    isDialog(call) {
      if (this.isEditPrice) {
        this.$confirm({
          title: '提示',
          content: '添加、删除、修改商品将会重新计算商品总价',
          onOk: () => {
            call()
            this.isEditPrice = false
          },
          onCancel: () => this.resetUseableListData(),
        })
      } else call()
    },
    confirmPrice(data) {
      this.storeViewForSubmitData.pay_amount = data;
      this.storeViewForSubmitData.amount = data;
      this.storeViewForSubmitData.payamount_modify = 1;
      this.isEditPrice = true;
      this.refactoringData();
      this.$forceUpdate();
    },
    //  重置储值卡信息
    resetUseableListData() {
      this.useableListData = {};
      this.temporaryUseableListData = {};
      this.sumPay_amount();
    },
    //  储值卡是否可用
    getUseableListDisable() {
      if (
        !this.storeViewForSubmitData ||
        JSON.stringify(this.storeViewForSubmitData) == "{}"
      ) {
        return true;
      }
      if (
        !this.storeViewForSubmitData.projects ||
        (this.storeViewForSubmitData.projects &&
          this.storeViewForSubmitData.projects.length == 0)
      ) {
        return true;
      }
      return false;
    },
    //  储值卡选择事件
    exportData(e) {
      this.temporaryUseableListData = e || {};
    },
    //  储值卡确认事件
    confirmPopover() {
      this.useableListData = this.temporaryUseableListData;
      this.sumPay_amount();
    },
    //  储值卡列表加载
    openPopover() {
      if (
        !this.storeViewForSubmitData ||
        JSON.stringify(this.storeViewForSubmitData) == "{}"
      ) {
        return this.$message.error("请选择用户");
      }
      if (
        !this.storeViewForSubmitData.projects ||
        (this.storeViewForSubmitData.projects &&
          this.storeViewForSubmitData.projects.length == 0)
      ) {
        return this.$message.error("请选商品");
      }
      let storeViewForSubmitData = JSON.parse(
        JSON.stringify(this.storeViewForSubmitData)
      );
      // 已购项目不传值
      storeViewForSubmitData.projects = storeViewForSubmitData.projects.filter(
        (item) => !item.customer_product_id
      );
      this.useableListQuery = {
        goods_list: storeViewForSubmitData.projects.map((item) => {
          return {
            goods_type: item.goods_type,
            goods_id: item.goods_id,
            price: item.goods_price,
            goods_name: item.goods_name,
          };
        }),
        cus_id: storeViewForSubmitData.cus_id,
      };
      this.$nextTick(() => {
        this.$refs.useableList.promotePerson();
      });
    },
    deleteBulk() {
      this.storeViewForSubmitData.group_amount = 0;
      this.storeViewForSubmitData.group_code = "";
      this.storeViewForSubmitData.group_platform_id = "";
      this.storeViewForSubmitData.group_platform_name = "";
      this.group_exportData = {};
      this.sumPay_amount();
      this.$forceUpdate();
    },
    set_tlement_note() {
      this.storeViewForSubmitData.settlement_remark = this.settlement_remark;
      this.refactoringData();
    },
    async teacherConfirm(call) {
      this.storesTheTeacherLoading = true;
      try {
        const res = await this.statement();
        this.storeViewForSubmitData.id = res;
        this.$store.commit("SET_TEMPORARYROUTERCACHE", [this.$options.name]); // 缓存当前页面
        this.$nextTick(() => {
          this.$router.push({
            path:
              "/storeWorkbench/payReceive/payReceive/" +
              this.$CryptoEncrypt(`${this.$options.name}-` + res),
          });
          this.storesTheTeacherLoading = false;
          call("1");
        });
      } catch (error) {
        this.storesTheTeacherLoading = false;
        call("2");
      }
    },
    allSetTeacher({ value, key, reset = false }) {
      let projects = JSON.parse(
        JSON.stringify(this.storeViewForSubmitData.projects)
      );
      let data = [];
      const func = (projects, resData) => {
        if (Array.isArray(projects) && projects.length > 0) {
          projects.forEach((v, i) => {
            // if (v.hasOwnProperty(key)) {
            console.log(`%c 老师选择 变化了 ${key} `, "color:red");
            if (!reset) {
              v[key] = value;
            } else {
              for (let key in v) {
                if (key.includes(this.teacher_selact_key)) delete v[key];
              }
            }
            // }
            resData[i] = v;
            let arr = [];
            func(v.goods_list, arr);
            resData[i].goods_list = arr;
          });
        }
      };
      func(projects, data);
      this.storeViewForSubmitData.projects = JSON.parse(JSON.stringify(data));
      console.log(this.storeViewForSubmitData.projects);
      this.refactoringData();
      this.$forceUpdate();
    },
    setTeacher(e) {
      console.log(e);
    },
    async teacherOrderChange(e) {
      let { id } = e;
      this.storesTheTeacherLoading = true;
      this.storeViewForSubmitData.order_type_id = id;
      this.refactoringData();
      this.allSetTeacher({ reset: true });
      await this.getTeacherList(e);
    },
    async getOrderType(id) {
      let query = {};
      this.orderType = await new Promise((resolve) => {
        orderType.select({ status: 1 }).then((res) => {
          if (res.code == 200) {
            resolve(res.data);
          }
        });
      });
      //   编辑时通过  order_type_id 获取所在tab 否则默认拿列表第一条
      if (id) {
        query = await new Promise((resolve) => {
          orderType.view({ id }).then((res) => {
            if (res.code == 200) {
              resolve(res.data.info);
            } else resolve(this.orderType[0]);
          });
        });
      } else {
        query = this.orderType[0];
      }
      this.storeViewForSubmitData.order_type_id = query.id;
      this.refactoringData();
      this.getTeacherList(query || {});
    },
    async getTeacherList({ id },keyword) {
      if(!this.order_type_id) {
        this.order_type_id = id
      }
      cardsSales
        .storeList({
          keyword,
          order_type_id: id,
          store_id: this.$store.getters.store.id,
        })
        .then((res) => {
          if (res.code == 200) {
            this.teacherList = res.data;
            console.log(res.data);
          }
        })
        .finally(() => {
          this.storesTheTeacherLoading = false;
        });
    },
    deleteItem(index) {
      const active = () => {
        this.storeViewForEditData.projects.splice(index, 1);
        this.storeViewForSubmitData.projects.splice(index, 1);
        this.resetUseableListData();
        this.sumPay_amount();
      }
      this.isDialog(() => active())
    },
    choosePackage(e) {
      if (JSON.stringify(this.storeViewForSubmitData) == "{}") {
        return this.$message.error("请选择结算订单或完善客户信息");
      }
      if (
        this.storeViewForSubmitData.projects.some(
          (item) => item.goods_id == e.goods_id
        )
      ) {
        return this.$message.warning("商品已选");
      }
      const active = () => {
        let data = [];
        const func = (projects, resData) => {
          if (Array.isArray(projects) && projects.length > 0) {
            projects.forEach((v, i) => {
              if (v.goods_type != 3) {
                v.sale_teacher_ids = [];
                v.operation_teacher_ids = [];
              }
              v.uuid = this.onlyData();
              resData[i] = v;
              let arr = [];
              func(v.goods_list, arr);
              resData[i].goods_list = arr;
            });
          }
        };
        func(e.goods_list, data);
        e.uuid = this.onlyData();
        e.sale_teacher_ids = [];
        e.operation_teacher_ids = [];
        e.goods_list = data;
        this.storeViewForSubmitData.projects.push(JSON.parse(JSON.stringify(e)));
        this.sumPay_amount();
        this.refactoringData();
      }
      this.isDialog(() => active())
    },
    sumPay_amount() {
      let increaseInAdvancePrice = 0;
      let storeViewForSubmitData = JSON.parse(
        JSON.stringify(this.storeViewForSubmitData)
      );
      const func = (projects) => {
        projects.forEach((item) => {
          if (item.goods_type == 3) {
            increaseInAdvancePrice = this.$operation.addPrecision(
              item.goods_price,
              increaseInAdvancePrice
            );
          } else if (item.goods_price) {
            increaseInAdvancePrice = this.$operation.addPrecision(
              item.goods_price,
              increaseInAdvancePrice
            );
            func(item.goods_list);
          }
        });
      };
      func(storeViewForSubmitData.projects);
      // if (storeViewForSubmitData.deposit) {
      //   increaseInAdvancePrice = this.$operation.subPrecision(
      //     increaseInAdvancePrice,
      //     storeViewForSubmitData.deposit
      //   );
      // }
      //    团购
      if (storeViewForSubmitData.group_amount) {
        increaseInAdvancePrice = this.$operation.subPrecision(
          increaseInAdvancePrice,
          storeViewForSubmitData.group_amount
        );
      }
      //    储值卡
      if (
        this.useableListData &&
        JSON.stringify(this.useableListData) != "{}"
      ) {
        increaseInAdvancePrice = this.$operation.subPrecision(
          increaseInAdvancePrice,
          this.useableListData.deduction_amount
        );
      }
      this.storeViewForSubmitData.pay_amount = increaseInAdvancePrice;
      this.storeViewForSubmitData.payamount_modify = 0;
      this.refactoringData();
      this.$forceUpdate();
    },
    counterChange({ counter, old }, index) {
      this.storeViewForSubmitData.projects[index].num = counter;
      let goods_price = this.$operation.mulPrecision(
        this.$operation.divPrecision(
          this.storeViewForSubmitData.projects[index].goods_price,
          old
        ),
        counter
      );
      this.storeViewForSubmitData.projects[index].goods_price =
        goods_price.toFixed(2);
      this.resetUseableListData();
      this.sumPay_amount();
    },
    refactoringData(key) {
      this.storeViewForEditData = JSON.parse(
        JSON.stringify(this.storeViewForSubmitData)
      );
      //   this.$nextTick(() => {
      setTimeout(async () => {
        this.storeViewForEditData.projects &&
          this.storeViewForEditData.projects.forEach((item, index) => {
            this.form.setFieldsValue({
              [`storeViewForEditData.projects[${index}]`]: item.goods_price,
              [`singleUse.projects[${index}]`]: item.use_num,
            });
          });
        await this.$asyncNextTick();
        this.form.validateFields((err, values) => { });
      }, 50);
      //   });
    },
    priceScopeOf(e, num, type) {
      let data = [];
      this.priceType.forEach((i) => {
        if (e[i] && e[i] != 0) {
          data.push(e[i]);
        }
      });
      if (type == 2) {
        //    指定价格
        return data.map((l) => this.$operation.mulPrecision(l, num)).join("、");
      } else {
        //    价格范围
        let max = data.sort().reverse()[0];
        let min = data.sort()[0];
        if (min == max) return this.$operation.mulPrecision(min, num);
        return `${this.$operation.mulPrecision(
          min,
          num
        )}~${this.$operation.mulPrecision(max, num)}`;
      }
    },
    inputChange({ uuid, value, key = "use_num", decimal = false }, bol) {
      if (!value && value != 0) return;
      const active = () => {
        this.$debounce(
          () => {
            let projects = JSON.parse(
              JSON.stringify(this.storeViewForSubmitData.projects)
            );
            let data = [];
            const func = (projects, resData) => {
              if (Array.isArray(projects) && projects.length > 0) {
                projects.forEach((v, i) => {
                  if (v.uuid == uuid) {
                    v[key] = value;
                  }
                  resData[i] = v;
                  let arr = [];
                  func(v.goods_list, arr);
                  resData[i].goods_list = arr;
                });
              }
            };
            func(projects, data);
            this.storeViewForSubmitData.projects = data;
            !bol && this.resetUseableListData();
            this.sumPay_amount();
          },
          {
            timeName: uuid,
            time: 100,
          }
        );
      }
      this.isDialog(() => active())
    },
    changeSmall(e) {
      let projects = JSON.parse(
        JSON.stringify(this.storeViewForSubmitData.projects)
      );
      let data = [];
      let childData = [];
      const func = (projects, resData, selectSwitch) => {
        if (Array.isArray(projects) && projects.length > 0) {
          projects.forEach((v, i) => {
            if (selectSwitch) {
              if (v.uuid == e.is_select_id) {
                console.log(
                  `%c下拉框 正选了 ==> ${e.select_index} --- ${v.goods_name}`,
                  "color:red"
                );
                v.is_select = true;
                v.is_select_uuid = e.self_uuid;
                v.select_index = e.select_index;
              }
              if (v.uuid == e.old_select_id) {
                console.log(
                  `%c下拉框 反选了 旧的数据==>${v.goods_name}`,
                  "color:red"
                );
                func(v.goods_list, childData, false);
                v.goods_list = JSON.parse(JSON.stringify(childData));
                v.is_select = false;
                v.is_select_uuid = "";
                v.select_index = "";
              }
            } else {
              console.log(
                `%c下拉框 反选了 包含的旧的子数据==>${v.goods_name}`,
                "color:red"
              );
              v.is_select = false;
              v.is_select_uuid = "";
              v.select_index = "";
            }
            resData[i] = v;
            let arr = [];
            func(v.goods_list, arr, selectSwitch);
            resData[i].goods_list = arr;
          });
        }
      };
      func(projects, data, true);
      this.storeViewForSubmitData.projects = JSON.parse(JSON.stringify(data));
      this.refactoringData();
      this.$forceUpdate();
    },
    groupPurchaseInformation(e) {
      this.storeViewForSubmitData.group_amount = e.couponCodeValue;
      this.storeViewForSubmitData.group_code = e.verificationCodeValue;
      this.storeViewForSubmitData.group_platform_id = e.id;
      this.storeViewForSubmitData = JSON.parse(
        JSON.stringify(this.storeViewForSubmitData)
      );
      this.group_exportData = e.exportData;
      this.sumPay_amount();
      this.refactoringData();
    },
    callback(e) {
      switch (e) {
        case "1":
          this.url.list = goodsList;
          this.loadData(1, { nodeDomType: 1 });
          break;
        case "2":
          this.url.list = haveToBuy;
          this.loadData(1, { nodeDomType: 2 });
          break;
      }
    },
    changeTag(e) {
      this.queryParam.cate_id = e.id;
      this.refactoringData();
      this.loadData(1, { nodeDomType: 1 });
    },
    goodsCategorySelect(cat_type) {
      //  获取商品分类
      goodsUnion.goodsEnableTopCategoryList().then((res) => {
        if (res.code == 200) {
          this.tagList = [...this.tagList, ...res.data];
        }
      });
    },
    addRemarksFortheGifts() {
      this.$refs.giftListForModal.visible = true;
    },
    open() {
      this.statementInformation = !this.statementInformation;
    },
    async layoutEvent(e, data) {
      switch (e) {
        case "confirm":
          try {
            await this.check();
          } catch (error) {
            return;
          }
          if (this.storeViewForSubmitData.goods_price < 0) {
            return this.$message.warning("价格异常");
          }
          this.settlement_remark_modal_input = this.storeViewForSubmitData.settlement_remark || "";
          
          // 设置年龄段和性别的默认值（从当前客户信息中获取）
          if (this.currentUserInfo && this.currentUserInfo.cus_age_bracket && this.currentUserInfo.cus_age_bracket !== 0) {
            this.age_bracket_modal_input = this.currentUserInfo.cus_age_bracket;
          } else {
            this.age_bracket_modal_input = null;
          }
          
          if (this.currentUserInfo && this.currentUserInfo.cus_gender && this.currentUserInfo.cus_gender !== 0) {
            this.gender_modal_input = this.currentUserInfo.cus_gender;
          } else {
            this.gender_modal_input = 2; // 默认女性
          }
          
          this.isRemarkModalVisible = true;
          break;
        case "exportData":
          this.currentUserInfo = data || {};
          this.queryParam.cus_id = data ? data.id : "";
          console.log(data);
          if (data) {
            this.listDisabled = false;
            let setDataTemplate = Object.assign(dataTemplate, {
              cus_id: data.id,
              store_id: this.$store.getters.store.id,
              order_type_id: this.storeViewForSubmitData.order_type_id,
            });
            this.storeViewForEditData =
              this.$utils.cloneObject(setDataTemplate);
            this.storeViewForSubmitData =
              this.$utils.cloneObject(setDataTemplate);
            
            // 设置年龄段和性别的默认值
            if (data.cus_age_bracket && data.cus_age_bracket !== 0) {
              this.age_bracket_modal_input = data.cus_age_bracket;
              this.storeViewForSubmitData.cus_age_bracket = data.cus_age_bracket;
            }
            if (data.cus_gender && data.cus_gender !== 0) {
              this.gender_modal_input = data.cus_gender;
              this.storeViewForSubmitData.cus_gender = data.cus_gender;
            }
            
            this.shouList = false;
            this.$nextTick(() => {
              this.shouList = true;
            });
            this.informationDisabled = false;
          } else {
            this.listDisabled = true;
            this.storeViewForEditData = {};
            this.storeViewForSubmitData = {};
            this.informationDisabled = true;
          }
          this.listShow = false;
          this.$nextTick(() => {
            this.listShow = true;
          });
          break;
        case "settlement":
          this.storeViewForEdit(data);
          this.record.order_id = data.id;
          break;
        case "cancel":
          await this.check();
          await this.statement();
          this.$router.push({ name: "预约到店" });
          break;
      }
    },
    check() {
      return new Promise((resolve, reject) => {
        if (
          !this.storeViewForSubmitData ||
          JSON.stringify(this.storeViewForSubmitData) == "{}"
        ) {
          this.$message.error("请选择结算单");
          return reject();
        }
        if (
          !this.storeViewForSubmitData.projects ||
          this.storeViewForSubmitData.projects.length == 0
        ) {
          this.$message.error("请选择项目");
          return reject();
        }
        this.form.validateFields((err, values) => {
          if (!err) {
            resolve();
          } else {
            this.$message.error("请完善信息");
            this.$scrollIntoView();
            // reject();
          }
        });
      });
    },
    statement() {
      let storeViewForSubmitData = JSON.parse(
        JSON.stringify(this.storeViewForSubmitData)
      );
      let data = [];
      const func = (projects, resData) => {
        if (Array.isArray(projects) && projects.length > 0) {
          projects.forEach((v, i) => {
            v.teachers_list = [];
            if (v.hasOwnProperty("goods_price")) {
              v.goods_price = v.goods_price / v.num;
            }
            if (this.useableListData.detail && this.useableListData.detail) {
              let item = this.useableListData.detail.find(
                (item) =>
                  item.goods_type == v.goods_type && item.goods_id == v.goods_id
              );
              if (item) {
                v.recharge_discount = item.discount;
                v.recharge_amount = item.deduction_amount;
              }
            }
            //  将每个类型底下的老师加入到teacher_lsit
            for (let key in v) {
              if (key.includes(this.teacher_selact_key))
                v.teachers_list.push(v[key]);
            }
            resData[i] = v;
            let arr = [];
            func(v.goods_list, arr);
            resData[i].goods_list = arr;
          });
        }
      };
      if (this.useableListData) {
        storeViewForSubmitData.cus_recharge_id = this.useableListData.id;
      }
      func(storeViewForSubmitData.projects, data);
      storeViewForSubmitData.projects = data;
      storeViewForSubmitData.cus_recharge_id;
      let requestFun = null;
      if (storeViewForSubmitData.id) {
        requestFun = orderStore.storeSettlement;
      } else {
        requestFun = orderStore.storeCreate;
      }
      return new Promise((resolve, reject) => {
        requestFun(storeViewForSubmitData)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message);
              let id = null;
              if (storeViewForSubmitData.id) {
                id = this.storeViewForSubmitData.id;
              } else {
                id = res.data;
              }
              resolve(id);
            } else {
              this.$message.error(res.message);
              reject(res);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    testData_() {
      let data = [];
      let testData_ = JSON.parse(JSON.stringify(testData));
      const func = (projects, resData) => {
        if (Array.isArray(projects) && projects.length > 0) {
          projects.forEach((v, i) => {
            v.uuid = this.onlyData();
            resData[i] = v;
            let arr = [];
            func(v.goods_list, arr);
            resData[i].goods_list = arr;
          });
        }
      };
      func(testData_.projects, data);
      testData_.projects = data;
      this.storeViewForEditData = JSON.parse(JSON.stringify(testData_));
      this.storeViewForSubmitData = JSON.parse(JSON.stringify(testData_));
      this.refactoringData();
    },
    onlyData() {
      const uuidList = () => [uuid.v1(), uuid.v1(), uuid.v1(), uuid.v1()];
      return md5(uuidList().join("-") + moment().valueOf());
    },
    storeViewForEdit({ id }) {
      let data = [];
      this.shouListSpin = true;
      return new Promise((resolve) => {
        orderStore.storeViewForEdit({ id }).then((res) => {
          if (res.code == 200) {
            if (this.test) {
              res.data = JSON.parse(JSON.stringify(testData)).data;
            }
            this.storeViewForEditData = {};
            this.storeViewForSubmitData = {};
            const func = (projects, resData) => {
              if (Array.isArray(projects) && projects.length > 0) {
                projects.forEach((v, i) => {
                  v.uuid = this.onlyData();
                  if (
                    v.hasOwnProperty("goods_price") &&
                    v.hasOwnProperty("num")
                  ) {
                    v.goods_price = this.$operation
                      .mulPrecision(v.num, v.goods_price)
                      .toFixed(2);
                  }
                  if (v.teachers_list) {
                    v.teachers_list.forEach((teacher_item) => {
                      console.log(
                        "this.teacher_selact_key + teacher_item.teacher_type_idthis.teacher_selact_key + teacher_item.teacher_type_idthis.teacher_selact_key + teacher_item.teacher_type_id",
                        this.teacher_selact_key + teacher_item.teacher_type_id
                      );
                      v[
                        this.teacher_selact_key + teacher_item.teacher_type_id
                      ] = teacher_item;
                    });
                  }
                  resData[i] = v;
                  let arr = [];
                  func(v.goods_list, arr);
                  resData[i].goods_list = arr;
                });
              }
            };
            func(res.data.projects, data);
            console.log("datadatadatadatadatadata", data);
            res.data.projects = data;
            this.storeViewForEditData = JSON.parse(JSON.stringify(res.data));
            this.storeViewForSubmitData = JSON.parse(JSON.stringify(res.data));
            // 团购
            this.group_exportData = {
              name: res.data.group_platform_name,
            };
            //  储值卡
            if (
              res.data.recharge &&
              res.data.recharge.deduction_amount &&
              res.data.recharge.deduction_amount > 0
            ) {
              this.temporaryUseableListData = JSON.parse(
                JSON.stringify(res.data.recharge || {})
              );
              this.useableListData = JSON.parse(
                JSON.stringify(res.data.recharge || {})
              );
            }
            // --
            this.sumPay_amount();
            this.informationDisabled = false;
          }
          this.shouList = false;
          this.$nextTick(() => {
            this.shouList = true;
          });
          this.shouListSpin = false;
          resolve();
        });
      });
    },
    clear() {
      if (JSON.stringify(this.storeViewForSubmitData) == "{}") {
        return this.$message.error("请选择结算订单");
      }
      const active = () => {
        let storeViewForSubmitData = JSON.parse(
          JSON.stringify(this.storeViewForSubmitData)
        );
        storeViewForSubmitData.projects = [];
        this.storeViewForSubmitData = storeViewForSubmitData;
        this.resetUseableListData();
        this.sumPay_amount();
      }
      this.isDialog(() => active())
    },
    couponPayment() {
      this.$refs.couponPayment.aBulkVisible = true;
      this.$refs.couponPayment.applyColoursToADrawing();
    },
    onSearch({ keyword, action }) {
      this.queryParam.keyword = keyword;
      this.loadData(1, { nodeDomType: action });
    },
    // 新增：处理备注模态框的确定操作
    handleRemarkModalOk() {
      // 清空之前的错误信息
      this.remarkError = "";
      this.ageBracketError = "";
      this.genderError = "";

      // 验证结单备注
      if (!this.settlement_remark_modal_input || this.settlement_remark_modal_input.trim() === "") {
        this.remarkError = "结单备注不能为空";
        return;
      }

      // 验证年龄段
      if (!this.age_bracket_modal_input) {
        this.ageBracketError = "请选择年龄段";
        return;
      }

      // 验证性别
      if (!this.gender_modal_input) {
        this.genderError = "请选择性别";
        return;
      }

      // 将数据保存到提交对象中
      this.storeViewForSubmitData.settlement_remark = this.settlement_remark_modal_input;
      this.storeViewForSubmitData.cus_age_bracket = this.age_bracket_modal_input;
      this.storeViewForSubmitData.cus_gender = this.gender_modal_input;
      
      this.refactoringData();

      this.isRemarkModalVisible = false;

      // 弹出选择老师的弹层
      this.isTeacherModalVisible = true;
    },

    // 新增：处理备注模态框的取消操作
    handleRemarkModalCancel() {
      this.isRemarkModalVisible = false;
      this.remarkError = "";
      this.ageBracketError = "";
      this.genderError = "";
      // 重置表单数据
      this.settlement_remark_modal_input = this.storeViewForSubmitData.settlement_remark || "";
      
      // 恢复客户信息中的年龄段和性别默认值
      if (this.currentUserInfo && this.currentUserInfo.cus_age_bracket && this.currentUserInfo.cus_age_bracket !== 0) {
        this.age_bracket_modal_input = this.currentUserInfo.cus_age_bracket;
      } else {
        this.age_bracket_modal_input = null;
      }
      
      if (this.currentUserInfo && this.currentUserInfo.cus_gender && this.currentUserInfo.cus_gender !== 0) {
        this.gender_modal_input = this.currentUserInfo.cus_gender;
      } else {
        this.gender_modal_input = 2; // 默认女性
      }
    },
          // 加载年龄段选项
      async loadAgeBracketOptions() {
        try {
          const res = await cardsSales.getAgeBracket();
          if (res.code === 200 && res.data) {
            this.ageBracketOptions = res.data;
          }
        } catch (error) {
          console.error('获取年龄段选项失败:', error);
        }
      },

      // 获取完整的客户信息（包括年龄段和性别）
      async getCustomerCompleteInfo(customerRecord) {
        try {
          const res = await customer.view({
            id: customerRecord.id,
          });
          
          if (res.code === 200 && res.data && res.data.info) {
            // 将年龄段和性别信息添加到客户记录中
            // 注意：customer.view接口返回的是字符串，需要转换为数字
            if (res.data.info.age_bracket && res.data.info.age_bracket !== "0") {
              customerRecord.cus_age_bracket = parseInt(res.data.info.age_bracket, 10);
            }
            if (res.data.info.gender && res.data.info.gender !== "0") {
              customerRecord.cus_gender = parseInt(res.data.info.gender, 10);
            }
            
            console.log('获取到的客户完整信息:', {
              age_bracket: res.data.info.age_bracket,
              gender: res.data.info.gender,
              converted_age_bracket: customerRecord.cus_age_bracket,
              converted_gender: customerRecord.cus_gender
            });
          }
        } catch (error) {
          console.error('获取客户完整信息失败:', error);
        }
      },
  },
};
</script>
<style lang="less" scoped>
@import "./style.less";
.statementInformation {
  margin-top: 10px;
  > h5 {
    display: inline-block;
    cursor: pointer;
    color: @font-color-gray;
    margin: 0;
  }
  .buildingNo {
    margin-left: 5px;
    transform: rotate(-180deg);
  }
  .showStatementInformation {
    transform: rotate(0);
  }
  .buildingNo {
    transition: all 0.5s;
  }
  .originalstatementInformation {
    transition: all 0.5s;
    height: 0;
    overflow: hidden;
    .cell {
      display: flex;
      .title {
        display: inline-block;
        width: 85px;
        color: @font-color-gray;
      }
    }
  }
  .statementInformation-detail {
    height: 135px !important;
  }
  .statementInformation-box {
    background: @bg-color-component;
    overflow: hidden;
    display: grid;
    grid-row-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 10px;
    padding: 16px 24px;
    .price {
      color: red;
    }
    .link {
      cursor: pointer;
      color: #9373ee;
    }
  }
}
.couponsList,
.popoverCardList {
  height: 300px;
  overflow-y: auto;
  display: flex;
  flex-flow: column;
  /deep/ .ant-divider {
    margin: 5px 0;
  }
  .popoverCardDetail {
    flex: 1;
    width: 100%;
    height: 0;
    overflow: hidden;
    display: flex;
    flex-flow: column;
    &-list {
      flex: 1;
      width: 100%;
      height: 0;
      overflow-y: auto;
      &-cell {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .link {
          flex: 1;
          width: 0;
          text-align: right;
        }
      }
    }

    &-footer {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 10px;
    }
  }
  .popoverCardDetail-cell {
    display: flex;
    justify-content: space-between;
  }
}
.couponsList {
  .coupons {
    > div:first-child {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 18px;
      font-weight: bold;
    }
    .actionCard {
      color: @brand-color;
    }
    > div:last-child {
      display: flex;
      flex-flow: column;
      font-size: 13px;
      flex: 1;
      color: @font-color-gray;
      font-size: 12px;
      > div {
        margin-bottom: 2px;
      }
    }
  }
}
.cardList {
  .card {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
textarea {
  border: none;
  background: #fff;
  border-radius: 4px;
  padding: 6px;
  &:focus-visible {
    border: none;
    outline: none;
  }
  &:focus {
    border: none;
    outline: none;
  }
}
.theScopeOf {
  font-size: 14px;
  font-weight: 400;
  color: @font-color-gray;
  text-align: right;
}
.goodNum {
  display: inline-flex;
  align-items: flex-start;
  line-height: 40px;
  .billingCashierCounter {
    position: relative;
    top: -2px;
  }
  .inputNumberBox {
    position: relative;
    .priceSymbol {
      position: absolute;
      top: 50%;
      left: 5px;
      transform: translateY(-50%);
      z-index: 1;
      color: #333;
    }
  }
  /deep/ .action {
    width: 32px;
    height: 32px;
    border-radius: 3px;
    background: @bg-color-page;
    border: 1px solid #d9d9d9;
  }
  /deep/ .ant-input-group-addon {
    background: @bg-color-page;
    font-size: 16px;
    font-weight: bold;
  }
  .input {
    width: 150px;
    /deep/ input {
      text-align: right;
      font-weight: bold;
      font-size: 18px;
    }
  }
}
.goodNumagain {
  font-size: 12px;
  font-weight: 400;
  height: 100%;
  > div {
    line-height: initial;
    height: 100%;
    display: inline-flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
  }
}
.remarks {
  margin-top: 10px;
  padding: 0 24px;
  width: 100%;
  background: @bg-color-component;
  position: relative;
  &::before {
    display: inline-block;
    content: "";
    position: absolute;
    transform: translateX(-100%);
    top: 0;
    height: 100%;
    width: 100px;
    background: @bg-color-component;
  }
}
.remarksFortheGifts {
  background: #fff;
  &::before {
    display: none;
  }
}
.remarksFortheGiftsTitle {
  color: @font-color-gray;
}
.addRemarksFortheGifts {
  margin: 0;
  padding: 0;
  position: relative;
  top: -10px;
}
.packageDetails {
  background: #fff;
  padding: 10px 20px;
  margin: 10px 0;
}
.aBulkBox {
  position: relative;
  .circle {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(100%, -20%);
    color: @font-color-gray;
    font-size: 12px;
  }
}
.example {
  position: absolute;
  top: 0px;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  z-index: 999;
}
.resetTheBillingInfo {
  height: inherit;
  line-height: inherit;
  padding: 0;
}
.showBtn {
  .resetTheBillingInfo;
}
/deep/ .ant-form-inline .ant-form-item {
  margin-right: 0;
}
/deep/ .ant-form-item-with-help {
  margin: 0;
  font-weight: 400;
}
/deep/ .showBtn {
  .resetTheBillingInfo;
  /deep/ button {
    .resetTheBillingInfo;
  }
}
/deep/ .ant-list-empty-text {
  padding: 24px !important;
}
/deep/ .ant-input-number-handler-wrap {
  display: none;
}
</style>
