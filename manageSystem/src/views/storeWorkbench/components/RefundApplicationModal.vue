<template>
  <a-modal
    v-model="visible"
    :title="getModalTitle()"
    :width="1220"
    :maskClosable="true"
    @cancel="handleCancel"
    :footer="null"
    :destroyOnClose="true"
    class="refund-application-modal"
  >
    <div class="refund-modal-content">
      <div class="main-content">
        <!-- 左侧表单区域 -->
        <div class="left-section">
          
          <a-form-model 
            ref="refundForm" 
            :model="form" 
            :rules="rules" 
            :label-col="{ span: 5 }" 
            :wrapper-col="{ span: 19 }"
            class="refund-form"
          >
            <!-- 客户基本信息 -->
            <div class="info-group">
              <a-form-model-item label="客户姓名" class="info-display-item">
                <span class="info-value">{{ customerInfo.name }}</span>
              </a-form-model-item>
              <a-form-model-item label="客户电话" class="info-display-item">
                <span class="info-value">{{ customerInfo.phone }}</span>
              </a-form-model-item>
              <a-form-model-item label="门店名称" class="info-display-item">
                <span class="info-value">{{ customerInfo.storeName }}</span>
              </a-form-model-item>
            </div>

            <!-- 关联订单列表 -->
            <div class="order-table-section" v-if="selectedOrders.length > 0">
              <h4 class="table-title">关联订单</h4>
              <a-table
                :columns="orderTableColumns"
                :data-source="selectedOrderTableData"
                :pagination="false"
                size="small"
                rowKey="id"
                class="order-table"
              >
                <template slot="refund_amount" slot-scope="text, record">
                  <a-input-number
                    :value="record.refund_amount"
                    :min="0"
                    :precision="2"
                    placeholder="0.00"
                    size="small"
                    style="width: 120px"
                    @change="(value) => handleTableRefundAmountChange(record.id, value)"
                  />
                </template>
                <template slot="action" slot-scope="text, record">
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="handleRemoveOrderFromTable(record.id)"
                    style="color: #ff4d4f;"
                  >
                    删除
                  </a-button>
                </template>
              </a-table>
            </div>

            <!-- 实付金额 -->
            <a-form-model-item label="实付金额" class="refund-amount-item">
              <div class="refund-amount-display">
                <span class="amount-value">¥{{ totalOrderAmount.toFixed(2) }}</span>
              </div>
            </a-form-model-item>

            <!-- 退款信息 -->
            <a-form-model-item label="应退金额" class="refund-amount-item">
              <div class="refund-amount-display">
                <span class="amount-value refund-amount">¥{{ totalRefundAmount.toFixed(2) }}</span>
              </div>
            </a-form-model-item>
            
            <a-form-model-item label="退款原因" prop="refund_reason">
              <a-textarea 
                v-model="form.refund_reason" 
                placeholder="请详细说明退款原因"
                :rows="4"
                style="width: 100%"
              />
            </a-form-model-item>
            
            <a-form-model-item label="付款截图及退款协议照片" prop="attachment_urls" required class="multiline-label-item">
              <div class="upload-area">
                <j-image-upload 
                  hasInformation 
                  v-model="form.attachment_urls"
                  :isMultiple="true"
                  :max="8"
                  text="上传证明"
                  accept="image/jpeg,image/png"
                  :ImageSize="2048"
                  StorageDirectory="refund"
                  @change="handleAttachmentChange"
                />
                <div class="upload-tip">支持jpg、png格式，最多上传8张，单张不超过2MB</div>
              </div>
            </a-form-model-item>

            <!-- 服务老师字段 -->
            <a-form-model-item label="服务老师" prop="service_teacher_id" required>
              <div class="teacher-select-wrapper">
                <commone-self-principal
                  ref="teacherSelector"
                  searchKey="username"
                  :requestFun="planTeacherList"
                  placeholder="请选择服务老师"
                  value_key="username"
                  id_key="id"
                  v-model="form.service_teacher_id"
                  :defaultRendering="teacherDefaultRendering"
                  :isRequest="true"
                  style="width: 300px"
                  allow-clear
                  @change="handleTeacherChange"
                />
              </div>
            </a-form-model-item>

            <!-- 收款账户信息 -->
            <a-form-model-item label="收款账户信息" class="bank-info-section" required>
              <div class="bank-info-content">
                <a-form-model-item label="账户类型" prop="bank_account_type" class="bank-sub-item">
                  <a-select v-model="form.bank_account_type" placeholder="请选择账户类型" style="width: 200px">
                    <a-select-option value="个人">个人</a-select-option>
                    <a-select-option value="对公">对公</a-select-option>
                  </a-select>
                </a-form-model-item>

                <a-form-model-item label="户名" prop="bank_account_name" class="bank-sub-item">
                  <a-input v-model="form.bank_account_name" placeholder="请输入账户名称" style="width: 300px" />
                </a-form-model-item>
                
                <a-form-model-item label="卡号" prop="bank_account" class="bank-sub-item">
                  <a-input v-model="form.bank_account" placeholder="请输入卡号" style="width: 300px" />
                </a-form-model-item>

                <a-form-model-item label="银行" prop="bank_name" class="bank-sub-item">
                  <a-input v-model="form.bank_name" placeholder="请输入银行名称" style="width: 300px" />
                </a-form-model-item>

                <a-form-model-item label="银行所在地" prop="bank_location" required class="bank-sub-item">
                  <a-input v-model="form.bank_location" placeholder="请输入银行所在地" style="width: 300px" />
                </a-form-model-item>

                <a-form-model-item label="支行名称" prop="bank_branch" required class="bank-sub-item">
                  <a-input v-model="form.bank_branch" placeholder="请输入具体支行名称" style="width: 300px" />
                </a-form-model-item>
              </div>
            </a-form-model-item>
          </a-form-model>
        </div>
        
        <!-- 右侧订单列表 -->
        <div class="right-section">
          <div class="section-header">
            <h3 class="section-title">订单列表</h3>
          </div>
          
          <div class="order-cards">
            <a-spin :spinning="loading" class="order-spin-container">
              <div class="order-content-wrapper">
                <div class="empty-state" v-if="!loading && availableOrders.length === 0">
                  <a-empty description="暂无可退款订单" />
                </div>
                
                <div 
                  class="order-card" 
                  v-for="order in availableOrders" 
                  :key="order.id"
                >
              <div class="order-header">
                <span class="store-name">{{ order.store_name }}</span>
                <span class="order-no">{{ order.order_no }}</span>
              </div>
              
              <div class="order-content">
                <div class="service-info">
                  <a-tooltip :title="order.service_name || '服务套餐'" placement="top">
                    <a-tag color="purple" class="service-tag">{{ order.service_name || '服务套餐' }}</a-tag>
                  </a-tooltip>
                  <span class="price">¥{{ order.money }}</span>
                </div>
                <div class="order-date">
                  到店日期：{{ order.plan_time_text }}
                </div>
              </div>
              
              <!-- 添加按钮 -->
              <div class="add-order-action">
                <a-button 
                  v-if="!selectedOrders.includes(order.id)"
                  type="primary" 
                  size="small"
                  @click="handleAddOrderToTable(order.id)"
                  class="add-btn"
                >
                  添加
                </a-button>
                <a-button 
                  v-else
                  size="small"
                  disabled
                  class="added-btn"
                >
                  已添加
                </a-button>
              </div>
                </div>
              </div>
            </a-spin>
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="form-actions">
        <a-button @click="handleCancel" style="margin-right: 8px">
          取消
        </a-button>
        <a-button :loading="savingDraft" @click="handleSubmit(true)" style="margin-right: 8px">
          保存为草稿
        </a-button>
        <a-button type="primary" :loading="submitting" @click="handleSubmit(false)">
          提交退款申请
        </a-button>
      </div>
    </div>

  </a-modal>
</template>

<script>
import { refundApplicationApi, storeManage } from '@/api/api.js'
import JImageUpload from '@/components/jeecg/JImageUpload'

export default {
  name: 'RefundApplicationModal',
  components: {
    JImageUpload
  },
  data() {
    return {
      visible: false,
      submitting: false,
      savingDraft: false,
      loading: false,
      isEditMode: false, // 是否为编辑模式
      isResubmitMode: false, // 是否为重新发起模式
      editId: null, // 编辑的申请ID
      originalApplicationId: null, // 原申请ID（重新发起时使用）
      customerId: null,
      storeId: null, // 当前门店ID
      originalAttachments: [], // 存储原有的附件数据，用于保留feishu_code等信息
      customerInfo: {
        name: '',
        phone: '',
        storeName: ''
      },
      availableOrders: [], // 客户的所有可退款订单
      selectedOrders: [], // 选中的订单ID列表
      teacherDefaultRendering: {}, // 老师回显数据
      
      form: {
        refund_amount: 0,
        refund_reason: '',
        bank_account_type: undefined,
        bank_account: '',
        bank_account_name: '',
        bank_name: '',
        bank_location: '',
        bank_branch: '',
        attachment_urls: [],
        service_teacher_id: undefined // 服务老师ID
      },
      
      rules: {
        refund_reason: [
          { required: true, message: '请输入退款原因', trigger: 'blur' }
        ],
        attachment_urls: [
          { required: true, message: '请上传付款截图及退款协议照片', trigger: 'change' }
        ],
        bank_account: [
          { required: true, message: '请输入卡号', trigger: 'blur' }
        ],
        bank_account_name: [
          { required: true, message: '请输入账户名称', trigger: 'blur' }
        ],
        bank_name: [
          { required: true, message: '请输入银行名称', trigger: 'blur' }
        ],
        bank_location: [
          { required: true, message: '请输入银行所在地', trigger: 'blur' }
        ],
        bank_branch: [
          { required: true, message: '请输入支行名称', trigger: 'blur' }
        ],
        bank_account_type: [
          { required: true, message: '请选择账户类型', trigger: 'change' }
        ],
        service_teacher_id: [
          { required: true, message: '请选择服务老师', trigger: 'change' }
        ]
      },
      
      // 订单表格列定义
      orderTableColumns: [
        {
          title: '订单编号',
          dataIndex: 'order_no',
          key: 'order_no',
          width: 230
        },
        {
          title: '实付金额',
          dataIndex: 'money',
          key: 'money',
          width: 100,
          customRender: (text) => `¥${parseFloat(text || 0).toFixed(2)}`
        },
        {
          title: '退款金额',
          key: 'refund_amount',
          width: 140,
          scopedSlots: { customRender: 'refund_amount' }
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  
  computed: {
    // 选中的订单详情列表
    selectedOrderDetails() {
      return this.availableOrders.filter(order => 
        this.selectedOrders.includes(order.id)
      )
    },
    
    // 表格数据源
    selectedOrderTableData() {
      return this.selectedOrderDetails
    },
    
    // 订单号列表文本
    orderNosText() {
      return this.selectedOrderDetails.map(order => order.order_no).join(', ')
    },
    
    // 总订单金额（实付金额）
    totalOrderAmount() {
      return this.selectedOrderDetails.reduce((sum, order) => 
        sum + parseFloat(order.money || 0), 0
      )
    },
    
    // 总退款金额
    totalRefundAmount() {
      return this.selectedOrderDetails.reduce((sum, order) => 
        sum + parseFloat(order.refund_amount || 0), 0
      )
    },
    
    // 验证状态
    validationStatus() {
      if (this.selectedOrders.length === 0) {
        return { valid: false, message: '请选择要退款的订单' }
      }
      
      const invalidOrders = this.selectedOrderDetails.filter(order => 
        !order.refund_amount || order.refund_amount <= 0
      )
      if (invalidOrders.length > 0) {
        return { valid: false, message: `${invalidOrders.length}个订单未填写退款金额` }
      }
      
      if (this.totalRefundAmount <= 0) {
        return { valid: false, message: '总退款金额必须大于0' }
      }
      
      return { valid: true, message: '订单信息填写完整' }
    }
  },
  
  methods: {
    // 获取弹窗标题
    getModalTitle() {
      if (this.isResubmitMode) {
        return '重新发起退款申请'
      } else if (this.isEditMode) {
        return '编辑退款申请'
      } else {
        return '退款申请'
      }
    },
    
    // 打开弹窗
    async open(customerId, customerInfo = {}, defaultOrderId = null, storeId = null) {
      console.log('RefundApplicationModal.open called with:', { customerId, customerInfo, defaultOrderId, storeId });
      
      this.resetForm()
      this.customerId = customerId
      this.storeId = storeId
      // 使用Vue.set确保响应性
      this.$set(this, 'customerInfo', {
        name: customerInfo.name || '',
        phone: customerInfo.phone || '',
        storeName: customerInfo.storeName || ''
      })
      this.visible = true
      
      console.log('Modal visible set to true, visible:', this.visible);
      console.log('Customer info set:', this.customerInfo);
      console.log('Store ID set:', this.storeId);
      console.log('Customer info keys:', Object.keys(this.customerInfo));
      console.log('Customer info name:', this.customerInfo.name);
      console.log('Customer info phone:', this.customerInfo.phone);
      console.log('Customer info storeName:', this.customerInfo.storeName);
      
      // 注意：commone-self-principal 组件会自动处理老师列表加载
      
      if (customerId) {
        console.log('Loading refundable orders for customerId:', customerId, 'defaultOrderId:', defaultOrderId, 'storeId:', storeId);
        await this.loadRefundableOrders(customerId, defaultOrderId)
      }
    },
    
    // 打开编辑弹窗
    async openEdit(applicationId) {
      this.resetForm()
      this.isEditMode = true
      this.editId = applicationId
      this.visible = true
      
      // 等待弹窗渲染完成
      await this.$nextTick()
      
      try {
        this.loading = true
        
        // 获取申请详情
        const res = await refundApplicationApi.view({ id: applicationId })
        if (res.code === 200) {
          const data = res.data.info || res.data
          
          // 设置客户ID和门店ID
          this.customerId = data.cus_id || data.customer_id
          this.storeId = data.store_id
          
          // 填充客户信息
          this.customerInfo = {
            name: data.customer_name || '',
            phone: data.customer_phone || '',
            storeName: data.store_name || ''
          }
          
          // 填充表单数据
          const bankAccountInfo = data.bank_account_info || {}
          const attachmentUrls = data.attachment_urls_array || []
          
          // 保存原有附件数据，用于后续保留完整信息
          this.originalAttachments = attachmentUrls.slice() // 创建副本
          
          this.form = {
            refund_reason: data.refund_reason || '',
            bank_account_type: data.bank_account_type_text || '',
            bank_account: bankAccountInfo.account_no || '',
            bank_account_name: bankAccountInfo.account_name || '',
            bank_name: bankAccountInfo.bank_name || '',
            bank_location: bankAccountInfo.bank_location || '',
            bank_branch: bankAccountInfo.bank_branch || '',
            attachment_urls: attachmentUrls,
            service_teacher_id: undefined
          }
          
          // 设置服务老师ID和回显数据（编辑模式）
          if (data.teacher_id) {
            // 使用通用方法设置老师回显数据
            await this.setTeacherDefaultData(data.teacher_id, data.teacher_name)
          }
          
          // 处理订单详情 - 需要获取对应的可退款订单
          if (data.details && data.details.length > 0) {
            // 根据详情中的order_id重新加载对应订单的完整信息
            await this.loadRefundableOrdersForEdit(data.details)
          }
          
        } else {
          this.$message.error(res.message || '获取申请详情失败')
        }
      } catch (error) {
        console.error('获取申请详情失败:', error)
        this.$message.error('获取申请详情失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    // 打开重新发起弹窗
    async openResubmit(applicationId) {
      this.resetForm()
      this.isResubmitMode = true
      this.originalApplicationId = applicationId
      this.visible = true
      
      // 等待弹窗渲染完成
      await this.$nextTick()
      
      try {
        this.loading = true
        
        // 获取原申请详情
        const res = await refundApplicationApi.view({ id: applicationId })
        if (res.code === 200) {
          const data = res.data.info || res.data
          
          // 设置客户ID和门店ID
          this.customerId = data.cus_id || data.customer_id
          this.storeId = data.store_id
          
          // 填充客户信息
          this.customerInfo = {
            name: data.customer_name || '',
            phone: data.customer_phone || '',
            storeName: data.store_name || ''
          }
          
          // 填充表单数据
          const bankAccountInfo = data.bank_account_info || {}
          const attachmentUrls = data.attachment_urls_array || []
          
          // 保存原有附件数据，用于后续保留完整信息
          this.originalAttachments = attachmentUrls.slice() // 创建副本
          
          this.form = {
            refund_reason: data.refund_reason || '',
            bank_account_type: data.bank_account_type_text || '',
            bank_account: bankAccountInfo.account_no || '',
            bank_account_name: bankAccountInfo.account_name || '',
            bank_name: bankAccountInfo.bank_name || '',
            bank_location: bankAccountInfo.bank_location || '',
            bank_branch: bankAccountInfo.bank_branch || '',
            attachment_urls: attachmentUrls,
            service_teacher_id: undefined
          }
          
          // 设置服务老师ID和回显数据（重新发起模式）
          if (data.teacher_id) {
            // 使用通用方法设置老师回显数据
            await this.setTeacherDefaultData(data.teacher_id, data.teacher_name)
          }
          
          // 处理订单详情 - 需要获取对应的可退款订单
          if (data.details && data.details.length > 0) {
            // 根据详情中的order_id重新加载对应订单的完整信息
            await this.loadRefundableOrdersForResubmit(data.details)
          }
          
        } else {
          this.$message.error(res.message || '获取申请详情失败')
        }
      } catch (error) {
        console.error('获取申请详情失败:', error)
        this.$message.error('获取申请详情失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    // 编辑模式下加载相关订单
    async loadRefundableOrdersForEdit(details) {
      try {
        // 从申请详情中提取客户ID和门店ID
        const firstDetail = details[0]
        if (!firstDetail) return
        
        // 构建请求参数，使用与创建模式相同的API
        const params = {}
        
        // 优先使用组件状态中的ID（在openEdit中已设置）
        if (this.customerId) {
          params.cus_id = this.customerId
        } else if (firstDetail.cus_id) {
          // 如果组件状态中没有，再尝试从details中获取
          params.cus_id = firstDetail.cus_id
        }
        
        // 添加门店ID过滤
        if (this.storeId) {
          params.store_id = this.storeId
        } else if (firstDetail.store_id) {
          params.store_id = firstDetail.store_id
        }
        
        
        // 调用与创建模式相同的API获取所有可退款订单
        const res = await refundApplicationApi.refundableOrders(params)
        
        if (res.code === 200) {
          const orderList = (res.data && res.data.list) || res.data || []
          
          // 初始化所有订单数据
          this.availableOrders = orderList.map(order => ({
            ...order,
            refund_amount: 0, // 先初始化为0
            selected: false
          }))
          
          // 创建一个映射，用于快速查找编辑中的订单详情
          const editDetailsMap = new Map()
          details.forEach(detail => {
            // 使用订单号作为键，因为这是最可靠的匹配方式
            editDetailsMap.set(detail.order_no, detail)
          })
          
          // 标记并设置已关联的订单
          const selectedOrderIds = []
          this.availableOrders.forEach(order => {
            const editDetail = editDetailsMap.get(order.order_no)
            if (editDetail) {
              // 这个订单在编辑的申请中，标记为已选中
              selectedOrderIds.push(order.id)
              // 设置退款金额为编辑中的值
              order.refund_amount = parseFloat(editDetail.refund_amount || 0)
            }
          })
          
          // 设置选中的订单ID列表
          this.selectedOrders = selectedOrderIds
          
        } else {
          this.$message.error(res.message || '获取订单列表失败')
        }
        
      } catch (error) {
        this.$message.error('加载订单数据失败，请重试')
      }
    },
    
    // 重新发起模式下加载相关订单
    async loadRefundableOrdersForResubmit(details) {
      try {
        // 从申请详情中提取客户ID和门店ID
        const firstDetail = details[0]
        if (!firstDetail) return
        
        // 构建请求参数，使用与创建模式相同的API
        const params = {}
        
        // 优先使用组件状态中的ID（在openResubmit中已设置）
        if (this.customerId) {
          params.cus_id = this.customerId
        } else if (firstDetail.cus_id) {
          // 如果组件状态中没有，再尝试从details中获取
          params.cus_id = firstDetail.cus_id
        }
        
        // 添加门店ID过滤
        if (this.storeId) {
          params.store_id = this.storeId
        } else if (firstDetail.store_id) {
          params.store_id = firstDetail.store_id
        }
        
        
        // 调用与创建模式相同的API获取所有可退款订单
        const res = await refundApplicationApi.refundableOrders(params)
        
        if (res.code === 200) {
          const orderList = (res.data && res.data.list) || res.data || []
          
          // 初始化所有订单数据
          this.availableOrders = orderList.map(order => ({
            ...order,
            refund_amount: 0, // 先初始化为0
            selected: false
          }))
          
          // 创建一个映射，用于快速查找重新发起中的订单详情
          const resubmitDetailsMap = new Map()
          details.forEach(detail => {
            // 使用订单号作为键，因为这是最可靠的匹配方式
            resubmitDetailsMap.set(detail.order_no, detail)
          })
          
          // 标记并设置已关联的订单
          const selectedOrderIds = []
          this.availableOrders.forEach(order => {
            const resubmitDetail = resubmitDetailsMap.get(order.order_no)
            if (resubmitDetail) {
              // 这个订单在重新发起的申请中，标记为已选中
              selectedOrderIds.push(order.id)
              // 设置退款金额为原申请中的值
              order.refund_amount = parseFloat(resubmitDetail.refund_amount || 0)
            }
          })
          
          // 设置选中的订单ID列表
          this.selectedOrders = selectedOrderIds
          
        } else {
          this.$message.error(res.message || '获取订单列表失败')
        }
        
      } catch (error) {
        this.$message.error('加载订单数据失败，请重试')
      }
    },
    
    // 加载客户的可退款订单
    async loadRefundableOrders(customerId, defaultOrderId = null) {
      this.loading = true
      try {
        console.log('Starting to load refundable orders for customerId:', customerId, 'defaultOrderId:', defaultOrderId, 'storeId:', this.storeId)
        
        // 构建请求参数
        const params = { cus_id: customerId }
        if (this.storeId) {
          params.store_id = this.storeId
        }
        
        const res = await refundApplicationApi.refundableOrders(params)
        console.log('API response:', res)
        
        if (res.code === 200) {
          // 后端返回的数据结构可能是 { list: [], totalCount: 0 }
          const orderList = (res.data && res.data.list) || res.data || []
          console.log('Processed order list:', orderList)
          console.log('First order in list:', orderList[0])
          console.log('Looking for defaultOrderId:', defaultOrderId)
          
          this.availableOrders = orderList.map(order => {
            console.log('Processing order:', { id: order.id, order_no: order.order_no })
            return {
              ...order,
              refund_amount: 0, // 初始化退款金额为0
              selected: false   // 初始化选择状态
            }
          })
          
          // 如果有默认订单ID，自动选中该订单
          if (defaultOrderId) {
            console.log('Available order IDs:', this.availableOrders.map(o => o.id))
            console.log('Available order structure sample:', this.availableOrders[0])
            
            // 尝试多种ID字段匹配
            let defaultOrder = this.availableOrders.find(order => order.id == defaultOrderId)
            if (!defaultOrder) {
              defaultOrder = this.availableOrders.find(order => order.order_id == defaultOrderId)
            }
            if (!defaultOrder) {
              defaultOrder = this.availableOrders.find(order => order.oid == defaultOrderId)
            }
            
            if (defaultOrder) {
              // 使用找到的订单的实际ID作为选中值
              const actualOrderId = defaultOrder.id || defaultOrder.order_id || defaultOrder.oid
              this.selectedOrders = [actualOrderId]
              // 默认将实付金额设置为退款金额
              defaultOrder.refund_amount = parseFloat(defaultOrder.money || 0)
              console.log('Default order found and selected:', defaultOrder)
              console.log('Using order ID:', actualOrderId)
            } else {
              console.warn('Default order not found in available orders:', defaultOrderId)
              console.warn('Available orders:', this.availableOrders.map(o => ({ 
                id: o.id, 
                order_id: o.order_id,
                oid: o.oid,
                order_no: o.order_no 
              })))
            }
          }
          
          console.log('Available orders set:', this.availableOrders)
          console.log('Selected orders:', this.selectedOrders)
        } else {
          console.error('API error:', res.message)
          this.$message.error(res.message || '获取订单列表失败')
        }
      } catch (error) {
        console.error('获取可退款订单失败:', error)
        this.$message.error('获取订单列表失败，请重试')
      } finally {
        this.loading = false
        console.log('Loading finished, modal visible:', this.visible)
      }
    },
    
    // 关闭弹窗
    handleCancel() {
      this.visible = false
      this.resetForm()
    },
    
    // 重置表单
    resetForm() {
      this.isEditMode = false
      this.isResubmitMode = false
      this.editId = null
      this.originalApplicationId = null
      this.customerId = null
      this.storeId = null
      this.originalAttachments = [] // 清空原有附件数据
      this.customerInfo = {
        name: '',
        phone: '',
        storeName: ''
      }
      this.availableOrders = []
      this.selectedOrders = []
      // 老师列表由 commone-self-principal 组件管理
      this.teacherDefaultRendering = {} // 重置老师回显数据
      this.form = {
        refund_reason: '',
        bank_account_type: undefined,
        bank_account: '',
        bank_account_name: '',
        bank_name: '',
        bank_location: '',
        bank_branch: '',
        attachment_urls: [],
        service_teacher_id: undefined // 重置服务老师
      }
      this.$nextTick(() => {
        this.$refs.refundForm && this.$refs.refundForm.clearValidate()
      })
    },
    
    // 设置老师回显数据的通用方法
    async setTeacherDefaultData(teacherId, teacherName) {
      if (!teacherId || !teacherName) {
        return
      }
      
      try {
        // 构造老师数据
        const teacherData = {
          id: parseInt(teacherId),
          username: teacherName
        }
        
        // 设置defaultRendering
        this.teacherDefaultRendering = teacherData
        
        // 等待DOM更新
        await this.$nextTick()
        
        // 设置service_teacher_id
        this.form.service_teacher_id = parseInt(teacherId)
        
        // 如果组件存在，直接操作其calldata确保数据正确
        if (this.$refs.teacherSelector) {
          // 设置组件内部值
          this.$refs.teacherSelector.componentValue = parseInt(teacherId)
          
          // 检查calldata中是否已存在该老师
          const existingIndex = this.$refs.teacherSelector.calldata.findIndex(
            item => item.id == teacherId
          )
          
          if (existingIndex > -1) {
            // 如果存在，更新该项的数据确保字段正确
            this.$refs.teacherSelector.calldata[existingIndex] = {
              ...this.$refs.teacherSelector.calldata[existingIndex],
              ...teacherData
            }
          } else {
            // 如果不存在，添加到calldata
            this.$refs.teacherSelector.calldata.push(teacherData)
          }
          
          // 强制触发组件更新
          this.$refs.teacherSelector.$forceUpdate()
        }
        
      } catch (error) {
        console.error('设置老师数据时出错:', error)
      }
    },
    
    
    // 表格中退款金额变化处理
    handleTableRefundAmountChange(orderId, amount) {
      const order = this.availableOrders.find(o => o.id === orderId)
      if (order) {
        if (amount < 0) {
          this.$message.warning('退款金额不能小于0')
          order.refund_amount = 0
        } else {
          order.refund_amount = amount || 0
        }
      }
    },
    
    // 从表格中移除订单
    handleRemoveOrderFromTable(orderId) {
      const index = this.selectedOrders.indexOf(orderId)
      if (index > -1) {
        this.selectedOrders.splice(index, 1)
        // 清空该订单的退款金额
        const order = this.availableOrders.find(o => o.id === orderId)
        if (order) {
          order.refund_amount = 0
        }
      }
    },
    
    // 添加订单到表格
    handleAddOrderToTable(orderId) {
      if (!this.selectedOrders.includes(orderId)) {
        this.selectedOrders.push(orderId)
        // 默认将实付金额设置为退款金额
        const order = this.availableOrders.find(o => o.id === orderId)
        if (order) {
          order.refund_amount = parseFloat(order.money || 0)
        }
      }
    },
    
    // 附件上传变化处理
    handleAttachmentChange(files) {
      // files是JImageUpload组件传回的文件数组，包含url和file信息
      // 需要保留原有图片的完整信息（如feishu_code），避免数据丢失
      
      if (!Array.isArray(files)) {
        this.form.attachment_urls = files
        return
      }
      
      const processedFiles = files.map(file => {
        // 如果file只包含url和file字段，尝试从原有附件中匹配完整数据
        if (file && typeof file === 'object' && file.url && Object.keys(file).length <= 2) {
          // 从原有附件中查找匹配的URL
          const originalFile = this.originalAttachments.find(original => {
            // 比较URL，支持不同的URL格式匹配
            const fileUrl = file.url
            const originalUrl = original.url || original
            
            // 如果是字符串，进行URL匹配
            if (typeof originalUrl === 'string') {
              // 直接相等
              if (fileUrl === originalUrl) return true
              
              // 提取文件路径部分进行比较（去掉域名前缀）
              const getPathFromUrl = (url) => {
                if (!url) return ''
                try {
                  // 如果包含http://或https://，提取路径部分
                  if (url.includes('://')) {
                    const urlObj = new URL(url)
                    return urlObj.pathname
                  }
                  // 如果以/开头，返回路径
                  if (url.startsWith('/')) {
                    return url
                  }
                  // 否则认为就是文件名或相对路径
                  return url
                } catch (e) {
                  // URL解析失败，返回原始URL
                  return url
                }
              }
              
              const filePath = getPathFromUrl(fileUrl)
              const originalPath = getPathFromUrl(originalUrl)
              
              // 比较路径
              if (filePath && originalPath && (filePath === originalPath || filePath.includes(originalPath) || originalPath.includes(filePath))) {
                return true
              }
            }
            
            return false
          })
          
          // 如果找到原有数据，合并保留完整信息
          if (originalFile) {
            return {
              ...originalFile, // 保留原有的所有字段（包括feishu_code等）
              file: file.file   // 更新file字段（如果有的话）
            }
          }
        }
        
        // 如果没有找到匹配的原有数据，或者本身就是完整数据，直接返回
        return file
      })
      
      this.form.attachment_urls = processedFiles
      
      // 触发表单验证
      this.$nextTick(() => {
        this.$refs.refundForm && this.$refs.refundForm.validateField('attachment_urls')
      })
    },
    
    // 提交申请
    handleSubmit(isDraft = false) {
      this.$refs.refundForm.validate(valid => {
        if (!valid) {
          return false
        }
        
        // 检查是否选择了订单
        if (this.selectedOrders.length === 0) {
          this.$message.error('请至少选择一个订单')
          return false
        }
        
        // 检查每个选中订单的退款金额
        const invalidOrders = this.selectedOrderDetails.filter(order => 
          !order.refund_amount || order.refund_amount <= 0
        )
        if (invalidOrders.length > 0) {
          const orderNos = invalidOrders.map(o => o.order_no).join('、')
          this.$message.error(`请为订单 ${orderNos} 填写有效的退款金额`)
          return false
        }
        
        // 检查总退款金额
        if (this.totalRefundAmount <= 0) {
          this.$message.error('总退款金额必须大于0')
          return false
        }
        

        
        // 设置对应的loading状态
        if (isDraft) {
          this.savingDraft = true
        } else {
          this.submitting = true
        }
        
        // 构建订单明细数据
        const orderDetails = this.selectedOrderDetails.map((order) => ({
          order_id: order.id,
          order_no: order.order_no,
          consume_time: order.plan_time,
          plan_time: order.plan_time, // 添加到店时间
          service_name: order.service_name, // 添加服务名称
          order_amount: parseFloat(order.money),
          refund_amount: parseFloat(order.refund_amount) // 使用每个订单单独的退款金额
        }))
        
        // 处理附件URLs - 转换为JSON字符串
        // const attachmentUrls = this.form.attachment_urls && this.form.attachment_urls.length > 0 
        //   ? JSON.stringify(this.form.attachment_urls.map(item => item.url || item)) 
        //   : ''
        const attachmentUrls = this.form.attachment_urls && this.form.attachment_urls.length > 0 
          ? this.form.attachment_urls
          : []
        
        // 处理银行账户类型 - 转换为数字
        const bankAccountType = this.form.bank_account_type === '个人' ? 1 : 2
        
        // 处理银行账户信息 - 组合为JSON字符串
        const bankAccountInfo = JSON.stringify({
          account_no: this.form.bank_account,
          account_name: this.form.bank_account_name,
          bank_name: this.form.bank_name,
          bank_branch: this.form.bank_branch || '',
          bank_location: this.form.bank_location || ''
        })
        
        // 构建提交数据 - 按照后端接口字段要求
        const firstOrder = this.selectedOrderDetails[0]
        const submitData = {
          cus_id: this.customerId || firstOrder.cus_id,
          cus_name: this.customerInfo.name || firstOrder.cus_name,
          cus_phone: this.customerInfo.phone || firstOrder.cus_mobile,
          store_id: this.storeId || firstOrder.store_id,
          store_name: this.customerInfo.storeName || firstOrder.store_name,
          refund_reason: this.form.refund_reason,
          attachment_urls: attachmentUrls,
          bank_account_type: bankAccountType,
          bank_account_info: bankAccountInfo,
          order_details: orderDetails,
          teacher_id: this.form.service_teacher_id, // 传递服务老师ID给后端
          is_draft: isDraft ? 1 : 0  // 添加草稿标记：1表示保存为草稿，0表示正式提交
        }
        
        // 调用API提交 - 区分新增、编辑和重新发起
        let apiCall
        if (this.isEditMode) {
          // 编辑模式：使用update接口
          apiCall = refundApplicationApi.update({...submitData, id: this.editId})
        } else {
          // 创建模式或重新发起模式：都使用create接口
          apiCall = refundApplicationApi.create(submitData)
        }
        
        apiCall
          .then(res => {
            if (res.code === 200) {
              // 根据操作类型显示不同的成功消息
              let successMessage
              if (this.isResubmitMode) {
                successMessage = isDraft ? '退款申请已保存为草稿' : '重新发起退款申请成功'
              } else if (this.isEditMode) {
                successMessage = isDraft ? '退款申请已保存为草稿' : '退款申请编辑成功'
              } else {
                successMessage = isDraft ? '退款申请已保存为草稿' : '退款申请提交成功'
              }
              this.$message.success(successMessage)
              this.handleCancel()
              this.$emit('success')
            } else {
              this.$message.error(res.message || '操作失败')
            }
          })
          .catch(error => {
            console.error('提交退款申请失败:', error)
            let errorMessage
            if (this.isResubmitMode) {
              errorMessage = isDraft ? '保存草稿失败，请重试' : '重新发起失败，请重试'
            } else if (this.isEditMode) {
              errorMessage = isDraft ? '保存草稿失败，请重试' : '编辑失败，请重试'
            } else {
              errorMessage = isDraft ? '保存草稿失败，请重试' : '提交失败，请重试'
            }
            this.$message.error(errorMessage)
          })
          .finally(() => {
            // 重置对应的loading状态
            if (isDraft) {
              this.savingDraft = false
            } else {
              this.submitting = false
            }
          })
      })
    },
    
    // 处理服务老师选择变化
    handleTeacherChange(teacherId) {
      // 手动清除服务老师字段的验证错误
      this.$nextTick(() => {
        if (this.$refs.refundForm) {
          this.$refs.refundForm.clearValidate('service_teacher_id')
        }
      })
    },
    
    // 服务老师列表获取方法
    planTeacherList(params = {}) {
      // 确保传递当前门店ID
      const requestParams = {
        ...params,
        store_id: this.storeId || this.$store.getters.store.id
      };
      
      const promise = storeManage.planTeacherList(requestParams);
      
      // 在数据加载完成后，如果有待设置的老师数据，重新处理
      promise.then(() => {
        this.$nextTick(() => {
          if (this.teacherDefaultRendering && this.teacherDefaultRendering.id) {
            this.ensureTeacherDataCorrect(this.teacherDefaultRendering.id, this.teacherDefaultRendering.username);
          }
        });
      }).catch(() => {
        // 忽略错误，不影响主流程
      });
      
      return promise;
    },
    
    // 确保老师数据正确的辅助方法
    ensureTeacherDataCorrect(teacherId, teacherName) {
      if (!this.$refs.teacherSelector || !teacherId || !teacherName) {
        return;
      }
      
      const teacherData = {
        id: parseInt(teacherId),
        username: teacherName
      };
      
      // 检查calldata中是否已存在该老师
      const existingIndex = this.$refs.teacherSelector.calldata.findIndex(
        item => item.id == teacherId
      );
      
      if (existingIndex > -1) {
        // 如果存在，更新该项的数据确保字段正确
        this.$refs.teacherSelector.calldata[existingIndex] = {
          ...this.$refs.teacherSelector.calldata[existingIndex],
          ...teacherData
        };
        
        // 强制触发组件更新
        this.$refs.teacherSelector.$forceUpdate();
      }
    },
  }
}
</script>

<style lang="less" scoped>
.refund-application-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .ant-modal-body {
    background-color: #f5f5f5;
    border-radius: 10px;
    padding: 20px;
  }
}

.refund-modal-content {
  padding: 0 32px;
  
  .main-content {
    display: flex;
    min-height: 600px;
    position: relative;
    
    .left-section {
      flex: 1;
      margin-right: 432px; // 400px宽度 + 32px间距
      border: 1px solid #e8e8e8;
      box-shadow: 0px 4px 12px 0px rgba(177, 181, 208, 0.08);
      border-radius: 6px;
      overflow: hidden;
      
      .modal-subtitle {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 24px;
        padding-bottom: 12px;
        background-image: linear-gradient(
          to right,
          #9373ee 0%,
          #9373ee 50px,
          transparent 50px
        );
        background-position: bottom;
        background-size: 100% 3px;
        background-repeat: no-repeat;
      }
      
      .info-group {
        background: #fff;
        
        .info-display-item {
          margin-bottom: 16px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            
            &.amount-text {
              font-weight: 700;
              color: #f5222d;
              font-size: 18px;
              background: linear-gradient(45deg, #f5222d, #ff7875);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }
          }
        }
      }
      
              .refund-form {
          padding: 24px;
          background: #fff;
          
          .refund-amount-item {
            margin-bottom: 16px;
          
          .refund-amount-display {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .amount-value {
              font-size: 16px;
              font-weight: 400;
              color: #666;
              
              &.refund-amount {
                background: linear-gradient(45deg, #f5222d, #ff7875);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
              }
            }
            
            .amount-tip {
              font-size: 14px;
              color: #666;
              background: #f6f8fa;
              padding: 4px 8px;
              border-radius: 4px;
              border-left: 3px solid #9373ee;
            }
          }
          
          .validation-status {
            margin-top: 12px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
            
            &.valid {
              background: #f6ffed;
              border: 1px solid #b7eb8f;
              color: #52c41a;
              
              .anticon {
                color: #52c41a;
              }
            }
            
            &.invalid {
              background: #fff2f0;
              border: 1px solid #ffccc7;
              color: #ff4d4f;
              
              .anticon {
                color: #ff4d4f;
              }
            }
          }
        }
        
        /deep/ .ant-form-item-label {
          font-weight: 400;
          color: #666;
          
          > label {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            
            &::after {
              content: ':';
              margin-left: 4px;
            }
          }
        }
        
        // 必填字段红色星号样式
        /deep/ .ant-form-item-required .ant-form-item-label > label::before {
          content: '*';
          color: #ff4d4f;
          font-size: 16px;
          margin-right: 4px;
        }
        
        // 银行信息区域子项必填字段样式 - 移除红色星号
        .bank-info-section .bank-sub-item {
          /deep/ .ant-form-item-label > label::before {
            content: '' !important;
            display: none !important;
          }
        }
        
        // 更强的选择器确保移除银行信息子项的星号
        .bank-info-section .bank-info-content .bank-sub-item.ant-form-item-required {
          /deep/ .ant-form-item-label > label::before {
            content: '' !important;
            display: none !important;
          }
        }
        
        // 统一所有表单项的对齐方式
        /deep/ .ant-form-item {
          margin-bottom: 16px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          // 统一label的对齐方式
          .ant-form-item-label {
            text-align: right;
            padding-right: 16px;
          }
        }
        
        // 信息展示项样式
        .info-display-item {
          /deep/ .ant-form-item-label {
            > label {
              color: #666;
              font-weight: 400;
            }
          }
          
          /deep/ .ant-form-item-control {
            .info-value {
              color: #333;
              font-weight: 400;
            }
          }
        }
        
        // 多行label样式
        .multiline-label-item {
          /deep/ .ant-form-item-label {
            > label {
              white-space: normal;
              line-height: 1.4;
              word-break: break-all;
              max-width: 100px;
              display: inline-block;
              font-size: 14px;
              font-weight: 400;
              
              &::after {
                content: ':';
                margin-left: 4px;
              }
            }
          }
        }
        
        /deep/ .ant-input,
        /deep/ .ant-select-selector {
          border-radius: 8px;
          border: 1px solid #d9d9d9;
          transition: all 0.3s;
          
          &:hover {
            border-color: #9373ee;
          }
        }
        
        /deep/ .ant-input:focus,
        /deep/ .ant-select-focused .ant-select-selector {
          border-color: #9373ee;
          box-shadow: 0 0 8px rgba(147, 115, 238, 0.2);
        }
        
        .upload-area {
          .upload-component {
            /deep/ .ant-upload-list {
              .ant-upload-list-item {
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }
            }
            
            .upload-btn {
              width: 100px;
              height: 100px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              border: 2px dashed #d9d9d9;
              border-radius: 10px;
              cursor: pointer;
              transition: all 0.3s;
              background: linear-gradient(135deg, #fafbff 0%, #f9f8ff 100%);
              
              &:hover {
                border-color: #9373ee;
                background: linear-gradient(135deg, #f4f1ff 0%, #ede6ff 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(147, 115, 238, 0.2);
              }
              
              .anticon {
                font-size: 18px;
                color: #9373ee;
                margin-bottom: 8px;
              }
              
              .ant-upload-text {
                font-size: 12px;
                color: #666;
                font-weight: 500;
              }
            }
          }
          
          .upload-tip {
            font-size: 13px;
            color: #999;
            line-height: 1.5;
            padding: 8px 12px;
            background: #f6f8fa;
            border-radius: 6px;
          }
        }
      }
      
      .order-table-section {
        padding: 24px;
        background: #fff;
        
        .table-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 16px;
          position: relative;
          padding-left: 12px;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background: linear-gradient(45deg, #9373ee, #b794f6);
            border-radius: 2px;
          }
        }
        
        .order-table {
          /deep/ .ant-table {
            border-radius: 8px 8px 0 0;
            overflow: hidden;
            border: 1px solid #f0f0f0;
          }
          
          // 修复小尺寸表格的内容区域边距问题
          /deep/ .ant-table-small > .ant-table-content > .ant-table-body {
            margin: 0;
          }
          
          /deep/ .ant-table-thead > tr > th {
            background: #f8f9fa;
            border-bottom: 2px solid #e8e8e8;
            font-weight: 600;
            color: #333;
            padding: 12px 16px;
            
            &:first-child {
              border-left: none;
            }
            
            &:last-child {
              border-right: none;
            }
          }
          
          /deep/ .ant-table-container {
            border-left: none;
            border-right: none;
          }
          
          /deep/ .ant-table-tbody > tr {
            transition: all 0.3s;
            
            &:hover {
              background: #f6f8fa;
            }
          }
          
          /deep/ .ant-table-tbody > tr > td {
            border-bottom: 1px solid #f0f0f0;
            padding: 12px 16px;
            
            &:first-child {
              border-left: none;
            }
            
            &:last-child {
              border-right: none;
            }
          }
          
          /deep/ .ant-table-tbody > tr:last-child > td {
            border-bottom: none;
          }
          
          /deep/ .ant-input-number {
            border-radius: 6px;
            
            &:focus {
              border-color: #9373ee;
              box-shadow: 0 0 6px rgba(147, 115, 238, 0.2);
            }
          }
        }
        
        .order-table-summary {
          margin-top: 0;
          border: 1px solid #f0f0f0;
          border-top: none;
          border-radius: 0 0 8px 8px;
          overflow: hidden;
          
          .summary-row {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
            font-weight: 600;
            border-top: 2px solid #e8e8e8;
            
            .summary-label {
              width: 150px; /* 对应订单编号列宽度 */
              padding-left: 0;
              font-size: 14px;
              color: #333;
            }
            
            .summary-amount {
              width: 100px; /* 对应实付金额列宽度 */
              text-align: left;
              font-size: 14px;
              color: #f5222d;
              font-weight: 700;
            }
            
            .summary-refund {
              width: 140px; /* 对应退款金额列宽度 */
              text-align: left;
              font-size: 14px;
              color: #52c41a;
              font-weight: 700;
            }
            
            .summary-action {
              width: 80px; /* 对应操作列宽度 */
            }
          }
        }
      }
      
      .bank-info-section {
        margin-top: 0;
        padding: 0;
        background: transparent;
        
        /deep/ .ant-form-item-label {
          > label {
            position: relative;
            
            &::before {
              content: "*";
              color: #f5222d;
              position: absolute;
              left: -14px;
              top: 50%;
              transform: translateY(-50%);
              font-size: 14px;
            }
          }
        }
        
        .bank-info-content {
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e8e8e8;
          
            .bank-sub-item {
              margin-bottom: 16px;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              /deep/ .ant-form-item-label {
                // 使用与主表单相同的宽度比例，保持对齐
                width: 20.833333% !important; // 相当于 span: 5 的宽度
                text-align: right;
                padding-right: 16px; // 与主表单保持一致的右侧间距
                
                > label {
                  font-size: 14px;
                  color: #666;
                  font-weight: 400;
                  
                  &::before {
                    content: '' !important;
                    display: none !important;
                  }
                  
                  &::after {
                    content: ':' !important;
                    margin-left: 4px;
                    display: inline !important;
                  }
                }
              }
              
              /deep/ .ant-form-item-control {
                // 使用与主表单相同的宽度比例和间距
                padding-left: 16px; // 与主表单保持一致的左侧间距
              }
            }
        }
      }
    }
    
    .right-section {
      position: absolute;
      top: 0;
      right: 0;
      width: 400px;
      height: 100%;
      display: flex;
      flex-direction: column;
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        flex-shrink: 0;
        
          .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
            padding-bottom: 12px;
            position: relative;
            
          }
        
      }
      
      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
      }
      
      .order-cards {
        flex: 1;
        min-height: 0;
        max-height: calc(100% - 60px);
        
        .order-spin-container {
          height: 100%;
          
          /deep/ .ant-spin-container {
            height: 100%;
            overflow-y: auto;
            padding-right: 8px;
          }
        }
        
        .order-content-wrapper {
          min-height: 100%;
        }
        .order-card {
          margin-bottom: 20px;
          padding: 20px;
          padding-bottom: 45px; 
          border: 1px solid #e8e8e8;
          border-radius: 6px;
          background: #fff;
          position: relative;
          
          .order-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
            
            .store-name {
              font-size: 15px;
              font-weight: 400;
              color: #333;
              flex: 1;
            }
            
            .order-no {
              font-size: 13px;
              color: #666;
              font-weight: 500;
              padding: 4px 8px;
              background: #f5f5f5;
              border-radius: 4px;
            }
          }
          
          .order-content {
            .service-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              
              /deep/ .ant-tag {
                border-radius: 4px;
                font-weight: 500;
                margin: 0;
              }
              
              .service-tag {
                max-width: 240px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                vertical-align: top;
              }
              
              .price {
                font-size: 14px;
                font-weight: 600;
                color: #f5222d;
              }
            }
            
            .order-date {
              font-size: 12px;
              color: #999;
              background: #f9f9f9;
              padding: 6px 10px;
              border-radius: 4px;
              margin-bottom: 12px; /* 确保与底部按钮有距离 */
            }
          }
          
          .add-order-action {
            position: absolute;
            bottom: 16px;
            right: 16px;
            
            .add-btn {
              background: #9373ee;
              border: 1px solid #9373ee;
              border-radius: 6px;
              font-weight: 500;
              color: #fff;
              font-size: 14px;
              transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
              
              &:hover {
                background: #b794f6;
                border-color: #b794f6;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(147, 115, 238, 0.3);
              }
              
              &:active {
                background: #7c3aed;
                border-color: #7c3aed;
                transform: translateY(0);
              }
            }
            
            .added-btn {
              background: #f5f5f5;
              border: 1px solid #d9d9d9;
              color: #999;
              border-radius: 4px;
              cursor: not-allowed;
              
              &:hover {
                background: #f5f5f5 !important;
                border-color: #d9d9d9 !important;
              }
            }
          }
        }
      }
    }
  }
  
  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    background-image: linear-gradient(
      to right,
      #f0f0f0 0%,
      #f0f0f0 50%,
      transparent 50%
    );
    background-position: top;
    background-size: 12px 1px;
    background-repeat: repeat-x;
    text-align: center;
    
    .ant-btn {
      min-width: 100px;
      height: 40px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      
      &:not(.ant-btn-primary) {
        // 取消按钮样式 - 参考稍后结单
        background: #fff;
        border: 1px solid #d9d9d9;
        color: rgba(0, 0, 0, 0.65);
        
        &:hover {
          background: #fff;
          border-color: #b794f6;
          color: #b794f6;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2);
        }
        
        &:active {
          background: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.65);
          transform: translateY(0);
        }
      }
      
      &.ant-btn-primary {
        // 保存按钮样式 - 参考立即结单
        background: #9373ee;
        border-color: #9373ee;
        color: #fff;
        
        &:hover {
          background: #b794f6;
          border-color: #b794f6;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(147, 115, 238, 0.3);
        }
        
        &:active {
          background: #7c3aed;
          border-color: #7c3aed;
          transform: translateY(0);
        }
        
        &:disabled {
          background: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          transform: none;
          box-shadow: none;
        }
      }
    }
  }
}

// 图片预览框样式调整
/deep/ .ant-image-preview {
  .ant-image-preview-img {
    max-width: 95vw !important;
    max-height: 95vh !important;
  }
}

// JImageUpload组件图片预览优化
/deep/ .ant-image-preview-wrap {
  .ant-image-preview-img {
    max-width: 95vw !important;
    max-height: 95vh !important;
    object-fit: contain;
  }
  
  .ant-image-preview-img-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 图片预览模态框大小调整
/deep/ .ant-image-preview-root {
  .ant-image-preview-mask {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }
}

// 优化预览操作栏
/deep/ .ant-image-preview-operations {
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: 8px !important;
  padding: 8px 16px !important;
}

// 针对当前页面的 ant-row 样式优化
.refund-application-modal /deep/ .ant-row {
  margin-bottom: 10px !important;
}
.info-group /deep/ .ant-row {
  margin-bottom: 0px !important;
}

// 右侧订单卡片区域滚动条样式
.right-section .order-cards .ant-spin-container {
  // 滚动条整体样式  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  // 滚动条轨道
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  // 滚动条滑块
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    transition: background 0.3s;
    
    &:hover {
      background: #9373ee;
    }
  }
}

// 服务老师选择区域样式
.refund-application-modal {
  .teacher-select-wrapper {
    .teacher-option {
      display: flex;
      align-items: center;
      
      .teacher-name {
        font-weight: 500;
        color: #333;
      }
      
      .teacher-number {
        color: #666;
        font-size: 13px;
        margin-left: 4px;
      }
    }
  }
}
</style>