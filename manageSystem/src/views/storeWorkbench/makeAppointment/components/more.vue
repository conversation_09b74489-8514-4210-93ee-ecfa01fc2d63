<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2023-04-10 15:21:13
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-04-27 16:36:15
 * @FilePath: \manageSystem\src\views\storeWorkbench\makeAppointment\components\more.vue
 * @Description: 
-->
<template>
  <div class="moreBox">
    <self-dropdown>
      <div slot="title">
        <a-icon type="ellipsis" class="mr10 ml10 pointer ellipsis" />
      </div>
      <a
        v-has="'customer-introduced:store-bind'"
        href="javascript:;"
        @click="
          () => {
            propvisible = true;
          }
        "
      >
        绑定老带新
      </a>
      <a
        v-has="'order-order-header:store-cancel'"
        v-if="
          [0, 1, 4].some((l) => l == data.order_status) &&
          data.received_amount == 0
        "
        href="javascript:;"
        @click="order_cancel"
      >
        取消订单
      </a>
      <a
        v-has="'order-order-header:store-retract'"
        v-if="data.order_status === 3 || data.order_status === 4"
        href="javascript:;"
        @click="order_retract"
          >
            撤回已预约
      </a>
      <a
        v-if="[3, 4, 5, 6, 8].includes(data.order_status)"
        href="javascript:;"
        @click="showFeedback"
        v-has="'customer-feedback:qrcode'"
      >
        客资反馈
      </a>
    </self-dropdown>
    <self-oldGuestWithNew
      v-model="propvisible"
      :order_type_id="order_type_id"
      :data="data"
      @loadData="$emit('loadData')"
    />
  </div>
</template>
    <script>
import { customerIntroduced, cardsSales, storeManage } from "@/api/api";
export default {
  name: "more",
  props: {
    data: {
      type: Object,
      default: () => new Object(),
    },
    order_type_id: {
      type: [String, Number],
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      confirmLoading: false,
      propvisible: false,
      countdown: 0,
      mobile_code: "86",
      phoneRule: {
        86: "mobile",
        852: "hongKongMobilePhoneNumber",
        853: "macaoMobilePhoneNumber",
        886: "taiwansMobilePhoneNumber",
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 6 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 18 },
          sm: { span: 18 },
        },
      },
    };
  },
  computed: {
    validatorRules: function () {
      return {
        friend_mobile: {
          rules: [
            {
              required: true,
              validator: this.$validate[this.phoneRule[this.mobile_code]],
            },
          ],
        },
        responsible_id: {
          rules: [{ required: true, message: "请选择负责人" }],
        },
        code: {
          rules: [{ required: false, message: "请输入验证码" }],
        },
      };
    },
  },
  methods: {
    showFeedback() {
      this.$emit('showFeedback', this.data);
    },
    order_cancel() {
      this.$confirm({
        title: "提示",
        content: "是否取消当前订单",
        onOk: () => {
          return new Promise((resolve) => {
            storeManage
              .storeCancel({ id: this.data.id })
              .then((res) => {
                if (res.code == "200") {
                  this.$message.success(res.message);
                  this.$emit("loadData");
                }
              })
              .finally(() => resolve());
          });
        },
      });
    },
    order_retract() {
      this.$confirm({
        title: "提示",
        content: "订单是否确定撤回已预约",
        onOk: () => {
          return new Promise((resolve) => {
            storeManage
              .storeRetract({ id: this.data.id })
              .then((res) => {
                if (res.code == "200") {
                  this.$message.success(res.message);
                  this.$emit("loadData");
                }
              })
              .finally(() => resolve());
          });
        },
      });
    },
    sendSms() {
      this.countdown = 60;
      this.countdownFun();
    },
    countdownFun() {
      let time = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else clearInterval(time);
      }, 1000);
    },
    confirmPromise() {
      this.form.validateFields((err, value) => {
        console.log(err);
        if (!err) {
          value = Object.assign(value, {
            referrer_id: this.data.cus_id,
            code: "000000",
          });
          this.confirmLoading = true;
          customerIntroduced
            .storeBind(value)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.propvisible = false;
              }
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        }
      });
    },
  },
};
</script>
    
    <style scoped lang="less">
.moreBox {
  display: inline-flex;
  align-items: center;
  .ellipsis {
    font-size: 16px;
    font-weight: 700;
    position: relative;
    top: 6px;
    font-weight: bolder;
    color: #7a7979;
    background: #f7f8fc;
    padding: 0 5px;
    border-radius: 3px;
    margin: 0 !important;
    right: 0;
  }
}

.modalBox {
  h2 {
    font-weight: bold;
    text-align: center;
  }
  .userItem {
    display: flex;
    align-items: center;
    .userBox {
      width: 100%;
      background: @bg-color-page;
      padding: 5px 12px;
      display: flex;
      align-items: center;
      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 10px;
      }
      > div {
        line-height: normal;
      }
    }
  }
}
</style>