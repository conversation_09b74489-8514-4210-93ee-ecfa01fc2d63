<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2022-09-12 11:20:39
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-05-04 16:24:46
 * @FilePath: \manageSystem\src\views\storeWorkbench\makeAppointment\makeAppointment.vue
 * @Description: src/views/storeWorkbench/makeAppointment/makeAppointment.vue
-->
<template>
  <div class="pageBox">
    <pageTitle title="预约到店">
      <template slot="left">
        <div class="left shadow">
          <a-date-picker @change="pickerChange" :value="date"> </a-date-picker>
          <a-icon type="redo" class="refresh" @click="refresh" :class="{
            refreshLoading: refreshLoading,
          }" />
        </div>
      </template>
      <template slot="right">
        <div class="right" slot="right">
          <a-badge v-has="'order-order-header:store-settlement-count'" :count="totalPendingCount" :offset="[-22, 5]" class="pending-order-badge">
            <a-button type="primary" icon="solution" class="shadow qrcode-btn" @click="showPendingSettlementModal">待结算订单</a-button>
          </a-badge>
          <a-button v-has="'feishu:adjust-store'" type="primary" icon="qrcode" class="shadow qrcode-btn" @click="showQrcode">扫码调店</a-button>
          <a-radio-group button-style="outline" class="outline shadow" v-model="queryParam.order_status"
            @change="handleChange">
            <a-radio-button value="-1">
              全部({{ total.all_num || 0 }})
            </a-radio-button>
            <a-radio-button value="1">
              未到店({{ total.plan_num || 0 }})
            </a-radio-button>
            <a-radio-button value="4">
              未结算({{ total.settlement_num || 0 }})
            </a-radio-button>
            <a-radio-button value="5">
              已完成({{ total.complete_num || 0 }})
            </a-radio-button>
          </a-radio-group>
          <a-input-search placeholder="请输入客户姓名，手机号后四位进行搜索" enter-button v-model="queryParam.search_customer"
            @search="onSearch" @keydown.enter.native="onSearch" class="search shadow" />
        </div>
      </template>
      <!-- <div class="titleBox"></div> -->
    </pageTitle>
    <div class="list">
      <a-spin class="spin" v-if="loading"></a-spin>
      <div v-else>
        <div v-if="
          (dataSourceFormat.length > 0 &&
            dataSourceFormatComplete.length > 0) ||
          (dataSourceFormat.length > 0 &&
            dataSourceFormatComplete.length == 0)
        ">
          <div class="listBox" v-if="dataSourceFormat.length > 0">
            <memberCard v-for="(item, index) in dataSourceFormat" :key="'item' + index" :status="item.order_status"
              :item="item" class="ant-col-xs-24 ant-col-sm-24 ant-col-md-24 ant-col-lg-12 ant-col-xl-8 ant-col-xxl-6"
              :order_type_id="order_type_id" :actions="{
                details: details,
                collect: collect,
                appointment: appointment,
                settlement: settlement,
                arrived: arrived,
                billAgain: billAgain,
              }" @loadData="loadData" @showFeedback="showFeedback" />
          </div>
          <a-empty class="empty" v-else />
        </div>
        <template v-if="dataSourceFormatComplete.length > 0">
          <a-divider v-if="
            dataSourceFormat.length > 0 && dataSourceFormatComplete.length > 0
          ">已完成订单</a-divider>
          <div class="listBox">
            <memberCard v-for="(item, index) in dataSourceFormatComplete" :key="'item' + index"
              :status="item.order_status" :item="item"
              class="ant-col-xs-24 ant-col-sm-24 ant-col-md-12 ant-col-lg-12 ant-col-xl-8 ant-col-xxl-6"
              :order_type_id="order_type_id" :actions="{
                details: details,
                collect: collect,
                appointment: appointment,
                settlement: settlement,
                arrived: arrived,
                billAgain: billAgain,
              }" @loadData="loadData" @showFeedback="showFeedback" />
          </div>
        </template>
      </div>
    </div>
    <collect ref="collect" />
    <orderDetail ref="orderDetail" />
    <reservation v-model="propvisible" :id="planId" @loadData="loadData"/>
    
    <pending-settlement-modal
      v-if="pendingSettlementModalVisible"
      :visible="pendingSettlementModalVisible"
      :store-id="$store.getters.store.id"
      :settlement-action="settlement"
      @close="closePendingSettlementModal"
    />

    <a-modal
      title="扫码调店"
      :visible="qrcodeVisible"
      @cancel="closeQrcode"
      :footer="null"
      centered
      width="400px"
    >
      <div class="qrcode-container">
        <div v-if="qrCodeProcessing" class="processing-indicator">
          <a-spin size="large" tip="正在处理，请稍候..." />
        </div>

        <div v-else-if="showWeComQrCode" class="wecom-qrcode-display">
          <QrcodeVue :value="weComAuthUrl" :size="300" level="H" />
          <p class="qrcode-tip">请使用【<span class="qrcode-tip-child">企业微信</span>】扫描二维码授权</p>
          <p class="countdown-tip">将在 {{ countdownSeconds }} 秒后自动关闭</p>
        </div>

        <div v-else-if="qrCodeResult && !showWeComQrCode" class="result-display">
          <div v-if="qrCodeResult.errCode === 0">
            <a-icon type="check-circle" :style="{ color: '#52c41a', fontSize: '48px', marginBottom: '15px' }" />
            <p :style="{ color: '#52c41a' }">调店成功</p>
          </div>
          <div v-else-if="qrCodeResult.errCode === 1">
            <a-icon type="close-circle" :style="{ color: '#f5222d', fontSize: '48px', marginBottom: '15px' }" />
            <p :style="{ color: '#f5222d' }">调店失败</p>
            <p :style="{ fontSize: '14px' }">{{ qrCodeResult.errMsg }}</p>
          </div>
          <div v-else-if="qrCodeResult.errCode === 2">
            <a-button type="primary" @click="handleWeComAuth">企微授权</a-button>
            <p style="margin-top: 15px; color: #666;">需要进行企业微信授权才能完成操作</p>
          </div>
        </div>

        <div v-else-if="!showWeComQrCode && !qrCodeResult">
          <div id="feishuQrcode" ref="feishuQrcode" class="qrcode-box"></div>
          <p class="qrcode-tip">请使用【<span class="qrcode-tip-child">飞书APP</span>】扫描二维码</p>
        </div>
      </div>
    </a-modal>

    <a-modal
      title="客资反馈"
      :visible="feedbackVisible"
      @cancel="closeFeedback"
      :footer="null"
      centered
      width="400px"
    >
      <div class="qrcode-container">
        <QrcodeVue :value="feedbackUrl" :size="300" level="H" />
        <p class="qrcode-tip">请使用【<span class="qrcode-tip-child">飞书APP</span>】扫描二维码</p>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import memberCard from "./memberCard.vue";
import collect from "../components/collect.vue";
import pageTitle from "../components/pageTitle.vue";
import orderDetail from "./components/orderDetail.vue";
import getDefaulTime from "./getDefaulTime.js";
import moment from "moment";
import reservation from './reservation.vue'
import axios from 'axios';
import api from "@/api/index";
import { customerService, cardsSales } from "@/api/api"; 
import QrcodeVue from "qrcode.vue";
import PendingSettlementModal from "./components/PendingSettlementModal.vue";
import { getAction } from '@/api/manage';

export default {
  name: "makeAppointment",
  mixins: [JeecgListMixin],
  components: { memberCard, collect, pageTitle, orderDetail, reservation, QrcodeVue, PendingSettlementModal },
  data() {
    return {
      propvisible: false,
      planId: void 0,
      columns: [],
      selfDefaultTime: {},
      queryParam: {
        plan_start_time: "",
        plan_end_time: "",
        order_status: "-1",
        store_id: this.$store.getters.store.id,
      },
      mode: "today",
      date: "",
      refreshLoading: false,
      disableMixinCreated: true,
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 1000, // 不需要分页
        showSizeChanger: false,
        showTotal: function (total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        },
      },
      url: {
        list: "/order/order-header/store-list",
      },
      qrcodeVisible: false,
      feishuConfig: {
        appid: "",
      },
      feedbackVisible: false,
      feedbackUrl: "",
      ageBracketOptions: [],
      qrcodeEventListener: null,
      qrCodeProcessing: false,
      qrCodeResult: null,
      showWeComQrCode: false,
      weComAuthUrl: "",
      countdownTimer: null,
      countdownSeconds: 60,
      pendingSettlementModalVisible: false,
      pendingOrdersCountTotal: 0,
      pendingTransactionsCountTotal: 0,
      pendingCountInterval: null,
    };
  },
  computed: {
    total: function () {
      let d = Object.assign({}, this.dataSource.order);
      return d;
    },
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      //   d = d.map((l) => {
      //     l.order_status = 5;
      //     return l;
      //   });
      return d;
    },
    dataSourceFormatComplete: function () {
      let d = Object.assign([], this.dataSource.complete_list);
      return d;
    },
    totalPendingCount() {
      return Number(this.pendingOrdersCountTotal) + Number(this.pendingTransactionsCountTotal);
    },
  },
  mounted() {
    this.pickerChange(moment());
    this.setQueryTime();
    
    // 使用 sessionStorage 检查权限
    const hasPermission = this.hasPermission('order-order-header:store-settlement-count');
    console.log('mounted阶段 sessionStorage权限检查结果:', hasPermission);
    
    if (hasPermission) {
      this.fetchAllPendingCounts();
      this.pendingCountInterval = setInterval(this.fetchAllPendingCounts, 60000);
    }
    this.loadAgeBracketOptions();
  },
  beforeDestroy() {
    if (this.pendingCountInterval) {
      clearInterval(this.pendingCountInterval);
    }
  },
  methods: {
    // 检查当前用户是否有指定权限
    hasPermission(permission) {
      try {
        const authList = JSON.parse(sessionStorage.getItem('LOGIN_USER_BUTTON_AUTH') || "[]");

        // 过滤出类型不为 '2' 的权限（显示权限，非禁用权限）
        const permissionList = authList.filter(auth => auth.type !== '2');

        const permissions = permissionList.map(item => item.action);

        return permissions.includes(permission);
      } catch (error) {
        console.error('从sessionStorage检查权限时出错:', error);
        return false; 
      }
    },
    pickerChange(e) {
      let start = " 00:00:00";
      let end = " 23:59:59";
      console.log(e);
      this.date = e.format("YYYY-MM-DD");
      this.queryParam.plan_start_time = moment(this.date + start).unix();
      this.queryParam.plan_end_time = moment(this.date + end).unix();
      this.loadData();
    },
    setQueryTime() {
      getDefaulTime[this.mode].bind(Object.assign(this, getDefaulTime))();
    },
    details({ id }) {
      this.$refs.orderDetail.getView(id);
    },
    collect(e) {
      this.$refs.collect.open(e);
    },
    settlement(record) {
      this.$router.push({
        name: "开单收银",
        params: {
          settlementType: "settlementOrder",
          record,
        },
      });
    },
    arrived({ id }) {
      this.propvisible = true;
      this.planId = id;

    },
    //  ***** 已完成 ****
    appointment(record) {
      this.$router.push({
        name: "新增预约",
        params: {
          record,
          type: 2,
        },
      });
    },
    billAgain(record) {
      this.$router.push({
        name: "开单收银",
        params: {
          settlementType: "newOrder",
          record,
        },
      });
    },
    handleChange() {
      this.$nextTick(() => {
        this.loadData();
      });
    },
    async refresh() {
      this.refreshLoading = true;
      await this.loadData();
      setTimeout(() => {
        this.refreshLoading = false;
      }, 500); // 让动画至少转一圈
    },
    onSearch() {
      this.loadData();
    },
    showFeedback(item) {
      this.feedbackUrl = this.generateFeedbackUrl(item);
      this.feedbackVisible = true;
    },
    generateFeedbackUrl(item) {
      const baseUrl = 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnjdvWVhs5rbVugyoTNporwc';
      
      // 使用标准的URL编码，避免双重编码导致的显示问题
      // 为了兼容不同飞书客户端版本，我们构建一个标准的URL
      const params = new URLSearchParams();
      
      // 添加客户姓名
      if (item.cus_name) {
        params.append('prefill_客户姓名', item.cus_name);
      }
      
      // 添加订单ID
      if (item.id) {
        params.append('prefill_订单ID', item.id.toString());
      }
      
      // 隐藏订单ID字段
      params.append('hide_订单ID', '1');
      
      // 添加年龄段预填充（如果存在且不是"其他"）
      if (item.cus_age_bracket && item.cus_age_bracket !== 0) {
        const ageText = this.getAgeBracketText(item.cus_age_bracket);
        if (ageText) {
          params.append('prefill_年龄段', ageText);
        }
      }
      
      return baseUrl + '?' + params.toString();
    },
    getAgeBracketText(bracket) {
      if (!this.ageBracketOptions || this.ageBracketOptions.length === 0) {
        // 如果选项未加载，使用默认映射
        const map = {
          1: '18岁以下',
          2: '18-19岁', 
          3: '20-23岁',
          4: '24-30岁',
          5: '31-35岁',
          6: '36-40岁',
          7: '41-45岁',
          8: '46-50岁',
          9: '51-55岁',
          10: '56-59岁',
          11: '60岁以上'
        };
        return map[bracket] || '';
      }
      
      // 使用从API获取的选项
      const option = this.ageBracketOptions.find(item => item.id === bracket);
      return option ? option.name : '';
    },
    async loadAgeBracketOptions() {
      try {
        const res = await cardsSales.getAgeBracket();
        if (res.code === 200 && res.data) {
          this.ageBracketOptions = res.data;
        }
      } catch (error) {
        console.error('获取年龄段选项失败:', error);
      }
    },
    closeFeedback() {
      this.feedbackVisible = false;
      this.feedbackUrl = "";
    },
    showQrcode() {
      this.qrCodeProcessing = false;
      this.qrCodeResult = null;
      this.showWeComQrCode = false;
      this.weComAuthUrl = "";
      this.clearFeishuListener();

      this.qrcodeVisible = true;
      this.$nextTick(() => {
        this.initFeishuQrcode();
      });
    },
    closeQrcode() {
      this.qrcodeVisible = false;
      this.qrCodeProcessing = false;
      this.qrCodeResult = null;
      this.showWeComQrCode = false;
      this.weComAuthUrl = "";
      this.clearFeishuListener();
      this.clearCountdownTimer();
    },
    clearFeishuListener() {
      if (this.qrcodeEventListener && typeof window.removeEventListener !== 'undefined') {
        window.removeEventListener('message', this.qrcodeEventListener);
        this.qrcodeEventListener = null;
        console.log("Feishu event listener removed.");
      }
      this.clearCountdownTimer();
    },
    clearCountdownTimer() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    },
    initFeishuQrcode() {
      this.clearFeishuListener();
      
      this.showWeComQrCode = false; 

      this.getFeishuAppId().then((success) => {
        if (success) {
          this.createFeishuQrcode();
        } else {
          this.$message.error("无法初始化扫码功能");
          this.closeQrcode(); 
        }
      });
    },
    async getFeishuAppId() {
      try {
        const baseUrl = window._CONFIG['domianURL'] || '';
        const relativePath = api.getFeishuAppid.startsWith('/') ? api.getFeishuAppid.substring(1) : api.getFeishuAppid;
        const fullUrl = `${baseUrl}/${relativePath}?appCode=chz`;

        console.log('Requesting Feishu App ID from:', fullUrl);
        const res = await axios.get(fullUrl);
        console.log('Feishu App ID Response:', res);

        if (res && res.status === 200 && res.data && res.data.code == 200) { 
          this.feishuConfig.appid = res.data.data; 
          console.log('Successfully fetched Feishu App ID:', this.feishuConfig.appid);
          return true;
        } else {
          console.error("获取飞书AppID接口返回错误或状态码非200", res);
          const errorMessage = (res && res.data && res.data.message) || '获取飞书配置失败 (axios)';
          this.$message.error(errorMessage); 
          return false;
        }
      } catch (error) {
        console.error("获取飞书AppID失败 (axios)", error);
        this.$message.error('请求飞书配置时发生网络错误 (axios)');
        return false;
      }
    },
    createFeishuQrcode() {
      if (!this.feishuConfig.appid) {
        this.$message.error("获取飞书配置失败");
        return;
      }
      if (!this.$store.getters.store.id) {
        this.$message.error("请选择门店");
        return;
      }
      
      if (this.$refs.feishuQrcode) {
        this.$refs.feishuQrcode.innerHTML = "";
      }
      
      const redirectUrl = encodeURIComponent(`${window._CONFIG['domianURL']}/api/feishu?store_id=${this.$store.getters.store.id}`);
      const gotoUrl = `https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=${
        this.feishuConfig.appid
      }&redirect_uri=${redirectUrl}&response_type=code&state=chz`;
      
      try {
        console.log("开始创建二维码");
        console.log("飞书AppID:", this.feishuConfig.appid);
        console.log("DOM元素存在:", !!this.$refs.feishuQrcode);
        console.log("飞书授权URL:", gotoUrl);
        
        if (typeof QRLogin !== 'function') {
          console.error("飞书SDK未加载，QRLogin不是函数");
          this.$message.error("二维码组件加载失败");
          return;
        }
        
        const QRLoginObj = QRLogin({
          id: "feishuQrcode",
          goto: gotoUrl,
          width: "300", 
          height: "300",
          style: "width:300;height:300;border:0"
        });
        
        console.log("QRLogin对象创建成功:", !!QRLoginObj);
        
        const handleMessage = function(event) {
          if (this.qrcodeEventListener !== handleMessage) {
             console.warn("Ignoring message for old listener.");
             return;
          }

          const origin = event.origin;
          
          // 处理从 iframe 返回的调店结果消息
          if (typeof event.data === 'object' && event.data.type === 'feishuAdjustStore') {
            console.log('收到调店结果:', event.data);
            
            this.qrCodeProcessing = false;
            this.qrCodeResult = event.data;
            this.showWeComQrCode = false;

            // 移除授权用的隐藏iframe
            const frameToRemove = document.getElementById('feishu-auth-frame');
            if (frameToRemove) {
              document.body.removeChild(frameToRemove);
            }

            return;
          }
          
          if (origin === 'https://passport.feishu.cn') {
            if (typeof event.data === 'string' && event.data !== '[tea-sdk]ready') {
              const loginTmpCode = event.data;
              console.log("收到飞书扫码临时码:", loginTmpCode);
              
              this.qrCodeProcessing = true;
              this.qrCodeResult = null;
              this.showWeComQrCode = false;
              
              // 创建隐藏的iframe来处理授权
              const iframeId = 'feishu-auth-frame';
              let iframe = document.getElementById(iframeId);
              
              if (!iframe) {
                iframe = document.createElement('iframe');
                iframe.id = iframeId;
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
              }
              
              const finalUrl = `${gotoUrl}&tmp_code=${loginTmpCode}`;
              iframe.src = finalUrl;
            } else if (event.data === '[tea-sdk]ready') {
              console.log('Feishu QR Code SDK is ready.');
            } else {
              console.warn('Received unexpected message from Feishu origin:', event.data);
            }
          }
        }.bind(this);
        
        this.qrcodeEventListener = handleMessage;
        if (typeof window.addEventListener !== 'undefined') {
          window.addEventListener('message', handleMessage, false);
        } else if (typeof window.attachEvent !== 'undefined') {
          window.attachEvent('onmessage', handleMessage);
        }
      } catch (error) {
        console.error("创建二维码失败", error);
        this.$message.error("创建二维码失败: " + error.message);
      }
    },
    handleWeComAuth() {
      console.log("触发企微授权");
      if (this.qrCodeResult && this.qrCodeResult.userId !== undefined) {
        this.fetchAndGenerateWeComQrCode(this.qrCodeResult.userId);
      } else {
        console.error("无法获取 errCode 来启动企微授权");
        this.$message.error("启动企业微信授权失败，缺少必要信息");
      }
    },
    async fetchAndGenerateWeComQrCode(userId) {
      this.qrCodeProcessing = true;
      this.qrCodeResult = null;
      this.showWeComQrCode = false;

      try {
        console.log("尝试从后端获取企微配置...");
        const configRes = await customerService.chooseWxcom({ keyword: 'chz', status: 1 });
        console.log("获取企微配置 API 响应:", configRes);

        let fetchedCorpId = null;
        if (configRes.code === 200 && configRes.data && configRes.data.length > 0) {
          fetchedCorpId = configRes.data[0].corp_id;
          console.log("成功获取到 corp_id:", fetchedCorpId);
        } else {
          console.error("获取企微 corp_id 失败或未找到记录:", configRes.message || "返回结果为空");
          this.$message.error("获取企业微信配置失败：" + (configRes.message || "未找到相关配置"));
          this.qrCodeProcessing = false;
          return;
        }

        let redirectHost = location.origin + "/mobile/#/QrCodeLogin";
        let stateJson = encodeURIComponent('{"code":"chz","store_id":"' + this.$store.getters.store.id + '","user_id":"' + userId + '"}');
        console.log("stateJson:", stateJson);
        console.log("wxcomRedirectHost:", redirectHost);
        let encodedRedirectUri = encodeURIComponent(redirectHost);

        this.weComAuthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${fetchedCorpId}&redirect_uri=${encodedRedirectUri}&response_type=code&scope=snsapi_base&state=${stateJson}#wechat_redirect`;
        console.log("生成的企微授权URL (使用动态配置):", this.weComAuthUrl);

        this.showWeComQrCode = true;
        this.qrCodeProcessing = false;

        this.countdownSeconds = 60;
        this.clearCountdownTimer();
        this.countdownTimer = setInterval(() => {
          if (this.countdownSeconds > 0) {
            this.countdownSeconds--;
          } else {
            console.log("企微二维码倒计时结束，自动关闭弹窗");
            this.closeQrcode();
          }
        }, 1000);

      } catch (error) {
        console.error("处理企微授权过程中出错:", error);
        this.$message.error("处理企业微信授权时出错");
        this.qrCodeProcessing = false;
      }
    },
    showPendingSettlementModal() {
      this.pendingSettlementModalVisible = true;
    },
    closePendingSettlementModal() {
      this.pendingSettlementModalVisible = false;
    },
    async fetchAllPendingCounts() {
      // 使用 sessionStorage 检查权限
      const hasPermission = this.hasPermission('order-order-header:store-settlement-count');
      console.log('fetchAllPendingCounts sessionStorage权限检查结果:', hasPermission);
      
      // 如果没有权限或没有选择门店，则不执行
      if (!hasPermission || !this.$store.getters.store.id) { 
        this.pendingOrdersCountTotal = 0;
        this.pendingTransactionsCountTotal = 0;
        return;
      }
      try {
        const params = {
          store_id: this.$store.getters.store.id,
          order_status: 4,
        };
        const res = await getAction("/order/order-header/store-settlement-count", params);
        if (res.code === 200 && res.data) {
          this.pendingOrdersCountTotal = res.data.totalOrdersCount || 0;
          this.pendingTransactionsCountTotal = res.data.totalTransactionsCount || 0;
        } else {
          this.pendingOrdersCountTotal = 0;
          this.pendingTransactionsCountTotal = 0;
          console.warn("获取待处理总数失败:", res.message);
        }
      } catch (error) {
        this.pendingOrdersCountTotal = 0;
        this.pendingTransactionsCountTotal = 0;
        console.error("请求待处理总数异常:", error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.pageBox {
  height: 100%;
  display: flex;
  flex-flow: column;

  .list {
    position: relative;
    flex: 1;
    height: 0;
    overflow-y: scroll;
  }
}

.listBox {
  //   padding: 0 20px;
  text-align: center;
  //   display: grid;
  //   grid-template-columns: repeat(1, 1fr);
  display: flex;
  flex-wrap: wrap;

  .supplement {
    width: 310px;
    margin: 0 20px 20px 0;
  }
}

.refresh {
  cursor: pointer;
  margin-left: 10px;
}

.refreshLoading {
  animation: rotate 1s linear infinite;
}

.left {
  flex: 1;
  //   background: #fff;
  border-radius: 12px;
  padding: 4px 12px;
  text-align: center;
}

.right {
  display: inline-flex;
  flex-wrap: wrap;

  .outline {
    margin-right: 20px;
    margin-bottom: 10px;

    /deep/ .ant-radio-button-wrapper-checked {
      border: none;
    }

    /deep/ .ant-radio-button-wrapper {
      border: none;

      &::before {
        background: none;
      }
    }

    .ant-radio-button-wrapper-checked:focus-within {
      box-shadow: none;
    }
  }

  .search {
    margin: 0 0 10px 0 !important;
    width: 320px;
    margin-left: 10px;

    /deep/ input {
      //   border: none !important;
    }
  }
}

.empty {
  margin: 20% auto 0;
}

.spin {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0);
  z-index: 9;
}

/deep/ .ant-divider-inner-text {
  color: @font-color-gray;
}

/deep/ .ant-select-selection {
  background: none;
  border: none;
  box-shadow: none;
}

/deep/ .ant-divider {
  padding: 20px 0;
}

@media screen and (max-width: 2500px) {
  .listBox {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media screen and (max-width: 2500px) {
  .listBox {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media screen and (max-width: 2000px) {
  .listBox {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 1400px) {
  .listBox {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 800px) {
  .listBox {
    grid-template-columns: repeat(1, 1fr);
  }
}

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
  }

  25% {
    -webkit-transform: rotate(90deg);
  }

  50% {
    -webkit-transform: rotate(180deg);
  }

  75% {
    -webkit-transform: rotate(270deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

.qrcode-btn {
  margin-right: 20px;
  margin-bottom: 10px;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.qrcode-box {
  width: 300px;
  height: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-tip {
  margin-top: 15px;
  color: #666;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.qrcode-tip-child {
  color: red;
}

.processing-indicator, .result-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.result-display p {
  font-size: 16px;
  margin-top: 10px;
}

.wecom-qrcode-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.countdown-tip {
  margin-top: 10px;
  color: #999;
  font-size: 12px;
}
</style>