<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2022-09-12 17:01:48
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-04-24 15:35:55
 * @FilePath: \manageSystem\src\views\storeWorkbench\makeAppointment\memberCard.vue
 * @Description: 
-->
<template>
  <div class="cardBox">
    <div>
      <div class="title">
        <img
          :src="item.cus_avatar"
          class="memberAvert"
          v-imgdef="$headPortrait"
        />
        <div class="titleDetail">
          <div class="name">
            <div>{{ item.cus_name }}</div>
            <template v-if="status">
              <tag
                :color="statusList[status].linearColor"
                class="employmentStatus"
              >
                {{ statusList[status].name }}
              </tag>
            </template>
          </div>
          <div class="phone">
            <div>
              {{ item.cus_mobile }}
            </div>
            <div class="isold" :style="{ color: item.cus_is_new  == 0 ? 'green' : '#408ff7'}">
              <img src="@/assets/images/storeWorkbench/user.png" alt="" />
              {{ item.cus_is_new ? "新客" : "老客" }}
            </div>
          </div>
        </div>
      </div>
      <div class="centent">
        <div class="cententCellBox cententCell">
          <p>
            <span class="projectName">预约项目：</span>
            <span class="projectCentent">
              <self-tooltip-adaptive :text="item.goods_name" />
            </span>
          </p>
        </div>
        <div class="cententCellBox cententCell">
          <p>
            <span class="projectName">预约时间：</span>
            <span class="projectCentent">{{ item.plan_date }}</span>
          </p>
        </div>
        <div class="cententCell cententCellBox">
          <p>
            <span class="projectName">预约老师：</span>
            <span class="projectCentent">{{
              item.plan_teacher_name || "-"
            }}</span>
          </p>
          <p>
            <span class="projectName">对接客服：</span>
            <span class="projectCentent">{{ item.realname }}</span>
          </p>
        </div>
        <div class="cententCellBox cententCell">
          <p>
            <span class="projectName">实收金额：</span>
            <span class="projectCentent"
              >¥{{ item.received_amount }}
              <a-tag color="blue" class="deposit"
                >订金: {{ item.deposit }}</a-tag
              ></span
            >
          </p>
        </div>
        <div class="cententCellBox cententCell">
          <p>
            <span class="projectName">预约备注：</span>
            <span class="projectCentent">
              <self-tooltip-adaptive :text="item.plan_remark" />
            </span>
          </p>
        </div>
      </div>
      <div class="footer">
        <div class="leftBox">
          <more
            @loadData="$emit('loadData')"
            @showFeedback="$emit('showFeedback', $event)"
            class="more"
            :data="item"
            :order_type_id="order_type_id"
          />
        </div>
        <div class="rightBox">
          <settlementButton
            @click.native="action('details')"
            v-has="'order-order-header:view'"
          >
            详情
          </settlementButton>
          <settlementButton
            @click.native="action('collect')"
            v-if="showBtn([3, 4]) && false"
            v-has="'order-pay:get-data-by-order-id'"
          >
            便捷收款
          </settlementButton>
          <settlementButton
            @click.native="action('appointment')"
            v-if="showBtn([5])"
            v-has="'order-order-header:other-create'"
          >
            新增预约
          </settlementButton>
          <settlementButton
            color="#F59A23"
            @click.native="action('settlement')"
            v-if="showBtn([3, 4])"
            v-has="'order-order-header:store-view-for-edit'"
          >
            结算
          </settlementButton>
          <settlementButton
            color="#9373ee"
            @click.native="action('arrived')"
            v-if="showBtn([1])"
            v-has="'order-order-header:update-order-status'"
          >
            已到店
          </settlementButton>
          <settlementButton
            color="#01CE8B"
            @click.native="action('billAgain')"
            v-if="showBtn([5])"
            v-has="'order-order-header:other-create'"
          >
            再次开单
          </settlementButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import settlementButton from "./components/button.vue";
import tag from "./components/tag.vue";
import more from "./components/more.vue";
export default {
  name: "card",
  components: { settlementButton, tag, more },
  props: {
    status: {
      type: [String, Number],
    },
    actions: {
      type: Object,
    },
    item: {
      type: Object,
    },
    order_type_id: {
      type: [String, Number],
    },
  },
  data() {
    return {
      explainLength: 35,
      //   0 待预约 、
      //   1 已预约 、
      //   2 已取消 、
      //   3 已到店 、
      //   4 待结算 、
      //   5 已完成 、
      //   6 第三方结算 、
      //   7 申请退订 、
      //   8 售后服务 、
      //   9 已放弃 、
      //   10 作废
    };
  },
  computed: {
    statusList: function () {
      let data = this.$store.getters.statusList;
      let obj = {};
      data.forEach((element) => {
        obj[element.id] = element;
      });
      return obj;
    },
  },
  methods: {
    showBtn(e, blo) {
      if (!blo) {
        return e.some((l) => l == this.status);
      } else {
        return (
          e.some((l) => l == this.status) && !this.item.is_payment_completion
        );
      }
    },
    action(e) {
      try {
        this.actions[e](this.item, e);
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.cardBox {
  padding: 10px 20px 10px 0;
  display: inline-flex;
  flex-flow: column;
  text-align: left;
  > div {
    border-radius: 12px;
    background: #fff;
    padding: 5px 30px;
    box-shadow: 0px 4px 12px 0px rgba(177, 181, 208, 0.16);
  }
  .title,
  .footer {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .leftBox {
      flex: 1;
      img {
        width: 16px;
        height: 16px;
        position: relative;
        top: -2px;
      }
    }
  }
  .title {
    height: 100px;
    background-image: linear-gradient(
      to right,
      @border-color 0%,
      @border-color 50%,
      transparent 50%
    );
    background-position: bottom;
    background-size: 12px 1px;
    background-repeat: repeat-x;
    .memberAvert {
      border-radius: 50%;
      width: 60px;
      height: 60px;
      margin-right: 12px;
    }
    .titleDetail {
      flex: 1;
      width: 0;
      .name {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #333;
        > div {
          font-size: 20px;
          font-weight: bolder;
        }
        position: relative;
        top: -2px;
      }
      .phone {
        font-weight: 500;
        font-size: 16px;
        position: relative;
        display: flex;
        justify-content: space-between;
        .more {
          position: absolute;
          right: 0;
          top: 0;
        }
        .isold {
          padding-top: 2px;
          padding-right: 2px;
          width: 100px;
          text-align: right;
          font-size: 12px;
          img {
            width: 13px;
            height: 13px;
            position: relative;
            top: -2px;
          }
        }
      }

      .employmentStatus {
        display: flex;
        align-items: center;
        font-size: 16px;
        justify-content: center;
        border-color: #333333;
        position: relative;
        top: -1px;
        transform: scale(0.7);
        transform-origin: right;
        margin: 0;
      }
      /deep/ .employmentStatus {
        .employmentStatus;
      }
    }
  }
  .centent {
    flex: 1;
    padding: 15px 0;
    font-size: 14px;
    min-height: 200px;
    .cententCell {
      display: flex;
    }
    .cententCellBox {
      display: flex;
      font-size: 14px;
      p {
        margin-bottom: 0px;
        display: flex;
        // align-items: center;
        width: 100%;
        line-height: 36px;
        &::after {
          display: inline-block;
          content: "";
          height: 12px;
          width: 2px;
          margin: 0 5px;
          background: @font-color-gray-light;
          position: relative;
          top: 12px;
        }
        &:last-child {
          &::after {
            display: none;
          }
        }
        .projectName {
          width: 75px;
          color: @font-color-gray;
        }
        .projectCentent {
          display: inline-block;
          width: 0;
          flex: 1;
          color: #333;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          > div {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .footer {
    // border-top: 1px @border-color dashed;
    // height: 60px;
    padding: 15px 0;
    background-image: linear-gradient(
      to right,
      @border-color 0%,
      @border-color 50%,
      transparent 50%
    );
    background-position: top;
    background-size: 12px 1px;
    background-repeat: repeat-x;
    display: flex;
    justify-content: end;
  }

  .deposit {
    margin-left: 5px;
    transform: scale(0.8);
    transform-origin: left;
    border: none;
  }
}
</style>