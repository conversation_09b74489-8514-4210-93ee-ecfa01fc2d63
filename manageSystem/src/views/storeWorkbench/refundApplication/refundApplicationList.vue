<template>
  <div>
    <a-card :bordered="false">
      <self-page-header @searchQuery="searchQuery" @searchReset="searchReset" v-has="'order-refund:index'">
        <!-- 查询区域 -->
        <template slot="content">
          <self-col label="提交时间">
            <self-time
              timeRange="day"
              v-model="selfDefaultTime.created"
              :reset="timeReset"
              :InitializationTime="false"
              :timeKey="{
                start: 'created_start_time',
                end: 'created_end_time',
              }"
            />
          </self-col>
          <self-col label="客户信息">
            <a-input
              placeholder="请输入客户姓名或电话"
              v-model="queryParam.search_customer"
              allowClear
            ></a-input>
          </self-col>
          <self-col label="服务老师">
            <commone-self-principal
              searchKey="username"
              :requestFun="planTeacherList"
              placeholder="请选择服务老师"
              value_key="username"
              id_key="id"
              v-model="queryParam.teacher_id"
              :isRequest="true"
              allow-clear
            />
          </self-col>
          <self-col label="状态">
            <a-select
              placeholder="请选择状态"
              v-model="queryParam.status"
              allowClear
            >
              <a-select-option
                :key="key"
                :value="key"
                v-for="(value, key) in statusOptions"
              >{{ value }}</a-select-option>
            </a-select>
          </self-col>
        </template>
        <!-- 导出 -->
        <template slot="export">
          <export-to-csv
            :dataFormat="dataFormat"
            :query="queryParam"
            fileName="退款申请明细导出"
            :limit="1000"
            :istransmit="true"
            @visitable="(e) => (SummaryShow = e)"
            @percent="(e) => (CitySummary = e)"
            :queryParam="queryParam"
            :CommentApi="refundApplicationApi.export"
            :header="header"
            v-has="'order-refund:export'"
          />
        </template>
      </self-page-header>

      <!-- 导出进度条 -->
      <div v-if="SummaryShow" class="progress-box">
        <a-progress :percent="CitySummary"></a-progress>
      </div>

      <!-- 列表表格 -->
      <a-table
        ref="table"
        bordered
        size="middle"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSourceFormat"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <!-- 客户信息展示 -->
        <template slot="customer_info" slot-scope="text, record">
          <div class="mb5">{{ record.cus_name || "-" }}</div>
          <div>{{ record.cus_phone || "-" }}</div>
        </template>

        <!-- 实付金额展示 -->
        <template slot="order_amount" slot-scope="text, record">
          <div>￥{{ record.total_order_amount || "0.00" }}</div>
        </template>

        <!-- 应退金额展示 -->
        <template slot="refund_amount" slot-scope="text, record">
          <div>￥{{ record.total_refund_amount || "0.00" }}</div>
        </template>

        <!-- 状态展示 -->
        <template slot="status_text" slot-scope="text, record">
          <a-tag :color="getStatusColor(record.status)">
            {{ record.status_text || "未知" }}
          </a-tag>
        </template>

        <!-- 操作按钮 -->
        <span slot="action" slot-scope="text, record" v-divider>
          <a @click="handleView(record)" v-has="'order-refund:view'">详情</a>
          <a
            v-if="record.status == '0'"
            @click="handleEdit(record)"
            v-has="'order-refund:update'"
          >
            编辑
          </a>
          <a
            v-if="record.status == '3' || record.status == '2'"
            @click="handleResubmit(record)"
            v-has="'order-refund:create'"
          >
            重新发起
          </a>
        </span>
      </a-table>

      <!-- 详情弹窗 -->
      <refund-detail-modal 
        ref="refundDetailModal"
      />

      <!-- 编辑/重新发起弹窗 -->
      <refund-application-modal 
        ref="refundApplicationModal"
        @success="handleModalSuccess"
      />
    </a-card>
  </div>
</template>

<script>
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import { refundApplicationApi, storeManage } from "@/api/api.js";
import RefundDetailModal from "../components/RefundDetailModal.vue";
import RefundApplicationModal from "../components/RefundApplicationModal.vue";

export default {
  name: "RefundApplicationList",
  mixins: [JeecgListMixin],
  components: { 
    RefundDetailModal,
    RefundApplicationModal
  },
  data() {
    return {
      refundApplicationApi,

      // 禁用JeecgListMixin的自动加载，避免与Tab切换时的手动加载冲突
      disableMixinCreated: true,
      loading: false,
      confirmLoading: false,
      render: true,

      // 导出进度条相关变量
      SummaryShow: false,
      CitySummary: 0,

      // 导出表头
      header: [
        "审批单号",
        "客户姓名",
        "客户电话",
        "门店名称",
        "提交人",
        "实付总额",
        "应退总额",
        "服务老师",
        "提交时间",
        "审批状态",
        "订单号",
        "订单金额",
        "退款金额",
        "套餐",
        "预约时间"
      ],
      
      // 状态选项
      statusOptions: {},
      
      // 状态颜色映射
      statusColorMap: {
        0: 'blue',    // DRAFT - 草稿
        1: 'blue',    // IN_REVIEW - 审核中
        2: 'red',     // NOT_PASS - 审核未通过
        3: 'gray',    // CANCEL - 审核已撤销
        5: 'green',   // COMPLETE - 审核通过
      },
      
      
      // 时间筛选
      selfDefaultTime: {
        created: {},
      },
      
      // 查询参数（必须在data中初始化）
      queryParam: {},
      
      // 表格列配置
      columns: [
        {
          title: "审批单号",
          dataIndex: "application_no",
          align: "center",
          width: 230,
        },
        {
          title: "客户",
          align: "center",
          scopedSlots: { customRender: "customer_info" },
        },
        {
          title: "门店名称",
          dataIndex: "store_name",
          align: "center",
        },
        {
          title: "提交人",
          dataIndex: "created_by_text",
          align: "center",
        },
        {
          title: "实付金额",
          align: "center",
          scopedSlots: { customRender: "order_amount" },
        },
        {
          title: "应退金额",
          align: "center",
          scopedSlots: { customRender: "refund_amount" },
        },
        {
          title: "服务老师",
          dataIndex: "teacher_name",
          align: "center",
        },
        {
          title: "提交时间",
          dataIndex: "created_at_text",
          align: "center",
        },
        {
          title: "状态",
          align: "center",
          scopedSlots: { customRender: "status_text" },
        },
        {
          title: "操作",
          align: "center",
          scopedSlots: { customRender: "action" },
        },
      ],
      
      // 分页配置
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        pageSizeOptions: ["20", "50", "100", "200"],
        showSizeChanger: true,
        change: () => {
          this.loadData();
        },
        showTotal: function (total, range) {
          let page = range[1] + "/页 共" + total + "条";
          return page;
        },
      },
      
      // API配置
      url: {
        list: "/order/refund-application/index",
      },
    };
  },
  
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
  },
  
  created() {
    // 初始化查询参数
    this.queryParam = {
      search_customer: "",
      teacher_id: undefined,  // 修复：使用 undefined 让 placeholder 显示
      status: undefined,      // 修复：使用 undefined 让 placeholder 显示
      store_id: this.$store.getters.store.id,  // 添加当前门店ID
    };
    // 获取状态选项
    this.loadStatusOptions();
  },
  
  mounted() {
    // 窗口大小变化时重新渲染表格
    window.addEventListener("resize", () => {
      this.$newDebounce(async () => {
        this.render = false;
        await this.$asyncNextTick();
        this.render = true;
      }, 100);
    });
  },
  
  destroyed() {
    window.removeEventListener("resize", () => {
      this.$newDebounce(async () => {
        this.render = false;
        await this.$asyncNextTick();
        this.render = true;
      }, 100);
    });
  },
  
  methods: {
    // 获取状态颜色
    getStatusColor(status) {
      return this.statusColorMap[status] || 'default';
    },
    
    // 加载状态选项
    loadStatusOptions() {
      refundApplicationApi.statusSelectList().then((res) => {
        if (res.code === 200) {
          // 将数组转换为对象格式，key为id，value为name
          const statusObj = {};
          if (Array.isArray(res.data)) {
            res.data.forEach(item => {
              statusObj[item.id] = item.name;
            });
          }
          this.statusOptions = statusObj;
        }
      });
    },
    
    // 查看详情
    handleView(record) {
      // 先显示页面loading
      this.loading = true;
      
      // 调用API获取详情数据
      refundApplicationApi.view({ id: record.id }).then((res) => {
        this.loading = false;
        if (res.code === 200) {
          // 数据加载完成后显示弹窗
          this.$refs.refundDetailModal.open(res.data.info || res.data);
        } else {
          this.$message.error(res.message || '获取详情失败');
        }
      }).catch((error) => {
        this.loading = false;
        console.error('获取退款申请详情失败:', error);
        this.$message.error('获取详情失败，请重试');
      });
    },
    
    // 编辑申请
    handleEdit(record) {
      this.$refs.refundApplicationModal.openEdit(record.id);
    },
    
    // 重新发起
    handleResubmit(record) {
      this.$refs.refundApplicationModal.openResubmit(record.id);
    },
    
    //导出-数据格式
    dataFormat({ list }) {
      console.log(list);
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];
        let key = [
          "application_no",
          "cus_name",
          "cus_phone",
          "store_name",
          "created_by_name",
          "total_order_amount",
          "total_refund_amount",
          "teacher_name",
          "created_at_text",
          "status_text",
          "order_no",
          "order_amount",
          "refund_amount",
          "service_name",
          "plan_time_text"
        ];
        b = this.$utils.fieldCompletion(key, b);
        let nb = this.$pick(b, ...key);
        arr.push(nb);
      }
      return arr;
    },
    
    // 模态框操作成功回调（编辑/重新发起）
    handleModalSuccess() {
      // 不在这里显示成功消息，因为RefundApplicationModal已经显示了
      this.loadData();
    },
    
    // 服务老师列表获取方法
    planTeacherList(params = {}) {
      // 确保传递当前门店ID
      const requestParams = {
        ...params,
        store_id: this.$store.getters.store.id
      };
      return storeManage.planTeacherList(requestParams);
    },
  },
};
</script>

<style lang="less" scoped>
.top-title {
  font-size: 24px;
  font-weight: bold;
}

.mb5 {
  margin-bottom: 5px;
}

.mt16 {
  margin-top: 16px;
}

// 导出进度条样式
.progress-box {
  margin: 0;
  padding: 5px 0 16px;
  border-radius: 4px;
}

// 服务老师选择控件样式
.teacher-option {
  display: flex;
  align-items: center;

  .teacher-name {
    font-weight: 500;
    color: #333;
  }

  .teacher-number {
    color: #666;
    font-size: 13px;
    margin-left: 4px;
  }
}
</style>