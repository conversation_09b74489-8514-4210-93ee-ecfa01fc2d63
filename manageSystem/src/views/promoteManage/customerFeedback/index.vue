<!--
 * @Author: <PERSON>
 * @Date: 2025-07-17
 * @Description: 客资反馈列表页
-->
<template>
  <div>
    <a-card>
      <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="提交时间">
          <self-time
            timeRange="monthToNow"
            v-model="selfDefaultTime.feedback"
            :reset="timeReset"
            :timeKey="{
              start: 'start_time',
              end: 'end_time',
            }"
          />
        </self-col>
        <self-col label="客户名称">
          <a-input
            placeholder="请输入客户昵称或备注"
            v-model="queryParam.cus_name"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="反馈门店">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="searchStoreByEntity"
            placeholder="请选择反馈门店"
            value_key="store_name"
            id_key="id"
            v-model="queryParam.store_id"
          />
        </self-col>
        <self-col label="订单编号">
          <a-input
            placeholder="请输入订单编号"
            v-model="queryParam.order_no"
            allowClear
          ></a-input>
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="客资反馈"
          btnName="导出"
          :limit="1000"
          :queryParam="queryParam"
          :CommentApi="customerFeedback.export"
          :header="header"
          v-has="'customer-feedback:export'"
        ></export-to-csv>
      </template>
    </self-page-header>
    
    <!-- 数据表格 -->
    <a-table
      ref="table"
      :pagination="ipagination"
      @change="handleTableChange"
      size="middle"
      :scroll="{ x: 2400 }"
      :dataSource="dataSourceFormat"
      :rowKey="(i, index) => index"
      :columns="columns"
      :loading="loading"
    >
      <!-- 客户信息 -->
      <template slot="customer_info" slot-scope="text, record, index">
        <user-detail
          :name="text ? (text.name || '-') : '-'"
          :remark="text ? (text.nick_name || '-') : '-'"
          :keyValue="index"
          :avatar="text ? text.avatar : ''"
          :mainBodyType="text ? text.type : ''"
          :explainLength="8"
        />
      </template>

      <!-- 反馈内容 -->
      <template slot="feedback" slot-scope="text, record">
        <self-tooltip :text="record.feedback || '-'" :explainLength="30" />
      </template>

      <!-- 图片展示 -->
      <template slot="images" slot-scope="text, record">
        <img-box 
          v-if="record.images_parsed && record.images_parsed.length > 0"
          :arr="getImageUrls(record.images_parsed)"
          class="img-box"
        />
        <span v-else>-</span>
      </template>

      <!-- 素材信息 -->
      <template slot="material_info" slot-scope="text, record">
        <div v-if="record.material_info && record.material_info.mid3">
          <!-- 有素材图片时，整个单元格内容都可以悬停展示图片 -->
          <a-popover 
            v-if="record.material_info.video_img" 
            placement="right" 
            trigger="hover"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
          >
            <template slot="content">
              <div class="material-thumbnail-container">
                <img 
                  :src="record.material_info.video_img" 
                  alt="素材图片" 
                  class="material-thumbnail"
                  @error="handleImageError"
                />
              </div>
            </template>
            <div>
              <div class="Id-lightGray">
                素材ID：{{ record.material_info.mid3 }}
              </div>
              <div class="Id-lightGray" v-if="record.material_info.csite_text">
                广告版位：
                <span 
                  :class="{ 'ad-position-special': record.material_info.csite_text === '通投广告位' }"
                >
                  {{ record.material_info.csite_text }}
                </span>
              </div>
            </div>
          </a-popover>
          <!-- 没有素材图片时的正常显示 -->
          <div v-else>
            <div class="Id-lightGray">
              素材ID：{{ record.material_info.mid3 }}
            </div>
            <div class="Id-lightGray" v-if="record.material_info.csite_text">
              广告版位：{{ record.material_info.csite_text }}
            </div>
          </div>
        </div>
        <span v-else>-</span>
      </template>

      <!-- 账户信息 -->
      <template slot="account_info" slot-scope="text, record">
        <div v-if="record.account_info && record.account_info.sub_advertiser_name">
          <div>{{ record.account_info.sub_advertiser_name }}</div>
          <div class="Id-lightGray" v-if="record.account_info.sub_advertiser_id">
            账户ID：{{ record.account_info.sub_advertiser_id }}
          </div>
          <div class="Id-lightGray" v-if="record.account_info.adid">
            计划ID：{{ record.account_info.adid }}
          </div>
        </div>
        <span v-else>-</span>
      </template>
    </a-table>
    </a-card>
  </div>
</template>

<script>
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import { inventoryManage, customerFeedback } from "@/api/api";
import columns from "./columns";
import imgBox from "@/components/imgBox/imgBox.vue";
import selfTooltip from "@/components/selfComponents/self-tooltip/tooltip.vue";

export default {
  name: "CustomerFeedbackIndex",
  mixins: [JeecgListMixin],
  components: {
    imgBox,
    selfTooltip,
  },
  data() {
    return {
      searchStoreByEntity: inventoryManage.searchStoreByEntity,
      customerFeedback: customerFeedback,
      queryParam: {},
      selfDefaultTime: {},
      columns: columns.customerFeedback,
      header: [
        "ID",
        "客户名称",
        "客户微信号",
        "反馈门店",
        "到店时间",
        "状态",
        "年龄段",
        "反馈内容",
        "图片数量",
        "归属推广",
        "加粉时间",
        "素材ID",
        "广告版位",
        "推广账户",
        "账户ID",
        "计划ID",
        "提交人",
        "提交时间"
      ],
      url: {
        list: "/customer/feedback/index",
      },
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function (total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        },
      },
    };
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
  },
  methods: {
    // 获取图片URL数组，适配imgBox组件
    getImageUrls(imagesParsed) {
      if (!imagesParsed || !Array.isArray(imagesParsed)) {
        return [];
      }
      return imagesParsed.map(image => image.url || '').filter(url => url);
    },
    
    // 处理图片加载错误
    handleImageError(event) {
      event.target.style.display = 'none';
      console.warn('素材图片加载失败');
    },
    
    //导出-数据格式
    dataFormat({ list }) {
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];
        
        // 处理客户信息
        const customerInfo = b.customer_info || {};
        b.customer_name = customerInfo.name || '-';
        b.customer_mobile = customerInfo.mobile || '-';
        
        // 处理素材信息
        const materialInfo = b.material_info || {};
        b.material_id = materialInfo.mid3 || '-';
        b.ad_position = materialInfo.csite_text || '-';
        
        // 处理账户信息
        const accountInfo = b.account_info || {};
        b.advertiser_name = accountInfo.sub_advertiser_name || '-';
        b.advertiser_id = accountInfo.sub_advertiser_id || '-';
        b.plan_id = accountInfo.adid || '-';
        
        // 处理图片数量
        b.image_count = (b.images_parsed && b.images_parsed.length) || 0;
        
        let key = [
          "id",
          "customer_name",
          "customer_mobile", 
          "store_name",
          "plan_time_text",
          "status_text",
          "age_bracket_text",
          "feedback",
          "image_count",
          "promoter_name",
          "add_time_text",
          "material_id",
          "ad_position",
          "advertiser_name",
          "advertiser_id",
          "plan_id",
          "created_by_text",
          "created_at_text",
        ];
        b = this.$utils.fieldCompletion(key, b);
        let nb = this.$pick(b, ...key);
        arr.push(nb);
      }
      return arr;
    },
  },
};
</script>

<style lang="less" scoped>
.Id-lightGray {
  color: #999;
  font-size: 12px;
  margin-top: 2px;
}

// 素材图片预览相关样式
.material-thumbnail-container {
  padding: 8px;
  max-width: 600px;
  max-height: 400px;
  
  .material-thumbnail {
    max-width: 100%;
    max-height: 360px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    object-fit: cover;
  }
}

.img-box {
  margin-top: 10px;

  /deep/ .ImgList .ImgListBlock {
    margin-bottom: 6px;
  }
}

// 通投广告位特殊样式
.ad-position-special {
  color: #ff4d4f !important;
}
</style>