export default {
  customerFeedback: [
    {
      title: "客户信息",
      align: "left",
      width: 120,
      dataIndex: "customer_info",
      scopedSlots: {
        customRender: "customer_info"
      }
    },
    {
      title: "反馈门店",
      align: "left",
      width: 100,
      dataIndex: "store_name"
    },
    {
      title: "到店时间",
      align: "left",
      width: 150,
      dataIndex: "plan_time_text"
    },
    {
      title: "状态",
      align: "left",
      width: 80,
      dataIndex: "status_text"
    },
    {
      title: "年龄段",
      align: "left",
      width: 80,
      dataIndex: "age_bracket_text"
    },
    {
      title: "反馈内容",
      align: "left",
      width: 200,
      dataIndex: "feedback",
      scopedSlots: {
        customRender: "feedback"
      }
    },
    {
      title: "图片",
      align: "left",
      width: 160,
      dataIndex: "images_parsed",
      scopedSlots: {
        customRender: "images"
      }
    },
    {
      title: "归属推广",
      align: "left",
      width: 100,
      dataIndex: "promoter_name"
    },
    {
      title: "加粉时间",
      align: "left",
      width: 150,
      dataIndex: "add_time_text"
    },
    {
      title: "素材信息",
      align: "left",
      width: 150,
      dataIndex: "material_info",
      scopedSlots: {
        customRender: "material_info"
      }
    },
    {
      title: "归属账户",
      align: "left",
      width: 180,
      dataIndex: "account_info",
      scopedSlots: {
        customRender: "account_info"
      }
    },
    {
      title: "提交人",
      align: "left",
      width: 100,
      dataIndex: "created_by_text",
    },
    {
      title: "提交时间",
      align: "left",
      width: 150,
      dataIndex: "created_at_text"
    }
  ]
};