<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 顶部按钮 -->
      <template slot="top">
        <self-col-btn>
          <self-ads-budget-modal />
        </self-col-btn>
      </template>
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="推广日期">
          <a-range-picker
            allowClear
            @change="dateChange"
            :disabledDate="disabledDate"
            format="YYYY-MM-DD"
            :value="defaultTime"
          >
            <span slot="suffixIcon">
              <svgIcon type="picker" />
            </span>
          </a-range-picker>
        </self-col>
        <self-col label="推广账户">
          <a-input type="text" placeholder="请输入账户名称/账户ID" v-model="queryParam.sub_advertiser_name" />
        </self-col>
        <self-col label="账户主体">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'mainBodyList','promoteMainBody')"
            placeholder="请选择主体"
            v-model="queryParam.main_body_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in mainBodyList"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="推广渠道">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'channelList')"
            placeholder="请选择渠道"
            v-model="queryParam.promote_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in channelList"
            >{{ item.name }}</a-select-option>
          </a-select>
        </self-col>
        <self-col label="负责人">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择负责人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.responsible_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
            >{{ item.username }}</a-select-option>
          </a-select>
        </self-col>
        <self-col label="定向">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="directionSelect"
            placeholder="请选择定向"
            value_key="name"
            v-model="queryParam.direction_id"
            :isRequest="true"
          />
        </self-col>
        <self-col label="所属部门">
          <a-tree-select
            show-search
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="dept_list"
            v-model="queryParam.dept_id"
            placeholder="请选择所属部门"
          ></a-tree-select>
        </self-col>
        <self-col label="推广项目">
          <a-tree-select
            show-search
            style="selectDep"
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="Project_tree"
            v-model="queryParam.currentKey"
            @change="treeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择项目"
          ></a-tree-select>
        </self-col>
        <self-col label="推广链路">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'linkList')"
            placeholder="请选择链路"
            v-model="queryParam.link_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in linkList"
            >{{ item.name }}</a-select-option>
          </a-select>
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          v-has="'analysis:cast-people-export'"
          :query="queryParam"
          class="mr10"
          fileName="投放人分析汇总列表"
          btnName="汇总导出"
          :limit="1000"
          :total="ipagination.total"
          :queryParam="queryParam"
          :CommentApi="DataAnalysis.castPeopleExport"
          :dataFormat="dataFormat"
          :header="csvHeader"
          :csvKey="csvKey"
        ></export-to-csv>&nbsp;
        <export-to-csv
          v-has="'analysis:cast-people-details-export'"
          :query="queryParam"
          class="mr10"
          fileName="投放人分析明细列表"
          btnName="明细导出"
          :limit="1000"
          :total="ipagination.total"
          :queryParam="queryParam"
          :CommentApi="DataAnalysis.castPeopleDetailsExport"
          :dataFormat="dataFormat"
          :header="csvHeader"
          :csvKey="csvKey"
        ></export-to-csv>&nbsp;
        <export-to-csv
          v-has="'analysis:cast-people-details-export'"
          :query="queryParam"
          class="mr10"
          fileName="投放人分析每日数据列表"
          btnName="每日数据导出"
          :limit="1000"
          :total="ipagination.total"
          :queryParam="Object.assign({}, queryParam, {groupByDate: true})"
          :CommentApi="DataAnalysis.castPeopleDetailsExport"
          :dataFormat="dailyDataFormat"
          :header="dailyCsvHeader"
          :csvKey="dailyCsvKey"
        ></export-to-csv>
      </template>
    </self-page-header>
    <treeTable
      v-if="showTreeTable"
      :rowKey="(e, index) => index"
      @handleTableChange="handleTableChange"
      :getSonDataSource="getSonDataSource"
      :ipagination="ipagination"
      :defaultExpandedRowKeys="defaultExpandedRowKeys"
      :dataSource="dataSourceFormat"
      :loading="loading"
      :columns="columns"
      :keys="treeTableKey"
    ></treeTable>
  </a-card>
</template>

<script>
import treeTable from "./component/treeTable";
import { promoteManage, direction, DataAnalysis } from "@/api/api";
import SelfAdsBudgetModal from "@/components/selfComponents/self-ads-budget-modal/index.vue";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import JEllipsis from "@/components/jeecg/JEllipsis";
import JInput from "@/components/jeecg/JInput";
import moment from "moment";
import commentMixin from "@/mixins/commentMixin";
import { mapActions } from "vuex";
import columns from "./deliveryPersonColumns";
export default {
  //  投放人分析
  name: "deliveryPersonData",
  mixins: [JeecgListMixin, commentMixin],
  components: {
    JInput,
    JEllipsis,
    treeTable,
    SelfAdsBudgetModal
  },
  data() {
    return {
      directionSelect: direction.select,
      columns:columns.columns,
      csvHeader: columns.csvHeader,
      csvKey: columns.csvKey,
      // 每日数据导出配置
      dailyCsvHeader: [
        "日期",
        ...columns.csvHeader
      ],
      dailyCsvKey: [
        "date_format",
        ...columns.csvKey
      ],
      DataAnalysis,
      treeTableKey: null,
      sonDataSource: {},
      showtable: true,
      CitySummary: 0,
      defaultExpandedRowKeys: [],
      defaultValue: [],
      SummaryShow: false,
      CityDetails: 0,
      DetailsShow: false,
      promoteManage,
      timeShow: false,
      channelList: [],
      dept: [],
      projectList: [],
      linkList: [],
      provinceList: [],
      queryParam: {
        end_time: [],
        time: [],
        platform: "",
        project_id: "",
        // dept_id: "",
        promote_id: "",
        responsible_id: "",
        direction_id: "",
        link_id: ""
      },
      platformList: [],
      loading: true,
      timeDuring: {
        start: "",
        end: ""
      },
      dataDetailTotal: 0,
      defaultTime: [],
      personCharge: [],
      ipagination: {
        size: document.body.clientWidth <= 450 ? "small" : "",
        current: 1,
        total: 0,
        pageSize: 20,
        pageSizeOptions: ["10", "20", "30", "40", "50"],
        showSizeChanger: true,
        showTotal: function(total, range) {
          let page = 20 + "/页 共" + total + "条";
          return page;
        }
      },
      isorter: {
        column: "cost",
        order: "desc"
      },
      url: {
        list: "/promote/analysis/cast-people-analysis"
      }
    };
  },
  created() {
    this.Department();
    this.projectSelectTree();
    this.promotePerson();
    this.peopleTime();
    this.promoteMainBody();
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      this.channelList = this.dataSource.channelList; // 渠道列表
      this.dept = this.dataSource.deptList; // 部门列表
      this.linkList = this.dataSource.linkList; // 链路列表
      this.projectList = this.dataSource.projectList; // 项目列表
      d = d.map(item => {
        item.direction_name = "-";
        return item;
      });
      if (this.dataSource.start_time && this.dataSource.end_time) {
        let startTime = moment(
          Number(this.dataSource.start_time) * 1000
        ).format("YYYY-MM-DD");
        let endTime = moment(Number(this.dataSource.end_time) * 1000).format(
          "YYYY-MM-DD"
        );
        this.queryParam.start_time = startTime;
        this.queryParam.end_time = endTime;
        this.$nextTick(() => {
          this.defaultTime = [
            startTime ? moment(startTime, "YYYY-MM-DD") : null, 
            endTime ? moment(endTime, "YYYY-MM-DD") : null
          ].filter(Boolean); // 过滤掉null值
        });
      }
      d = this.$utils.TablefieldCompletion({ list: d });
      this.timeShow = false;
      this.$nextTick(() => {
        this.timeShow = true;
      });
      d = this.addKey(d, this.dataSource.is_promote_person);
      return d;
    },
    dept_list: function() {
      return this.$store.state.organizationalStructure.department;
    },
    expParams() {
      return Object.assign({ is_export: 1 }, this.queryParam);
    },
    Project_tree: function() {
      return this.$store.state.organizationalStructure.Project_tree;
    }
  },
  methods: {
    moment,
    ...mapActions("organizationalStructure", [
      "projectSelectTree",
      "Department"
    ]),
    treeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.project_type = selectedNode.type;
      this.queryParam.project_id = selectedNode.id;
    },
    //导出-城市分析数据格式
    dataFormat({ list, is_promote_person }) {
      try {
        list = this.addKey(list, is_promote_person);
        return list;
      } catch (err) {
        console.log("err");
      }
    },
    //导出-每日数据格式化
    dailyDataFormat({ list, is_promote_person }) {
      try {
        list = this.addKey(list, is_promote_person);
        // 确保日期字段正确格式化
        list.forEach(item => {
          if (item.date) {
            item.date_format = moment(item.date).format('YYYY-MM-DD');
          }
        });
        return list;
      } catch (err) {
        console.log("err");
      }
    },
    getSonDataSource({ item, index, page }) {
      const _this = this;
      return new Promise((resolve, reject) => {
        let data = {
          page: page,
          limit: 10,
          responsible_id: item.responsible_id,
          start_time: _this.queryParam.start_time,
          end_time: _this.queryParam.end_time
        };
        data = Object.assign(
          JSON.parse(JSON.stringify(_this.queryParam)),
          data
        );
        DataAnalysis.castPeopleDetails(data)
          .then(res => {
            if (res.code == 200) {
              let data = res.data.list;
              try {
                if (Array.isArray(data)) {
                  data = _this.addKey(data, res.data.is_promote_person);
                } else {
                  data = [];
                }
                // data = data.map((item) => {
                // item.deposit_rate = "-";
                // item.new_store_cus_count = "-";
                // item.store_cost = "-";
                // item.amount = "-";
                // item.real_store_cost = "-";
                // item.amount_cost_rate = "-";
                // return item;
                // });
                data = this.$utils.TablefieldCompletion({ list: data });
                resolve({
                  loading: false,
                  // data: res.data.list,
                  data: data,
                  total: res.data.totalCount
                });
              } catch (error) {
                console.log(error);
              }
            } else {
              reject([]);
            }
          })
          .catch(() => reject([]));
      });
    },
    //  table展开事件
    expandedRowsChange(e) {
      console.log(e);
    },
    //获取城市数据初始起止时间
    peopleTime() {
      let that = this;
      DataAnalysis.projectPublicTime().then(res => {
        if (res.code == 200) {
          // 确保获取到的日期格式正确
          that.timeDuring.start = res.data.start_time;
          that.timeDuring.end = res.data.end_time;
          
          // 如果已有起止时间，设置默认时间范围
          if (that.timeDuring.start && that.timeDuring.end) {
            that.defaultTime = [
              moment(that.timeDuring.start, "YYYY-MM-DD"),
              moment(that.timeDuring.end, "YYYY-MM-DD")
            ];
            that.queryParam.start_time = that.timeDuring.start;
            that.queryParam.end_time = that.timeDuring.end;
          }
          
          that.timeShow = true;
        } else {
          that.$message.warning(res.message);
        }
      });
    },
    provinceFilterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    cityFilterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    dateChange(date, dateString) {
      // 确保日期输入有效
      this.defaultTime = [];
      if (dateString[0] && dateString[1]) {
        this.defaultTime = [
          moment(dateString[0], "YYYY-MM-DD"),
          moment(dateString[1], "YYYY-MM-DD")
        ];
      }
      
      this.timeShow = false;
      this.$nextTick(() => {
        this.timeShow = true;
      });
      
      this.queryParam.start_time = dateString[0] || "";
      this.queryParam.end_time = dateString[1] || "";
    },
    async searchReset() {
      this.ipagination.current = 1;
      this.defaultTime = [];
      this.queryParam = {
        end_time: [],
        time: [],
        platform: "",
        project_id: "",
        // dept_id: "",
        promote_id: "",
        responsible_id: "",
        direction_id: "",
        link_id: ""
      };
      await this.loadData();
      this.releasTreeTable();
    },
    //时间格式
    dateParse(date) {
      let d = moment(new Date(parseInt(date))).format("YYYY-MM-DD HH:mm:ss");
      return d;
    },
    //设置可选日期
    disabledDate(current) {
      let that = this;
      let dayStartDistance, dayEndDistance;
      
      // 处理当前日期
      const today = new Date();
      
      // 处理开始和结束日期，使用更安全的方式创建Date对象
      const parseDate = (dateStr) => {
        if (!dateStr) return null;
        const parts = dateStr.split('-');
        if (parts.length !== 3) return null;
        // 使用年,月,日的方式创建日期对象，月份需要-1因为JS中月份从0开始
        return new Date(parseInt(parts[0]), parseInt(parts[1])-1, parseInt(parts[2]));
      };
      
      const startDateTime = parseDate(that.timeDuring.start);
      const endDateTime = parseDate(that.timeDuring.end);
      
      if (!startDateTime || !endDateTime) return false;
      
      // 计算日期差异，使用更可靠的方法
      dayStartDistance = Math.floor((today - startDateTime) / (1000 * 60 * 60 * 24));
      dayEndDistance = Math.floor((today - endDateTime) / (1000 * 60 * 60 * 24));
      
      return (
        current < moment().subtract(parseInt(dayStartDistance) + 1, "day") ||
        current > moment().subtract(parseInt(dayEndDistance), "day")
      );
    },
    handlePreview(r) {
      let { href } = this.$router.resolve({
        path: "/h5/preview",
        query: { id: r.id }
      });
      window.open(href, "_blank");
    },
    handleCancel() {
      this.modal.visible = false;
      this.modal.record = undefined;
      this.modal.cost_discount = 0;
    },
    getCurrentData() {
      var timeRange = "";
      var time = "";
      timeRange = new Date(new Date() - 1 * 60 * 60 * 1000);
      this.count = 1;
      timeRange = this.formateDate(timeRange);
      time = timeRange.split(":")[0] + ":00:00";
      return time;
    }
  }
};
</script>

<style></style>
