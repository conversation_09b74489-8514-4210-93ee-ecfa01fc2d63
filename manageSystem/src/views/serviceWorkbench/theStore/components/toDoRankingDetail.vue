<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2023-03-08 17:13:39
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-06-27 10:39:23
 * @FilePath: \manageSystem\src\views\serviceWorkbench\theStore\components\toDoRankingDetail.vue
 * @Description:  
-->
<template>
  <div>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="预约时间">
          <self-time
            timeRange="day"
            :reset="timeReset"
            :InitializationTime="true"
            :disabledDate="() => false"
            v-model="selfDefaultTime.promote"
            :timeKey="{
              start: 'start_time',
              end: 'end_time',
            }"
          />
        </self-col>
        <self-col label="门店名称">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="searchStoreByEntity"
            placeholder="请选择预约门店"
            value_key="store_name"
            :isRequest="false"
            :query="{
              is_show_warehouse: 0,
            }"
            v-model="queryParam.store_id"
          />
        </self-col>
        <self-col label="未做原因">
          <a-select v-model="queryParam.reason_status">
            <a-select-option value>全部</a-select-option>
            <a-select-option :key="s.id" :value="s.id" v-for="(s, i) in reasonStatusSelect">{{ s.name }}</a-select-option>
          </a-select>
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="到店未做明细表"
          :limit="5000"
          :queryParam="queryParam"
          :CommentApi="export_"
          :exportObject="exportObject"
          v-has="'customer-churn-remark:export'"
        ></export-to-csv>
      </template>
    </self-page-header>
    <template v-if="loading || dataSourceFormat.length == 0">
      <div class="theStoreItem">
        <div class="storeDetail">
          <span class="title">{{ "-" }}</span>
          <a @click="action(record)">到店未做</a>
        </div>
        <a-table
          rowKey="id"
          :columns="columns"
          :data-source="[]"
          :scroll="{ x: 1200 }"
          :loading="loading"
          @change="handleTableChange"
          :pagination="false"
        >
       
        </a-table>
      </div>
    </template>
    <template v-else>
      <div class="theStoreItem" v-for="(item, index) in dataSourceFormat" :key="index">
        <div class="storeDetail">
          <span class="title">{{ item.store_name || "-" }}</span>
          <span class="text">到店未做：{{ item.unfinished || 0 }}</span>
        </div>
        <a-table
          rowKey="id"
          :columns="columns"
          :data-source="item.details"
          :scroll="{ x: 1200 }"
          :loading="loading"
          @change="handleTableChange"
          :pagination="false"
        >
          <!-- <template slot="cus_name" v-if="recored" slot-scope="text, recored">
            <div class="mb5">{{ recored.cus_name || "-" }}</div>
            <div>
              {{ recored.cus_mobile }} -->
              <!-- <a
                v-has="'order-order-header:mobile-check'"
                @click="handleShow(recored)"
              >显示</a>-->
            <!-- </div>
          </template> -->

          <div slot="order_status" slot-scope="text, record">
            <a-tag
              :color="
                orderStatusList.find((l) => l.id == text)
                  ? orderStatusList.find((l) => l.id == text).theme
                  : ''
              "
            >
              {{
              orderStatusList.find((l) => l.id == text)
              ? orderStatusList.find((l) => l.id == text).name
              : ""
              }}
            </a-tag>
          </div>
          <div slot="is_new_customer" slot-scope="text, record">{{ text == 0 ? "老客" : "新客" }}</div>
          <div slot="action" slot-scope="text, record">
            <a
              @click="action(record)"
              v-if="record.reach_status == 2 && !(record.store_real_remark == '-' ? '': record.store_real_remark)"
              v-has="'customer-churn-remark:cancel'"
            >取消到店未做</a>
          </div>
          <self-tooltip slot="goods_name" slot-scope="text" :text="text" :explainLength="10" />
          <self-tooltip slot="store_remark" slot-scope="text" :text="text" :explainLength="20" />
          <self-tooltip slot="servicer_remark" slot-scope="text" :text="text" :explainLength="20" />
          <self-tooltip slot="edit_remark" slot-scope="text" :text="text" :explainLength="20" />
        </a-table>
      </div>
    </template>
    <self-mobile-look ref="self-mobile-look" />
    <self-detail-modal
      title="取消到店未做"
      placement="center"
      v-model="propvisible"
      :confirmLoading="confirmLoading"
      :drawerWidth="500"
      :drawerHeight="'auto'"
      :confirmPromise="confirmPromise"
    >
      <a-form :form="form">
        <a-form-item label="取消原因">
          <a-textarea
            placeholder="请输入取消原因"
            allow-clear
            :maxLength="200"
            v-decorator="['content',validatorRules.content]"
            :autosize="{
              minRows: 10,
              maxRows: 10,
            }"
          />
        </a-form-item>
      </a-form>
    </self-detail-modal>
  </div>
</template> 
  <script>
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import {
  storeManage,
  inventoryManage,
  customerChurnRemark
} from "@/api/api.js";
import moment from "moment";
export default {
  name: "toDoRankingDetail",
  mixins: [JeecgListMixin],
  data() {
    return {
      export_: customerChurnRemark.export,
      searchStoreByEntity: inventoryManage.storeSelect,
      reachStoreExport: storeManage.reachStoreExport,
      header: [
        "到店时间",
        "渠道",
        "客户姓名",
        "新老客",
        "预约项目",
        "预约备注",
        "下单客服",
        "预约客服",
        "到店状态"
      ],
      csvKey: [
        "first_store_time_text",
        "channel_name",
        "cus_name",
        "is_new_customer",
        "goods_name",
        "plan_remark",
        "created_by_text",
        "plan_name",
        "order_status"
      ],
      columns: this.$actionForAuth([
        {
          title: "订单ID",
          align: "left",
          width: 80,
          dataIndex: "order_id",
          scopedSlots: { customRender: "order_id" }
        },
        {
          title: "预约时间",
          width: 180,
          align: "left",
          dataIndex: "plan_time_text",
          scopedSlots: { customRender: "plan_time_text" }
        },
        {
          title: "客户信息",
          align: "left",
          width: 130,
          dataIndex: "cus_name",
          scopedSlots: { customRender: "cus_name" }
        },
        {
          title: "订单状态",
          align: "left",
          width: 100,
          dataIndex: "order_status",
          scopedSlots: { customRender: "order_status" }
        },
        {
          title: "预约项目", //
          align: "left",
          width: 200,
          dataIndex: "project_name",
          scopedSlots: { customRender: "project_name" }
        },
        {
          title: "责任老师",
          width: 120,
          align: "left",
          dataIndex: "plan_teacher_name_text"
        },
        {
          title: "标记流失客服",
          width: 120,
          align: "left",
          dataIndex: "created_by_text"
        },
        {
          title: "未做原因",
          width: 120,
          align: "left",
          dataIndex: "reason_status_text",
          scopedSlots: { customRender: "reason_status_text" }
        },
        {
          title: "门店反馈",
          align: "left",
          width: 265,
          dataIndex: "store_remark",
          scopedSlots: { customRender: "store_remark" }
        },
        {
          title: "客户反馈",
          align: "left",
          width: 265,
          dataIndex: "servicer_remark",
          scopedSlots: { customRender: "servicer_remark" }
        },
        {
          title: "取消原因",
          align: "left",
          width: 265,
          dataIndex: "edit_remark",
          scopedSlots: { customRender: "edit_remark" }
        },
        {
          title: "操作",
          align: "left",
          dataIndex: "action",
          width: 200,
          fixed: "right",
          scopedSlots: { customRender: "action" }
        }
      ]),
      orderStatusList: this.$store.state.storeCollection.statusList,
      statusList: [
        {
          id: 1,
          name: "已预约",
          status: "blue"
        },
        {
          id: 4,
          name: "已到店",
          status: "orange"
        },
        {
          id: 5,
          name: "已完成",
          status: "green"
        },
        {
          id: 6,
          name: "第三方结算",
          status: "green"
        },
        {
          id: 7,
          name: "申请退订",
          status: "red"
        },
        {
          id: 8,
          name: "售后服务",
          status: "red"
        }
      ],
      selfDefaultTime: {},
      queryParam: {},
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function(total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        }
      },
      url: {
        list: "/order/customer-churn-remark/index"
      },
      labelCol: {
        xs: { span: 22 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 15 }
      },
      id: 0,
      propvisible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      exportObject: [
        { header: "订单号", key: "order_no"},
        { header: "预约时间", key: "plan_time_text" },
        { header: "客户姓名", key: "cus_name" },
        // { header: "客户手机号", key: "cus_mobile" },
        { header: "责任老师", key: "plan_teacher_name_text" },
        { header: "标记流失客服", key: "created_by_text" },
        { header: "预约项目", key: "project_name" },
        { header: "订单状态", key: "order_status" },
        { header: "是否流失", key: "reach_status_text" },
        { header: "未做原因", key: "reason_status_text" },
        { header: "门店反馈", key: "store_remark" },
        { header: "客户反馈", key: "servicer_remark" },
        { header: "取消原因", key: "edit_remark" }
      ],
      reasonStatusSelect:[
        {
          id: 1,
          name: "担心效果"
        },
        {
          id: 2,
          name: "到店先了解"
        },
        {
          id: 3,
          name: "个人原因做不了"
        },
        {
          id: 4,
          name: "价格原因"
        },
        {
          id: 5,
          name: "门店原因"
        },
        {
          id: 6,
          name: "咨询不满意"
        }
      ]
    };
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      d = this.$utils.TablefieldCompletion({
        list: d,
        child: "details",
        format: "YYYY-MM-DD HH:mm:ss"
      });
      d.forEach(item => {
        item.phoneShow = true;
      });
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
    validatorRules: function() {
      return {
        content: {
          initialValue: '',
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (!value.trim()) {
                  cbfn("请输入内容");
                }
                cbfn();
              }
            }
          ]
        },
      };
    }
  },
  methods: {
    handleChange(e) {
      console.log(e);
    },
    //导出-数据格式
    dataFormat({ list }) {
      list = list.map(item => {
        item.order_status = this.orderStatusList.find(
          l => l.id == item.order_status
        )
          ? this.orderStatusList.find(l => l.id == item.order_status).name
          : "";
        item.is_new_customer = item.is_new_customer == 0 ? "否" : "是";
        return item;
      });
      return list;
    },
    handleShow(data) {
      if (!data.cus_id) return;
      this.$refs["self-mobile-look"].phoneLook(data);
    },
    action({ id }) {
      this.propvisible = true;
      this.id = id;
    },
    confirmPromise() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true;
          customerChurnRemark
            .cancelReachStoreRemark(
              Object.assign(values, {
                id: this.id
              })
            )
            .then(res => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.propvisible = false;
                this.loadData();
              }
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        }
      });
    }
  }
};
</script>
  
  <style lang="less" scoped>
.theStoreItem {
  margin-bottom: 70px;
  .storeDetail {
    height: 50px;
    background: #f7f8fc;
    position: relative;
    padding: 0 20px;
    align-items: left;
    display: flex;
    align-items: center;
    border-bottom: solid 1px @border-color;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-right: 25px;
    }
    .text {
      font-size: 14px;
      color: @primary-color;
      margin-right: 25px;
    }
    &::before {
      content: "";
      display: inline-block;
      width: 4px;
      background: @primary-color;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
    }
  }
}
</style>