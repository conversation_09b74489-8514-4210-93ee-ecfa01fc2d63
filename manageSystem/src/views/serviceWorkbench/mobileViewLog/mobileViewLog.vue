<!--
 * @Description: 手机号查看日志
-->
<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="查看时间">
          <self-time
            timeRange="day"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :timeKey="{
              start: 'start_time',
              end: 'end_time'
            }"
          />
        </self-col>
        <self-col label="查看人员">
          <a-input
            v-model="queryParam.username"
            placeholder="请输入查看人员姓名"
            style="width: 100%"
          />
        </self-col>
        <self-col label="订单编号">
          <a-input
            v-model="queryParam.order_no"
            placeholder="请输入订单编号"
            style="width: 100%"
          />
        </self-col>
        <self-col label="手机号">
          <a-input
            v-model="queryParam.mobile"
            placeholder="请输入手机号"
            style="width: 100%"
          />
        </self-col>
      </template>
    </self-page-header>
    <!-- table区域-begin -->
    <a-table
      class="totalTable"
      size="middle"
      :rowKey="record => record.created_at + '_' + record.mobile"
      :pagination="false"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :loading="loading"
      @change="onChange"
    >
    </a-table>
    <!-- table区域-end -->
    <self-pagination :ipagination="ipagination" @change="handleTableChange" />
  </a-card>
</template>

<script>
import { mobileViewLog } from "@/api/api";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import { columns } from "./columns";

export default {
  name: "mobileViewLog",
  mixins: [JeecgListMixin],
  components: {},
  data() {
    return {
      mobileViewLog,
      // 表头
      columns,
      selfDefaultTime: {
        promote: {}
      },
      // 查询参数 - 必须使用字符串类型以符合JeecgListMixin要求
      queryParam: {
        username: '',
        order_no: '',
        mobile: '',
        start_time: '',
        end_time: ''
      },
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function(total, range) {
          let page = `${range[1]}/页 共` + total + "条";
          return page;
        }
      },
      url: {
        list: "/customer/mobile-view-log/index"
      }
    };
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    }
  },
  methods: {
    onChange(pagination, filters, sorter) {
      // 排序
      if (sorter.order) {
        this.queryParam.order_field = sorter.field;
        this.queryParam.order_type =
          sorter.order === "descend" ? "desc" : "asc";
      } else {
        this.queryParam.order_field = "";
        this.queryParam.order_type = "";
      }
      this.ipagination.current = 1;
      //  end
      this.handleTableChange();
    }
  }
};
</script>
<style scoped lang="less">
/deep/
  .ant-table-thead
  > tr
  > th
  .ant-table-column-sorter
  .ant-table-column-sorter-inner {
  margin-left: 0.3em !important;
}

// 移除第一行的特殊样式
/deep/ .totalTable .ant-table-tbody .ant-table-row:first-child {
  color: inherit !important;
  background: inherit !important;
}
</style>